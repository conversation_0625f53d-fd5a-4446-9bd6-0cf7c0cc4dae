{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": []}, "formatter": {"enabled": true, "indentStyle": "space", "lineWidth": 120}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noNonNullAssertion": "off", "noParameterAssign": "off"}, "a11y": {"recommended": false}, "complexity": {"noForEach": "off", "noStaticOnlyClass": "off"}, "suspicious": {"noExplicitAny": "off", "noArrayIndexKey": "off"}, "nursery": {"useSortedClasses": {"level": "error", "fix": "safe", "options": {"attributes": ["className"], "functions": ["cn", "tw"]}}}, "security": {"noDangerouslySetInnerHtml": "off"}}}, "javascript": {"formatter": {"quoteStyle": "double"}}}