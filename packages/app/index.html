<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Tauri + React + Typescript</title>
    <style>
      html,
      body {
        background: transparent;
        overscroll-behavior: none;
        -webkit-overflow-scrolling: touch;
      }
      
      html {
        overflow: hidden;
      }
      
      body {
        overflow: hidden;
      }
      
      #root {
        height: 100vh;
        overflow: hidden;
      }
    </style>
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
