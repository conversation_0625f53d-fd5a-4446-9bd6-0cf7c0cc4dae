:root {
  --background: oklch(1.00 0 0);
  --foreground: oklch(0.14 0.00 285.86);
  --card: oklch(1.00 0 0);
  --card-foreground: oklch(0.14 0.00 285.86);
  --popover: oklch(1.00 0 0);
  --popover-foreground: oklch(0.14 0.00 285.86);
  --primary: oklch(0.21 0.01 285.93);
  --primary-foreground: oklch(0.99 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.21 0.01 285.93);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.55 0.02 285.93);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.21 0.01 285.93);
  --destructive: oklch(0.58 0.24 28.48);
  --border: oklch(0.92 0.00 286.61);
  --input: oklch(0.92 0.00 286.61);
  --ring: oklch(0.71 0.01 286.09);
  --chart-1: oklch(0.65 0.22 36.85);
  --chart-2: oklch(0.60 0.11 184.15);
  --chart-3: oklch(0.40 0.07 227.18);
  --chart-4: oklch(0.83 0.17 81.03);
  --chart-5: oklch(0.77 0.17 65.36);
  --sidebar: oklch(0.99 0 0);
  --sidebar-foreground: oklch(0.14 0.00 285.86);
  --sidebar-primary: oklch(0.21 0.01 285.93);
  --sidebar-primary-foreground: oklch(0.99 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.21 0.01 285.93);
  --sidebar-border: oklch(0.92 0.00 286.61);
  --sidebar-ring: oklch(0.71 0.01 286.09);

  --font-sans: 'Geist', 'Geist Fallback', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-serif: "Geist", "Geist Fallback", ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: "Geist Mono", "Geist Mono Fallback", ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;

  --radius: 0.625rem;
  
  --shadow-2xs: 0 1px 3px 0px oklch(0.00 0 0 / 0.05);
  --shadow-xs: 0 1px 3px 0px oklch(0.00 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 1px 2px -1px oklch(0.00 0 0 / 0.10);
  --shadow: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 1px 2px -1px oklch(0.00 0 0 / 0.10);
  --shadow-md: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 2px 4px -1px oklch(0.00 0 0 / 0.10);
  --shadow-lg: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 4px 6px -1px oklch(0.00 0 0 / 0.10);
  --shadow-xl: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 8px 10px -1px oklch(0.00 0 0 / 0.10);
  --shadow-2xl: 0 1px 3px 0px oklch(0.00 0 0 / 0.25);
  
}

.dark {
  --background: oklch(0.14 0.00 285.86);
  --foreground: oklch(0.99 0 0);
  --card: oklch(0.21 0.01 285.93);
  --card-foreground: oklch(0.99 0 0);
  --popover: oklch(0.21 0.01 285.93);
  --popover-foreground: oklch(0.99 0 0);
  --primary: oklch(0.92 0.00 286.61);
  --primary-foreground: oklch(0.21 0.01 285.93);
  --secondary: oklch(0.27 0.01 286.10);
  --secondary-foreground: oklch(0.99 0 0);
  --muted: oklch(0.27 0.01 286.10);
  --muted-foreground: oklch(0.71 0.01 286.09);
  --accent: oklch(0.27 0.01 286.10);
  --accent-foreground: oklch(0.99 0 0);
  --destructive: oklch(0.70 0.19 22.23);
  --border: oklch(1.00 0 0 / 10%);
  --input: oklch(1.00 0 0 / 15%);
  --ring: oklch(0.55 0.02 285.93);
  --chart-1: oklch(0.49 0.24 264.40);
  --chart-2: oklch(0.70 0.16 160.43);
  --chart-3: oklch(0.77 0.17 65.36);
  --chart-4: oklch(0.62 0.26 305.32);
  --chart-5: oklch(0.64 0.25 16.51);
  --sidebar: oklch(0.21 0.01 285.93);
  --sidebar-foreground: oklch(0.99 0 0);
  --sidebar-primary: oklch(0.49 0.24 264.40);
  --sidebar-primary-foreground: oklch(0.99 0 0);
  --sidebar-accent: oklch(0.27 0.01 286.10);
  --sidebar-accent-foreground: oklch(0.99 0 0);
  --sidebar-border: oklch(1.00 0 0 / 10%);
  --sidebar-ring: oklch(0.55 0.02 285.93);
  
  --shadow-2xs: 0 1px 3px 0px oklch(0.00 0 0 / 0.05);
  --shadow-xs: 0 1px 3px 0px oklch(0.00 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 1px 2px -1px oklch(0.00 0 0 / 0.10);
  --shadow: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 1px 2px -1px oklch(0.00 0 0 / 0.10);
  --shadow-md: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 2px 4px -1px oklch(0.00 0 0 / 0.10);
  --shadow-lg: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 4px 6px -1px oklch(0.00 0 0 / 0.10);
  --shadow-xl: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 8px 10px -1px oklch(0.00 0 0 / 0.10);
  --shadow-2xl: 0 1px 3px 0px oklch(0.00 0 0 / 0.25);
}

