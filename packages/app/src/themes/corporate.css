.theme-corporate {
  --background: oklch(0.98 0 0);
  --foreground: oklch(0.21 0.03 263.61);
  --card: oklch(1.00 0 0);
  --card-foreground: oklch(0.21 0.03 263.61);
  --popover: oklch(1.00 0 0);
  --popover-foreground: oklch(0.21 0.03 263.61);
  --primary: oklch(0.48 0.20 260.47);
  --primary-foreground: oklch(1.00 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.37 0.03 259.73);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.55 0.02 264.41);
  --accent: oklch(0.95 0.02 260.18);
  --accent-foreground: oklch(0.48 0.20 260.47);
  --destructive: oklch(0.58 0.22 27.29);
  --border: oklch(0.93 0.01 261.82);
  --input: oklch(0.93 0.01 261.82);
  --ring: oklch(0.48 0.20 260.47);
  --chart-1: oklch(0.48 0.20 260.47);
  --chart-2: oklch(0.56 0.24 260.92);
  --chart-3: oklch(0.40 0.16 259.61);
  --chart-4: oklch(0.43 0.16 259.82);
  --chart-5: oklch(0.29 0.07 261.20);
  --sidebar: oklch(0.97 0 0);
  --sidebar-foreground: oklch(0.21 0.03 263.61);
  --sidebar-primary: oklch(0.48 0.20 260.47);
  --sidebar-primary-foreground: oklch(1.00 0 0);
  --sidebar-accent: oklch(0.95 0.02 260.18);
  --sidebar-accent-foreground: oklch(0.48 0.20 260.47);
  --sidebar-border: oklch(0.93 0.01 261.82);
  --sidebar-ring: oklch(0.48 0.20 260.47);

  --font-sans: Inter, sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: IBM Plex Mono, monospace;

  --radius: 0.375rem;
  
  --shadow-2xs: 0 1px 3px 0px oklch(0.00 0 0 / 0.05);
  --shadow-xs: 0 1px 3px 0px oklch(0.00 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 1px 2px -1px oklch(0.00 0 0 / 0.10);
  --shadow: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 1px 2px -1px oklch(0.00 0 0 / 0.10);
  --shadow-md: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 2px 4px -1px oklch(0.00 0 0 / 0.10);
  --shadow-lg: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 4px 6px -1px oklch(0.00 0 0 / 0.10);
  --shadow-xl: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 8px 10px -1px oklch(0.00 0 0 / 0.10);
  --shadow-2xl: 0 1px 3px 0px oklch(0.00 0 0 / 0.25);
  
}

.theme-corporate.dark {
  --background: oklch(0.26 0.03 262.67);
  --foreground: oklch(0.93 0.01 261.82);
  --card: oklch(0.30 0.03 260.51);
  --card-foreground: oklch(0.93 0.01 261.82);
  --popover: oklch(0.30 0.03 260.51);
  --popover-foreground: oklch(0.93 0.01 261.82);
  --primary: oklch(0.56 0.24 260.92);
  --primary-foreground: oklch(1.00 0 0);
  --secondary: oklch(0.35 0.04 261.40);
  --secondary-foreground: oklch(0.93 0.01 261.82);
  --muted: oklch(0.30 0.03 260.51);
  --muted-foreground: oklch(0.71 0.02 261.33);
  --accent: oklch(0.33 0.04 264.63);
  --accent-foreground: oklch(0.93 0.01 261.82);
  --destructive: oklch(0.64 0.21 25.39);
  --border: oklch(0.35 0.04 261.40);
  --input: oklch(0.35 0.04 261.40);
  --ring: oklch(0.56 0.24 260.92);
  --chart-1: oklch(0.56 0.24 260.92);
  --chart-2: oklch(0.48 0.20 260.47);
  --chart-3: oklch(0.69 0.17 255.59);
  --chart-4: oklch(0.43 0.16 259.82);
  --chart-5: oklch(0.29 0.07 261.20);
  --sidebar: oklch(0.26 0.03 262.67);
  --sidebar-foreground: oklch(0.93 0.01 261.82);
  --sidebar-primary: oklch(0.56 0.24 260.92);
  --sidebar-primary-foreground: oklch(1.00 0 0);
  --sidebar-accent: oklch(0.33 0.04 264.63);
  --sidebar-accent-foreground: oklch(0.93 0.01 261.82);
  --sidebar-border: oklch(0.35 0.04 261.40);
  --sidebar-ring: oklch(0.56 0.24 260.92);
  
  --shadow-2xs: 0 1px 3px 0px oklch(0.00 0 0 / 0.05);
  --shadow-xs: 0 1px 3px 0px oklch(0.00 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 1px 2px -1px oklch(0.00 0 0 / 0.10);
  --shadow: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 1px 2px -1px oklch(0.00 0 0 / 0.10);
  --shadow-md: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 2px 4px -1px oklch(0.00 0 0 / 0.10);
  --shadow-lg: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 4px 6px -1px oklch(0.00 0 0 / 0.10);
  --shadow-xl: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 8px 10px -1px oklch(0.00 0 0 / 0.10);
  --shadow-2xl: 0 1px 3px 0px oklch(0.00 0 0 / 0.25);
}

