.theme-perplexity {
  --background: oklch(0.95 0.01 196.81);
  --foreground: oklch(0.38 0.06 211.49);
  --card: oklch(0.97 0.01 196.81);
  --card-foreground: oklch(0.38 0.06 211.49);
  --popover: oklch(0.97 0.01 196.81);
  --popover-foreground: oklch(0.38 0.06 211.49);
  --primary: oklch(0.72 0.12 210.36);
  --primary-foreground: oklch(1.00 0 0);
  --secondary: oklch(0.97 0.01 247.91);
  --secondary-foreground: oklch(0.14 0 0);
  --muted: oklch(0.97 0.01 247.91);
  --muted-foreground: oklch(0.55 0.04 256.40);
  --accent: oklch(0.96 0.02 204.34);
  --accent-foreground: oklch(0.57 0.10 213.73);
  --destructive: oklch(0.64 0.21 25.39);
  --border: oklch(0.93 0.01 256.71);
  --input: oklch(0.93 0.01 256.71);
  --ring: oklch(0.72 0.12 210.36);
  --chart-1: oklch(0.72 0.12 210.36);
  --chart-2: oklch(0.57 0.10 213.73);
  --chart-3: oklch(0.79 0.12 209.45);
  --chart-4: oklch(0.76 0.11 208.70);
  --chart-5: oklch(0.83 0.10 208.33);
  --sidebar: oklch(0.98 0 0);
  --sidebar-foreground: oklch(0.14 0 0);
  --sidebar-primary: oklch(0.72 0.12 210.36);
  --sidebar-primary-foreground: oklch(1.00 0 0);
  --sidebar-accent: oklch(0.96 0.02 204.34);
  --sidebar-accent-foreground: oklch(0.57 0.10 213.73);
  --sidebar-border: oklch(0.93 0.01 256.71);
  --sidebar-ring: oklch(0.72 0.12 210.36);

  --font-sans: Inter, sans-serif;
  --font-serif: Lora, serif;
  --font-mono: Roboto Mono, monospace;

  --radius: 0.5rem;
  
  --shadow-2xs: 0 1px 3px 0px oklch(0.00 0 0 / 0.05);
  --shadow-xs: 0 1px 3px 0px oklch(0.00 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 1px 2px -1px oklch(0.00 0 0 / 0.10);
  --shadow: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 1px 2px -1px oklch(0.00 0 0 / 0.10);
  --shadow-md: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 2px 4px -1px oklch(0.00 0 0 / 0.10);
  --shadow-lg: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 4px 6px -1px oklch(0.00 0 0 / 0.10);
  --shadow-xl: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 8px 10px -1px oklch(0.00 0 0 / 0.10);
  --shadow-2xl: 0 1px 3px 0px oklch(0.00 0 0 / 0.25);
  
}

.theme-perplexity.dark {
  --background: oklch(0.21 0.02 226.02);
  --foreground: oklch(0.85 0.13 194.97);
  --card: oklch(0.23 0.03 218.18);
  --card-foreground: oklch(0.85 0.13 194.97);
  --popover: oklch(0.23 0.03 218.18);
  --popover-foreground: oklch(0.85 0.13 194.97);
  --primary: oklch(0.72 0.12 210.36);
  --primary-foreground: oklch(1.00 0 0);
  --secondary: oklch(0.27 0.01 285.81);
  --secondary-foreground: oklch(0.97 0 0);
  --muted: oklch(0.24 0 0);
  --muted-foreground: oklch(0.71 0.01 286.23);
  --accent: oklch(0.24 0 0);
  --accent-foreground: oklch(0.97 0 0);
  --destructive: oklch(0.64 0.21 25.39);
  --border: oklch(0.29 0 0);
  --input: oklch(0.29 0 0);
  --ring: oklch(0.72 0.12 210.36);
  --chart-1: oklch(0.72 0.12 210.36);
  --chart-2: oklch(0.79 0.12 209.45);
  --chart-3: oklch(0.76 0.11 208.70);
  --chart-4: oklch(0.83 0.10 208.33);
  --chart-5: oklch(0.57 0.10 213.73);
  --sidebar: oklch(0.19 0 0);
  --sidebar-foreground: oklch(0.97 0 0);
  --sidebar-primary: oklch(0.72 0.12 210.36);
  --sidebar-primary-foreground: oklch(1.00 0 0);
  --sidebar-accent: oklch(0.24 0 0);
  --sidebar-accent-foreground: oklch(0.97 0 0);
  --sidebar-border: oklch(0.29 0 0);
  --sidebar-ring: oklch(0.72 0.12 210.36);
  
  --shadow-2xs: 0 1px 3px 0px oklch(0.00 0 0 / 0.05);
  --shadow-xs: 0 1px 3px 0px oklch(0.00 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 1px 2px -1px oklch(0.00 0 0 / 0.10);
  --shadow: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 1px 2px -1px oklch(0.00 0 0 / 0.10);
  --shadow-md: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 2px 4px -1px oklch(0.00 0 0 / 0.10);
  --shadow-lg: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 4px 6px -1px oklch(0.00 0 0 / 0.10);
  --shadow-xl: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 8px 10px -1px oklch(0.00 0 0 / 0.10);
  --shadow-2xl: 0 1px 3px 0px oklch(0.00 0 0 / 0.25);
}

