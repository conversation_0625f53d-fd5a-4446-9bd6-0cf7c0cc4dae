.theme-nature {
  --background: oklch(0.97 0.01 81.76);
  --foreground: oklch(0.30 0.04 29.20);
  --card: oklch(0.97 0.01 81.76);
  --card-foreground: oklch(0.30 0.04 29.20);
  --popover: oklch(0.97 0.01 81.76);
  --popover-foreground: oklch(0.30 0.04 29.20);
  --primary: oklch(0.52 0.13 144.33);
  --primary-foreground: oklch(1.00 0 0);
  --secondary: oklch(0.96 0.02 147.54);
  --secondary-foreground: oklch(0.43 0.12 144.33);
  --muted: oklch(0.95 0.01 72.65);
  --muted-foreground: oklch(0.45 0.05 38.69);
  --accent: oklch(0.90 0.05 146.01);
  --accent-foreground: oklch(0.43 0.12 144.33);
  --destructive: oklch(0.54 0.19 26.90);
  --border: oklch(0.88 0.02 77.29);
  --input: oklch(0.88 0.02 77.29);
  --ring: oklch(0.52 0.13 144.33);
  --chart-1: oklch(0.67 0.16 144.06);
  --chart-2: oklch(0.58 0.14 144.14);
  --chart-3: oklch(0.52 0.13 144.33);
  --chart-4: oklch(0.43 0.12 144.33);
  --chart-5: oklch(0.22 0.05 145.19);
  --sidebar: oklch(0.94 0.01 72.65);
  --sidebar-foreground: oklch(0.30 0.04 29.20);
  --sidebar-primary: oklch(0.52 0.13 144.33);
  --sidebar-primary-foreground: oklch(1.00 0 0);
  --sidebar-accent: oklch(0.90 0.05 146.01);
  --sidebar-accent-foreground: oklch(0.43 0.12 144.33);
  --sidebar-border: oklch(0.88 0.02 77.29);
  --sidebar-ring: oklch(0.52 0.13 144.33);

  --font-sans: Montserrat, sans-serif;
  --font-serif: Merriweather, serif;
  --font-mono: Source Code Pro, monospace;

  --radius: 0.5rem;
  
  --shadow-2xs: 0 1px 3px 0px oklch(0.00 0 0 / 0.05);
  --shadow-xs: 0 1px 3px 0px oklch(0.00 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 1px 2px -1px oklch(0.00 0 0 / 0.10);
  --shadow: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 1px 2px -1px oklch(0.00 0 0 / 0.10);
  --shadow-md: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 2px 4px -1px oklch(0.00 0 0 / 0.10);
  --shadow-lg: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 4px 6px -1px oklch(0.00 0 0 / 0.10);
  --shadow-xl: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 8px 10px -1px oklch(0.00 0 0 / 0.10);
  --shadow-2xl: 0 1px 3px 0px oklch(0.00 0 0 / 0.25);
  
}

.theme-nature.dark {
  --background: oklch(0.14 0.00 285.86);
  --card: oklch(0.21 0.01 285.93);
  --foreground: oklch(0.99 0 0);
  --card: oklch(0.21 0.01 285.93);
  --card-foreground: oklch(0.99 0 0);
  --popover: oklch(0.21 0.01 285.93);
  --popover-foreground: oklch(0.99 0 0);
  --primary: oklch(0.92 0.00 286.61);
  --primary-foreground: oklch(0.21 0.01 285.93);
  --secondary: oklch(0.27 0.01 286.10);
  --secondary-foreground: oklch(0.99 0 0);
  --muted: oklch(0.27 0.01 286.10);
  --muted-foreground: oklch(0.71 0.01 286.09);
  --accent: oklch(0.27 0.01 286.10);
  --accent-foreground: oklch(0.99 0 0);
  --destructive: oklch(0.70 0.19 22.23);
  --border: oklch(1.00 0 0 / 10%);
  --input: oklch(1.00 0 0 / 15%);
  --ring: oklch(0.55 0.02 285.93);
  --chart-1: oklch(0.49 0.24 264.40);
  --chart-2: oklch(0.70 0.16 160.43);
  --chart-3: oklch(0.77 0.17 65.36);
  --chart-4: oklch(0.62 0.26 305.32);
  --chart-5: oklch(0.64 0.25 16.51);
  --sidebar: oklch(0.21 0.01 285.93);
  --sidebar-foreground: oklch(0.99 0 0);
  --sidebar-primary: oklch(0.49 0.24 264.40);
  --sidebar-primary-foreground: oklch(0.99 0 0);
  --sidebar-accent: oklch(0.27 0.01 286.10);
  --sidebar-accent-foreground: oklch(0.99 0 0);
  --sidebar-border: oklch(1.00 0 0 / 10%);
  --sidebar-ring: oklch(0.55 0.02 285.93);
  
  --shadow-2xs: 0 1px 3px 0px oklch(0.00 0 0 / 0.05);
  --shadow-xs: 0 1px 3px 0px oklch(0.00 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 1px 2px -1px oklch(0.00 0 0 / 0.10);
  --shadow: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 1px 2px -1px oklch(0.00 0 0 / 0.10);
  --shadow-md: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 2px 4px -1px oklch(0.00 0 0 / 0.10);
  --shadow-lg: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 4px 6px -1px oklch(0.00 0 0 / 0.10);
  --shadow-xl: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 8px 10px -1px oklch(0.00 0 0 / 0.10);
  --shadow-2xl: 0 1px 3px 0px oklch(0.00 0 0 / 0.25);
}

