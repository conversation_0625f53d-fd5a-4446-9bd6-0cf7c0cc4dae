export const predefinedProviders: ModelProvider[] = [
  {
    active: true,
    apiKey: "",
    name: "<PERSON>A<PERSON>",
    icon: "/openai.svg",
    baseUrl: "https://api.openai.com/v1",
    apiKeyHelpUrl: "https://platform.openai.com/account/api-keys",
    baseUrlHelpUrl: "https://platform.openai.com/docs/api-reference/chat/create",
    provider: "openai",
    models: [],
  },
  {
    active: true,
    apiKey: "",
    name: "Anthropic",
    icon: "/anthropic.svg",
    apiKeyHelpUrl: "https://console.anthropic.com/settings/keys",
    baseUrl: "https://api.anthropic.com",
    provider: "anthropic",
    baseUrlHelpUrl: "https://docs.anthropic.com/en/api/messages",
    models: [],
  },
  {
    active: true,
    apiKey: "",
    name: "OpenRouter",
    icon: "/openrouter.svg",
    baseUrl: "https://openrouter.ai/api/v1",
    apiKeyHelpUrl: "https://openrouter.ai/settings/keys",
    baseUrlHelpUrl: "https://openrouter.ai/docs/api-reference/overview",
    provider: "openrouter",
    models: [],
  },
  {
    active: true,
    apiKey: "",
    name: "Grok",
    icon: "/grok.svg",
    baseUrl: "https://api.x.ai/v1",
    apiKeyHelpUrl: "https://console.x.ai/team/default/api-keys",
    baseUrlHelpUrl: "https://docs.x.ai/docs/models",
    provider: "grok",
    models: [],
  },
  {
    active: true,
    apiKey: "",
    name: "Gemini",
    icon: "/gemini.svg",
    baseUrl: "https://api.google.com/v1",
    apiKeyHelpUrl: "https://aistudio.google.com/apikey",
    baseUrlHelpUrl: "https://ai.google.dev/gemini-api/docs/openai",
    provider: "gemini",
    models: [],
  },
  {
    active: true,
    apiKey: "",
    name: "DeepSeek",
    icon: "/deepseek.svg",
    baseUrl: "https://api.deepseek.com/v1",
    apiKeyHelpUrl: "https://platform.deepseek.com/api_keys",
    baseUrlHelpUrl: "https://api-docs.deepseek.com",
    provider: "deepseek",
    models: [],
  },
];
