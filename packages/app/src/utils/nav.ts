import { BOOK_IDS_SEPARATOR } from "@/services/constants";
import { isPWA, isWebAppPlatform } from "@/services/environment";
import type { NavigateFunction } from "react-router";

export const navigateToReader = (
  navigate: NavigateFunction,
  bookIds: string[],
  queryParams?: string,
  navOptions?: { replace?: boolean; preventScrollReset?: boolean },
) => {
  const ids = bookIds.join(BOOK_IDS_SEPARATOR);
  if (isWebAppPlatform() && !isPWA()) {
    navigate(`/reader/${ids}${queryParams ? `?${queryParams}` : ""}`, navOptions);
  } else {
    const params = new URLSearchParams(queryParams || "");
    params.set("ids", ids);
    navigate(`/reader?${params.toString()}`, navOptions);
  }
};

export const navigateToLogin = (navigate: NavigateFunction) => {
  const pathname = window.location.pathname;
  const search = window.location.search;
  const currentPath = pathname !== "/auth" ? pathname + search : "/";
  navigate(`/auth?redirect=${encodeURIComponent(currentPath)}`);
};

export const navigateToProfile = (navigate: NavigateFunction) => {
  navigate("/user");
};

export const navigateToLibrary = (
  navigate: NavigateFunction,
  queryParams?: string,
  navOptions?: { replace?: boolean; preventScrollReset?: boolean },
) => {
  navigate(`/library${queryParams ? `?${queryParams}` : ""}`, navOptions);
};

export const redirectToLibrary = () => {
  // 在 React Router v7 中，redirect 通常在 loader/action 中使用
  // 如果在组件中需要重定向，使用 navigate
  window.location.href = "/library";
};
