import type { ExplainTextEventDetail } from "@/services/iframe-service";
import { useCallback, useEffect } from "react";

interface UseTextEventHandlerOptions {
  sendMessage: (message: { text: string }) => void;
  onTextReceived?: (text: string) => void;
}

export const useTextEventHandler = (options: UseTextEventHandlerOptions) => {
  const { sendMessage, onTextReceived } = options;

  // 处理自定义文本事件
  const handleTextEvent = useCallback(
    (event: CustomEvent<ExplainTextEventDetail>) => {
      console.log("📨 收到文本解释事件:", event.detail);

      const text = event.detail?.text;
      if (text) {
        console.log("🔍 处理解释文本请求:", text);
        onTextReceived?.(text);
        // 直接调用 sendMessage
        sendMessage({ text });
      }
    },
    [sendMessage, onTextReceived],
  );

  useEffect(() => {
    // 监听自定义文本事件
    window.addEventListener("explainText", handleTextEvent as EventListener);

    return () => {
      window.removeEventListener("explainText", handleTextEvent as EventListener);
    };
  }, [handleTextEvent]);
};
