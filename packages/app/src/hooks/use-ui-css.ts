import { useBookSettingsStore } from "@/store/book-settings-store";
import { useReaderStore } from "@/store/reader-store";
import { useEffect, useState } from "react";

// This hook allows you to inject custom CSS into the reader UI.
// Note that the book content is rendered in an iframe, so UI CSS won't affect book rendering.
export const useUICSS = (bookKey?: string) => {
  const { settings } = useBookSettingsStore();
  const { getViewSettings } = useReaderStore();
  const viewSettings = getViewSettings(bookKey || "");
  const [styleElement, setStyleElement] = useState<HTMLStyleElement | null>(null);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (styleElement) {
      styleElement.remove();
    }

    const rawCSS = viewSettings?.userUIStylesheet || settings?.globalViewSettings?.userUIStylesheet || "";

    const newStyleEl = document.createElement("style");
    newStyleEl.textContent = rawCSS.replace("foliate-view", `#foliate-view-${bookKey}`);
    document.head.appendChild(newStyleEl);
    setStyleElement(newStyleEl);

    return () => {
      newStyleEl.remove();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [viewSettings?.userUIStylesheet, settings?.globalViewSettings?.userUIStylesheet, bookKey]);
};
