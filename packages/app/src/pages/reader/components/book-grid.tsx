import { useSafeAreaInsets } from "@/hooks/use-safe-areaInsets";
import { useBookSettingsStore } from "@/store/book-settings-store";
import { useReaderStore } from "@/store/reader-store";
import { getGridTemplate, getInsetEdges } from "@/utils/grid";
import { getViewInsets } from "@/utils/insets";
import clsx from "clsx";
import type React from "react";
import { useEffect, useState } from "react";
import Annotator from "./annotator/annotator";
import DoubleBorder from "./double-border";
import FoliateViewer from "./foliate-viewer";
import FooterBar from "./footer-bar";
import HeaderBar from "./header-bar";

interface BookGridProps {
  onCloseBook: (bookKey: string) => void;
}

const BookGrid: React.FC<BookGridProps> = ({ onCloseBook }) => {
  const { setFontLayoutSettingsDialogOpen } = useBookSettingsStore();
  const { activeBookId, getBookData, getProgress, getViewSettings } = useReaderStore();
  const bookData = getBookData(activeBookId);
  const progress = getProgress(activeBookId);
  const viewSettings = getViewSettings(activeBookId);
  const [shouldRenderViewers, setShouldRenderViewers] = useState(false);
  const screenInsets = useSafeAreaInsets();
  const aspectRatio = window.innerWidth / window.innerHeight;
  const gridTemplate = getGridTemplate(1, aspectRatio);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShouldRenderViewers(true);
    }, 50);

    return () => clearTimeout(timer);
  }, []);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    const book = bookData?.book;
    if (!book) return;
    document.title = book.title;
  }, [bookData?.book?.title]);

  const calcGridInsets = (index: number, count: number) => {
    if (!screenInsets) return { top: 0, right: 0, bottom: 0, left: 0 };
    const { top, right, bottom, left } = getInsetEdges(index, count, aspectRatio);
    return {
      top: top ? screenInsets.top : 0,
      right: right ? screenInsets.right : 0,
      bottom: bottom ? screenInsets.bottom : 0,
      left: left ? screenInsets.left : 0,
    };
  };

  if (!screenInsets) return null;

  return (
    <div
      className={clsx("books-grid relative grid h-full flex-grow")}
      style={{
        gridTemplateColumns: gridTemplate.columns,
        gridTemplateRows: gridTemplate.rows,
      }}
    >
      {(() => {
        const config = bookData?.config;
        const gridInsets = calcGridInsets(0, 1);
        const { book, bookDoc } = bookData || {};

        if (!book || !config || !bookDoc || !viewSettings) {
          return null;
        }

        const { section, pageinfo, sectionLabel } = progress || {};
        const horizontalGapPercent = viewSettings.gapPercent;
        const viewInsets = getViewInsets(viewSettings);
        const contentInsets = {
          top: gridInsets.top + viewInsets.top,
          right: gridInsets.right + viewInsets.right,
          bottom: gridInsets.bottom + viewInsets.bottom,
          left: gridInsets.left + viewInsets.left,
        };
        const scrolled = viewSettings.scrolled;
        const showBarsOnScroll = viewSettings.showBarsOnScroll;
        const showHeader = viewSettings.showHeader && (scrolled ? showBarsOnScroll : true);
        const showFooter = viewSettings.showFooter && (scrolled ? showBarsOnScroll : true);

        return (
          <div
            id={`gridcell-${activeBookId}`}
            className="relative flex h-full w-full flex-col overflow-hidden bg-background"
          >
            <HeaderBar
              bookKey={activeBookId || ""}
              bookTitle={book.title}
              isTopLeft={true}
              isHoveredAnim={false}
              onCloseBook={onCloseBook}
              onSetSettingsDialogOpen={setFontLayoutSettingsDialogOpen}
              gridInsets={gridInsets}
              section={sectionLabel || ""}
            />
            <div className="relative mt-0 flex min-h-0 flex-1 flex-col">
              {shouldRenderViewers ? (
                <FoliateViewer
                  bookKey={activeBookId || ""}
                  bookDoc={bookDoc}
                  config={config}
                  contentInsets={contentInsets}
                />
              ) : (
                <div className="flex h-full w-full items-center justify-center rounded-md">
                  <div className="absolute inset-0 z-50 flex items-center justify-center bg-white/70 backdrop-blur-sm dark:bg-neutral-900/60" />
                </div>
              )}
            </div>
            <FooterBar
              bookKey={activeBookId || ""}
              bookFormat={book.format}
              section={section}
              pageinfo={pageinfo}
              isHoveredAnim={false}
              gridInsets={gridInsets}
            />
            {viewSettings.vertical && viewSettings.scrolled && (
              <>
                {(showFooter || viewSettings.doubleBorder) && (
                  <div
                    className="absolute top-0 left-0 h-full bg-base-100"
                    style={{
                      width: `calc(${contentInsets.left + (showFooter ? 32 : 0)}px)`,
                      height: "calc(100%)",
                    }}
                  />
                )}
                {(showHeader || viewSettings.doubleBorder) && (
                  <div
                    className="absolute top-0 right-0 h-full bg-base-100"
                    style={{
                      width: `calc(${contentInsets.right + (showHeader ? 32 : 0)}px)`,
                      height: "calc(100%)",
                    }}
                  />
                )}
              </>
            )}
            {viewSettings.vertical && viewSettings.doubleBorder && (
              <DoubleBorder
                showHeader={showHeader}
                showFooter={showFooter}
                borderColor={viewSettings.borderColor}
                horizontalGap={horizontalGapPercent}
                contentInsets={contentInsets}
              />
            )}
            <Annotator bookKey={activeBookId || ""} />
          </div>
        );
      })()}
    </div>
  );
};

export default BookGrid;
