import Spinner from "@/components/ui/spinner";
import { useEnv } from "@/context/env-context";
import { useBookSettingsStore } from "@/store/book-settings-store";
import { useReaderStore } from "@/store/reader-store";

import type { SystemSettings } from "@/types/settings";
import { eventDispatcher } from "@/utils/event";
import { navigateToLibrary } from "@/utils/nav";
import { throttle } from "@/utils/throttle";
import type * as React from "react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router";
import useBookShortcuts from "../hooks/use-book-shortcuts";
import BookGrid from "./book-grid";

const ReaderContent: React.FC<{ id?: string; settings: SystemSettings }> = ({ id, settings }) => {
  const navigate = useNavigate();
  const { envConfig } = useEnv();
  const {
    activeBookId,
    setActiveBookId,
    getBookData,
    getViewSettings,
    initBookData,
    getView,
    getConfig,
    saveConfig,
    tabs,
    removeTab,
  } = useReaderStore();

  // 从 tabs 中获取 bookKeys 以保持兼容性
  const bookKeys = tabs.map((tab) => tab.bookId);
  const { saveSettings } = useBookSettingsStore();
  const [loading, setLoading] = useState(false);
  const currentBookId = id || activeBookId;
  const currentBookData = getBookData(currentBookId);
  const currentViewSettings = getViewSettings(currentBookId);
  useBookShortcuts();

  useEffect(() => {
    if (!id) return;
    setLoading(false);
    setActiveBookId(id);
    initBookData(envConfig, id, id).catch((error: any) => {
      console.log("Error initializing book data", id, error);
    });
  }, [id, setActiveBookId, initBookData, envConfig]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    window.addEventListener("beforeunload", handleCloseBooks);
    eventDispatcher.on("beforereload", handleCloseBooks);
    eventDispatcher.on("quit-app", handleCloseBooks);
    return () => {
      window.removeEventListener("beforeunload", handleCloseBooks);
      eventDispatcher.off("beforereload", handleCloseBooks);
      eventDispatcher.off("quit-app", handleCloseBooks);
    };
  }, [bookKeys]);

  const saveBookConfig = async (bookKey: string) => {
    const config = getConfig(bookKey);
    const { book } = getBookData(bookKey) || {};
    if (book && config) {
      eventDispatcher.dispatch("sync-book-progress", { bookKey });
      const settings = useBookSettingsStore.getState().settings;
      await saveConfig(envConfig, bookKey, config, settings);
    }
  };

  const saveConfigAndCloseBook = async (bookKey: string) => {
    try {
      getView(bookKey)?.close();
      getView(bookKey)?.remove();
    } catch {
      console.info("Error closing book", bookKey);
    }
    eventDispatcher.dispatch("tts-stop", { bookKey });
    await saveBookConfig(bookKey);
  };

  const saveSettingsAndGoToLibrary = () => {
    saveSettings(envConfig, settings);
    navigateToLibrary(navigate);
  };

  const handleCloseBooks = throttle(async () => {
    const settings = useBookSettingsStore.getState().settings;
    await Promise.all(bookKeys.map((key: string) => saveConfigAndCloseBook(key)));
    await saveSettings(envConfig, settings);
  }, 200);

  const handleCloseBook = async (bookKey: string) => {
    saveConfigAndCloseBook(bookKey);
    const tabId = `reader-${bookKey}`;
    removeTab(tabId);
    if (bookKeys.filter((key: string) => key !== bookKey).length === 0) {
      saveSettingsAndGoToLibrary();
    }
  };

  if (!currentBookId || !currentViewSettings) {
    return null;
  }

  if (!currentBookData || !currentBookData.book || !currentBookData.bookDoc) {
    setTimeout(() => setLoading(true), 300);
    return (
      loading && (
        <div className="reader-content flex h-dvh">
          <Spinner loading={true} />
        </div>
      )
    );
  }

  return (
    <div className="reader-content flex h-[calc(100vh-50px)] rounded-md">
      <BookGrid onCloseBook={handleCloseBook} />
    </div>
  );
};

export default ReaderContent;
