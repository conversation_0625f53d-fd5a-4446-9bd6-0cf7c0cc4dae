import type { BookDoc } from "@/lib/document";
import type { BookConfig } from "@/types/book";
import type { Insets } from "@/types/misc";
import type React from "react";
import { useFoliateViewer } from "../hooks/use-foliate-viewer";

const FoliateViewer: React.FC<{
  bookKey: string;
  bookDoc: BookDoc;
  config: BookConfig;
  contentInsets: Insets;
}> = ({ bookKey, bookDoc, config, contentInsets: insets }) => {
  const { containerRef, mouseHandlers, touchHandlers } = useFoliateViewer(bookKey, bookDoc, config, insets);

  return <div ref={containerRef} className="foliate-viewer h-[100%] w-[100%]" {...mouseHandlers} {...touchHandlers} />;
};

export default FoliateViewer;
