import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useTranslation } from "@/hooks/use-translation";
import { useReaderStore } from "@/store/reader-store";
import type { Insets } from "@/types/misc";
import { TableOfContents } from "lucide-react";
import type React from "react";
import { useRef, useState } from "react";
import SearchDropdown from "./search-dropdown";
import SettingsDropdown from "./settings-dropdown";
import TOCView from "./toc-view";

interface HeaderBarProps {
  bookKey: string;
  bookTitle: string;
  isTopLeft: boolean;
  isHoveredAnim: boolean;
  gridInsets: Insets;
  onCloseBook: (bookKey: string) => void;
  onSetSettingsDialogOpen: (open: boolean) => void;
  section: string;
}

const HeaderBar: React.FC<HeaderBarProps> = ({ section, bookKey }) => {
  const _ = useTranslation();
  const headerRef = useRef<HTMLDivElement>(null);
  const [isTocDropdownOpen, setIsTocDropdownOpen] = useState(false);
  const { getBookData } = useReaderStore();
  // Get book data to access toc
  const bookData = getBookData(bookKey.split("-")[0]!);
  const bookDoc = bookData?.bookDoc;

  const handleToggleTocDropdown = (isOpen: boolean) => {
    setIsTocDropdownOpen(isOpen);
  };

  const handleTocItemSelect = () => {
    setIsTocDropdownOpen(false);
  };

  return (
    <div className="w-full">
      <div
        ref={headerRef}
        className="header-bar data-tauri-drag-region pointer-events-auto visible flex h-11.5 w-full items-center px-2 pl-4 transition-[opacity,margin-top] duration-300"
      >
        <div className="flex h-full items-center justify-start gap-x-2">
          <DropdownMenu open={isTocDropdownOpen} onOpenChange={handleToggleTocDropdown}>
            <DropdownMenuTrigger asChild>
              <button className="btn btn-ghost flex h-6 w-6 items-center justify-center rounded-full p-0">
                <TableOfContents size={18} className="text-base-content" />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="max-h-[calc(100vh-8rem)] w-80 overflow-y-auto p-0"
              align="start"
              side="bottom"
              sideOffset={4}
            >
              {bookDoc?.toc ? (
                <div className="h-full">
                  <TOCView
                    toc={bookDoc.toc}
                    bookKey={bookKey}
                    autoExpand={true}
                    onItemSelect={handleTocItemSelect}
                    isVisible={isTocDropdownOpen}
                  />
                </div>
              ) : (
                <div className="p-4 text-center text-muted-foreground">{_("No table of contents available")}</div>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="data-tauri-drag-region flex min-w-0 flex-1 items-center justify-center gap-x-2 px-4 ">
          <span className="flex-shrink-0 whitespace-nowrap text-neutral-800 dark:text-neutral-300">{section}</span>
        </div>

        <div className="flex h-full items-center justify-end space-x-4 ps-2">
          <SearchDropdown bookKey={bookKey} />
          <SettingsDropdown bookKey={bookKey} />
        </div>
      </div>
    </div>
  );
};

export default HeaderBar;
