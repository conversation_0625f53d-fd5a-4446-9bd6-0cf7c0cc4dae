import { Checkbox } from "@/components/ui/checkbox";
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useEnv } from "@/context/env-context";
import { useTranslation } from "@/hooks/use-translation";
import { CURATED_FONTS } from "@/services/constants";
import { useReaderStore } from "@/store/reader-store";
import { useThemeStore } from "@/store/theme-store";
import { getMaxInlineSize } from "@/utils/config";
import { isCJKEnv } from "@/utils/misc";
import { getStyles } from "@/utils/style";
import { Columns, PanelLeft, Settings2, Wand2 } from "lucide-react";
import type React from "react";
import { useCallback, useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, MdOutlineDarkMode, MdOutlineLightMode } from "react-icons/md";
import { TbSunMoon } from "react-icons/tb";
import { saveViewSettings } from "../utils/viewSettingsHelper";
import { FontSizeSlider } from "./font-size-slider";

interface SettingsDropdownProps {
  bookKey: string;
}

const FONT_SIZE_MIN = 12;
const FONT_SIZE_MAX = 32;
const FONT_SIZE_STEP = 2;

const SettingsDropdown: React.FC<SettingsDropdownProps> = ({ bookKey }) => {
  const _ = useTranslation();
  const { envConfig } = useEnv();
  const { getView, getViewSettings, setViewSettings } = useReaderStore();
  const { themeMode, setThemeMode } = useThemeStore();
  const [isSettingsDropdownOpen, setIsSettingsDropdownOpen] = useState(false);

  const view = getView(bookKey);
  const viewSettings = getViewSettings(bookKey);
  const currentFontSize = viewSettings?.defaultFontSize || 16;

  const [fontSize, setFontSize] = useState(currentFontSize);
  const [isScrolledMode, setScrolledMode] = useState(viewSettings?.scrolled || false);
  const [isContinuousScroll, setIsContinuousScroll] = useState(viewSettings?.continuousScroll || false);
  const [columnMode, setColumnMode] = useState<"auto" | "one" | "two">((viewSettings?.columnMode as any) || "auto");

  // 计算当前选中的字体方案ID
  const currentFontId = viewSettings
    ? CURATED_FONTS.find(
        (font) =>
          font.serif === viewSettings.serifFont &&
          font.sansSerif === viewSettings.sansSerifFont &&
          font.cjk === viewSettings.defaultCJKFont,
      )?.id || "comfortable"
    : "comfortable";

  const [selectedFontId, setSelectedFontId] = useState(currentFontId);

  useEffect(() => {
    setFontSize(currentFontSize);
  }, [currentFontSize]);

  useEffect(() => {
    setSelectedFontId(currentFontId);
  }, [currentFontId]);

  useEffect(() => {
    setScrolledMode(viewSettings?.scrolled || false);
  }, [viewSettings?.scrolled]);

  useEffect(() => {
    setIsContinuousScroll(viewSettings?.continuousScroll || false);
  }, [viewSettings?.continuousScroll]);

  useEffect(() => {
    setColumnMode(((viewSettings?.columnMode as any) || "auto") as any);
  }, [viewSettings?.columnMode]);

  // Remove effect-driven updates to avoid loops; handle in explicit event handlers instead.

  // Same: avoid effect-driven loops for continuous mode.

  const handleToggleSettingsDropdown = (isOpen: boolean) => {
    setIsSettingsDropdownOpen(isOpen);
  };

  const applyScrolledMode = useCallback(
    (newScrolled: boolean) => {
      if (!viewSettings) return;
      setScrolledMode(newScrolled);
      const updated = { ...viewSettings, scrolled: newScrolled };
      setViewSettings(updated, bookKey);
      saveViewSettings(envConfig, bookKey, "scrolled", newScrolled);
      const applyNow = () => {
        if (view?.renderer) {
          const contents = view.renderer.getContents?.();
          const ready = Array.isArray(contents) && contents.length > 0 && contents[0]?.doc;
          if (!ready) {
            setTimeout(applyNow, 80);
            return;
          }
          view.renderer.setAttribute("flow", newScrolled ? "scrolled" : "paginated");
          view.renderer.setAttribute("max-inline-size", `${getMaxInlineSize(updated)}px`);
          view.renderer.setStyles?.(getStyles(updated));
        }
      };
      applyNow();
    },
    [bookKey, envConfig, view, setViewSettings, viewSettings],
  );

  const applyContinuousScroll = useCallback(
    (newVal: boolean) => {
      if (!viewSettings) return;
      setIsContinuousScroll(newVal);
      setViewSettings({ ...viewSettings, continuousScroll: newVal }, bookKey);
      saveViewSettings(envConfig, bookKey, "continuousScroll", newVal);
    },
    [bookKey, envConfig, setViewSettings, viewSettings],
  );

  const handleFontSizeChange = useCallback(
    (newSize: number) => {
      const clampedSize = Math.max(FONT_SIZE_MIN, Math.min(FONT_SIZE_MAX, newSize));
      setFontSize(clampedSize);
      if (viewSettings) setViewSettings({ ...viewSettings, defaultFontSize: clampedSize }, bookKey);
      saveViewSettings(envConfig, bookKey, "defaultFontSize", clampedSize);
    },
    [envConfig, bookKey, viewSettings, setViewSettings],
  );

  const handleFontChange = async (fontId: string) => {
    setSelectedFontId(fontId);
    const selectedFont = CURATED_FONTS.find((f) => f.id === fontId);
    if (selectedFont) {
      if (viewSettings)
        setViewSettings(
          {
            ...viewSettings,
            serifFont: selectedFont.serif,
            sansSerifFont: selectedFont.sansSerif,
            defaultCJKFont: selectedFont.cjk,
          },
          bookKey,
        );
      await saveViewSettings(envConfig, bookKey, "serifFont", selectedFont.serif);
      await saveViewSettings(envConfig, bookKey, "sansSerifFont", selectedFont.sansSerif);
      await saveViewSettings(envConfig, bookKey, "defaultCJKFont", selectedFont.cjk);
    }
  };

  const handleIncrease = () => {
    handleFontSizeChange(fontSize + FONT_SIZE_STEP);
  };

  const handleDecrease = () => {
    handleFontSizeChange(fontSize - FONT_SIZE_STEP);
  };

  const isCJK = isCJKEnv();

  const triggerViewerRelayout = useCallback(() => {
    try {
      window.dispatchEvent(new CustomEvent("foliate-resize-update", { detail: { bookIds: [bookKey] } }));
    } catch {}
  }, [bookKey]);

  const handleSetColumnMode = useCallback(
    async (mode: "auto" | "one" | "two") => {
      if (!viewSettings) return;
      setColumnMode(mode);
      setViewSettings({ ...viewSettings, columnMode: mode }, bookKey);
      await saveViewSettings(envConfig, bookKey, "columnMode" as any, mode as any, false, false);
      triggerViewerRelayout();
    },
    [bookKey, envConfig, triggerViewerRelayout, viewSettings, setViewSettings],
  );

  return (
    <DropdownMenu open={isSettingsDropdownOpen} onOpenChange={handleToggleSettingsDropdown}>
      <DropdownMenuTrigger asChild>
        <button
          className="btn btn-ghost flex h-8 min-h-8 w-8 items-center justify-center rounded-full p-0"
          title={_("Font Size Settings")}
        >
          <Settings2 size={18} />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-80 p-4" align="end" side="bottom" sideOffset={4}>
        <div className="space-y-5">
          <div>
            <div className="mb-3 font-medium text-sm">{_("Font Family")}</div>
            {(() => {
              const selected = CURATED_FONTS.find((f) => f.id === selectedFontId);
              const triggerFontFamily = selected ? (isCJK ? selected.cjk : selected.serif) : undefined;
              const triggerFontWeight = selected?.id === "classic" ? "normal" : (undefined as any);
              return (
                <Select value={selectedFontId} onValueChange={handleFontChange}>
                  <SelectTrigger
                    className="h-8 w-full focus:outline-none focus:ring-0"
                    style={{ fontFamily: triggerFontFamily, fontWeight: triggerFontWeight }}
                  >
                    <SelectValue placeholder={_("Select Font")} />
                  </SelectTrigger>
                  <SelectContent className="w-full dark:border-neutral-700 dark:bg-neutral-800">
                    {CURATED_FONTS.map((font) => (
                      <SelectItem key={font.id} value={font.id}>
                        <span
                          className="truncate"
                          style={{
                            fontFamily: isCJK ? font.cjk : font.serif,
                            fontWeight: font.id === "classic" ? "normal" : (undefined as any),
                          }}
                        >
                          {font.name}
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              );
            })()}
          </div>

          {/* 字体大小 */}
          <div>
            <div className="mb-3 font-medium text-sm">{_("Font Size")}</div>
            <div className="flex items-center justify-center gap-4">
              <button
                className="btn btn-sm size-8 cursor-pointer rounded-md border-none bg-neutral-100 hover:bg-neutral-200 disabled:bg-neutral-50 disabled:opacity-50 dark:bg-neutral-800 dark:disabled:bg-neutral-900 dark:disabled:opacity-50 dark:hover:bg-neutral-700"
                onClick={handleDecrease}
                disabled={fontSize <= FONT_SIZE_MIN}
                title={_("Decrease font size")}
              >
                <span className="font-medium text-xs">A</span>
              </button>

              <FontSizeSlider
                value={[fontSize]}
                onValueChange={(value: number[]) => handleFontSizeChange(value[0]!)}
                min={FONT_SIZE_MIN}
                max={FONT_SIZE_MAX}
                step={FONT_SIZE_STEP}
                showTooltip={true}
                tooltipContent={(value) => `${value}px`}
              />
              <button
                className="btn btn-sm size-8 cursor-pointer rounded-md border-none bg-neutral-100 hover:bg-neutral-200 disabled:bg-neutral-50 disabled:opacity-50 dark:bg-neutral-800 dark:disabled:bg-neutral-900 dark:disabled:opacity-50 dark:hover:bg-neutral-700"
                onClick={handleIncrease}
                disabled={fontSize >= FONT_SIZE_MAX}
                title={_("Increase font size")}
              >
                <span className="font-medium text-lg">A</span>
              </button>
            </div>
          </div>

          {/* 滚动模式 */}
          <div>
            <div className="mb-3 font-medium text-sm">{_("Reading Mode")}</div>
            <div className="space-y-3">
              <div className="flex items-center gap-4">
                <button
                  className={`btn btn-sm flex h-10 flex-1 items-center justify-between rounded-md border-none px-3 ${
                    !isScrolledMode
                      ? "bg-neutral-800 text-white hover:bg-neutral-700 dark:bg-neutral-100 dark:text-neutral-900 dark:hover:bg-neutral-200"
                      : "bg-neutral-100 hover:bg-neutral-200 dark:bg-neutral-800 dark:hover:bg-neutral-700"
                  }`}
                  onClick={() => applyScrolledMode(false)}
                  title={_("Paginated Mode")}
                >
                  <span className="text-sm">{_("Paginated")}</span>
                  {!isScrolledMode && <MdCheck size={16} />}
                </button>
                <button
                  className={`btn btn-sm flex h-10 flex-1 items-center justify-between rounded-md border-none px-3 ${
                    isScrolledMode
                      ? "bg-neutral-800 text-white hover:bg-neutral-700 dark:bg-neutral-100 dark:text-neutral-900 dark:hover:bg-neutral-200"
                      : "bg-neutral-100 hover:bg-neutral-200 dark:bg-neutral-800 dark:hover:bg-neutral-700"
                  }`}
                  onClick={() => applyScrolledMode(true)}
                  title={_("Scrolled Mode")}
                >
                  <span className="text-sm">{_("Scrolled")}</span>
                  {isScrolledMode && <MdCheck size={16} />}
                </button>
              </div>

              {isScrolledMode && (
                <div className="flex items-center justify-between rounded-md border border-neutral-200 bg-neutral-50 px-3 py-2 dark:border-neutral-700 dark:bg-neutral-800/50">
                  <label
                    htmlFor="continuous-scroll"
                    className="cursor-pointer text-neutral-700 text-sm dark:text-neutral-300"
                  >
                    {_("Cross-Chapter Scroll")}
                  </label>
                  <Checkbox
                    id="continuous-scroll"
                    checked={isContinuousScroll}
                    onCheckedChange={(checked) =>
                      applyContinuousScroll(checked === "indeterminate" ? false : Boolean(checked))
                    }
                  />
                </div>
              )}
            </div>
          </div>

          {/* 栏模式 */}
          {!isScrolledMode && (
            <div>
              <div className="mb-3 font-medium text-sm">{_("Columns")}</div>
              <div className="flex items-center gap-3">
                <button
                  className={`btn btn-sm flex size-8 items-center justify-center rounded-md border-none ${
                    columnMode === "auto"
                      ? "bg-neutral-800 text-white hover:bg-neutral-700 dark:bg-neutral-100 dark:text-neutral-900 dark:hover:bg-neutral-200"
                      : "bg-neutral-100 hover:bg-neutral-200 dark:bg-neutral-800 dark:hover:bg-neutral-700"
                  }`}
                  onClick={() => handleSetColumnMode("auto")}
                  title={_("Auto Columns")}
                >
                  <Wand2 size={16} />
                </button>
                <button
                  className={`btn btn-sm flex size-8 items-center justify-center rounded-md border-none ${
                    columnMode === "one"
                      ? "bg-neutral-800 text-white hover:bg-neutral-700 dark:bg-neutral-100 dark:text-neutral-900 dark:hover:bg-neutral-200"
                      : "bg-neutral-100 hover:bg-neutral-200 dark:bg-neutral-800 dark:hover:bg-neutral-700"
                  }`}
                  onClick={() => handleSetColumnMode("one")}
                  title={_("Single Column")}
                >
                  <PanelLeft size={16} />
                </button>
                <button
                  className={`btn btn-sm flex size-8 items-center justify-center rounded-md border-none ${
                    columnMode === "two"
                      ? "bg-neutral-800 text-white hover:bg-neutral-700 dark:bg-neutral-100 dark:text-neutral-900 dark:hover:bg-neutral-200"
                      : "bg-neutral-100 hover:bg-neutral-200 dark:bg-neutral-800 dark:hover:bg-neutral-700"
                  }`}
                  onClick={() => handleSetColumnMode("two")}
                  title={_("Two Columns")}
                >
                  <Columns size={16} />
                </button>
              </div>
            </div>
          )}

          {/* 主题模式 */}
          <div>
            <div className="mb-3 font-medium text-sm">{_("Theme Mode")}</div>
            <div className="flex items-center gap-4">
              <button
                className={`btn btn-sm flex size-8 items-center justify-center rounded-md border-none ${
                  themeMode === "auto"
                    ? "bg-neutral-800 text-white hover:bg-neutral-700 dark:bg-neutral-100 dark:text-neutral-900 dark:hover:bg-neutral-200"
                    : "bg-neutral-100 hover:bg-neutral-200 dark:bg-neutral-800 dark:hover:bg-neutral-700"
                }`}
                onClick={() => setThemeMode("auto")}
                title={_("Auto Mode")}
              >
                <TbSunMoon size={16} />
              </button>
              <button
                className={`btn btn-sm flex size-8 items-center justify-center rounded-md border-none ${
                  themeMode === "light"
                    ? "bg-neutral-800 text-white hover:bg-neutral-700 dark:bg-neutral-100 dark:text-neutral-900 dark:hover:bg-neutral-200"
                    : "bg-neutral-100 hover:bg-neutral-200 dark:bg-neutral-800 dark:hover:bg-neutral-700"
                }`}
                onClick={() => setThemeMode("light")}
                title={_("Light Mode")}
              >
                <MdOutlineLightMode size={16} />
              </button>
              <button
                className={`btn btn-sm flex size-8 items-center justify-center rounded-md border-none ${
                  themeMode === "dark"
                    ? "bg-neutral-800 text-white hover:bg-neutral-700 dark:bg-neutral-100 dark:text-neutral-900 dark:hover:bg-neutral-200"
                    : "bg-neutral-100 hover:bg-neutral-200 dark:bg-neutral-800 dark:hover:bg-neutral-700"
                }`}
                onClick={() => setThemeMode("dark")}
                title={_("Dark Mode")}
              >
                <MdOutlineDarkMode size={16} />
              </button>
            </div>
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default SettingsDropdown;
