import { useEnv } from "@/context/env-context";
import { getBooksWithStatus } from "@/services/book-service";
import { useBookSettingsStore } from "@/store/book-settings-store";
import { useLibraryStore } from "@/store/library-store";
import { useReaderStore } from "@/store/reader-store";
import { mountAdditionalFonts } from "@/utils/font";
import { getLocale } from "@/utils/misc";
import { initDayjs } from "@/utils/time";
import type * as React from "react";
import { Suspense, useEffect, useRef, useState } from "react";
import { convertBookWithStatusToBook } from "../utils/bookConvert";
import ReaderContent from "./reader-content";

const Reader: React.FC<{ id?: string }> = ({ id }) => {
  const { envConfig } = useEnv();
  const { setLibrary } = useLibraryStore();
  const { setSettings: setBookSettings } = useReaderStore();
  const { settings, setSettings } = useBookSettingsStore();
  const [libraryLoaded, setLibraryLoaded] = useState(false);
  const isInitiating = useRef(false);

  useEffect(() => {
    mountAdditionalFonts(document);
    initDayjs(getLocale());
  }, []);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (isInitiating.current) return;
    isInitiating.current = true;
    const initLibrary = async () => {
      try {
        const appService = await envConfig.getAppService();
        const settings = await appService.loadSettings();
        setSettings(settings);
        // 如果有指定的书籍 ID，将设置同步到新的 store
        if (id) {
          setBookSettings(settings, id);
        }
        const booksWithStatus = await getBooksWithStatus();
        const convertedBooks = await Promise.all(booksWithStatus.map(convertBookWithStatusToBook));
        setLibrary(convertedBooks);
        setLibraryLoaded(true);
      } catch (error) {
        console.error("Error loading library for reader:", error);
        setLibraryLoaded(true);
      }
    };

    initLibrary();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    libraryLoaded &&
    settings.globalReadSettings && (
      <div className="reader-page select-none overflow-hidden rounded-md text-base-content">
        <Suspense>
          <ReaderContent id={id} settings={settings} />
        </Suspense>
      </div>
    )
  );
};

export default Reader;
