import type { EnvConfigType } from "@/services/environment";
import { useBookSettingsStore } from "@/store/book-settings-store";
import { useReaderStore } from "@/store/reader-store";
import type { ViewSettings } from "@/types/book";
import { getStyles } from "@/utils/style";

export const saveViewSettings = async <K extends keyof ViewSettings>(
  envConfig: EnvConfigType,
  bookKey: string,
  key: K,
  value: ViewSettings[K],
  skipGlobal = false,
  applyStyles = true,
) => {
  const { settings, isFontLayoutSettingsGlobal, setSettings, saveSettings } = useBookSettingsStore.getState();
  const { getView, getViewSettings, setViewSettings, getConfig, saveConfig } = useReaderStore.getState();
  const viewSettings = getViewSettings(bookKey)!;
  const config = getConfig(bookKey)!;
  if (viewSettings[key] !== value) {
    viewSettings[key] = value;
    if (applyStyles) {
      const view = getView(bookKey);
      view?.renderer.setStyles?.(getStyles(viewSettings));
    }
  }
  setViewSettings(bookKey, viewSettings);

  if (isFontLayoutSettingsGlobal && !skipGlobal) {
    settings.globalViewSettings[key] = value;
    setSettings(settings);
  }
  await saveConfig(envConfig, bookKey, config, settings);
  await saveSettings(envConfig, settings);
};
