import { useEnv } from "@/context/env-context";
import { useReaderStore } from "@/store/reader-store";

const useBooksManager = () => {
  const { envConfig } = useEnv();
  const { tabs, openReader, removeTab, initBookData } = useReaderStore();

  // 从 tabs 中提取 bookKeys 以保持兼容性
  const bookKeys = tabs.map((tab) => tab.bookId);

  // Append a new book and sync with bookKeys and URL
  const appendBook = (id: string) => {
    // 使用新的 tab 系统打开书籍
    openReader(id);
    // 初始化书籍数据
    initBookData(envConfig, id);
  };

  // Close a book and sync with bookKeys and URL
  const dismissBook = (bookKey: string) => {
    // 使用新的 tab 系统关闭书籍
    const tabId = `reader-${bookKey}`;
    removeTab(tabId);
  };

  const getNextBookKey = (bookKey: string) => {
    const index = bookKeys.findIndex((key) => key === bookKey);
    const nextIndex = (index + 1) % bookKeys.length;
    return bookKeys[nextIndex]!;
  };

  return {
    bookKeys,
    appendBook,
    dismissBook,
    getNextBookKey,
  };
};

export default useBooksManager;
