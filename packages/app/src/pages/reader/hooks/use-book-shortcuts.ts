import useShortcuts from "@/hooks/use-shortcuts";
import { MAX_ZOOM_LEVEL, MIN_ZOOM_LEVEL, ZOOM_STEP } from "@/services/constants";
import { useBookSettingsStore } from "@/store/book-settings-store";
import { useReaderStore } from "@/store/reader-store";
import { eventDispatcher } from "@/utils/event";
import { getStyles } from "@/utils/style";
import { viewPagination } from "./use-pagination";

const useBookShortcuts = () => {
  const { getView, getViewSettings, setViewSettings, activeBookId } = useReaderStore();
  const { setFontLayoutSettingsDialogOpen } = useBookSettingsStore();

  const viewSettings = getViewSettings(activeBookId ?? "");
  const fontSize = viewSettings?.defaultFontSize ?? 16;
  const lineHeight = viewSettings?.lineHeight ?? 1.6;
  const distance = fontSize * lineHeight * 3;

  const toggleScrollMode = () => {
    const viewSettings = getViewSettings(activeBookId ?? "");
    if (viewSettings && activeBookId) {
      viewSettings.scrolled = !viewSettings.scrolled;
      setViewSettings(activeBookId!, viewSettings!);
      const flowMode = viewSettings.scrolled ? "scrolled" : "paginated";
      getView(activeBookId)?.renderer.setAttribute("flow", flowMode);
    }
  };

  const goLeft = () => {
    const viewSettings = getViewSettings(activeBookId ?? "");
    viewPagination(getView(activeBookId), viewSettings, "left");
  };

  const goRight = () => {
    const viewSettings = getViewSettings(activeBookId ?? "");
    viewPagination(getView(activeBookId), viewSettings, "right");
  };

  const goPrev = () => {
    getView(activeBookId)?.prev(distance);
  };

  const goNext = () => {
    getView(activeBookId)?.next(distance);
  };

  const goBack = () => {
    getView(activeBookId)?.history.back();
  };

  const goHalfPageDown = () => {
    const view = getView(activeBookId);
    const viewSettings = getViewSettings(activeBookId ?? "");
    if (view && viewSettings && viewSettings.scrolled) {
      view.next(view.renderer.size / 2);
    }
  };

  const goHalfPageUp = () => {
    const view = getView(activeBookId);
    const viewSettings = getViewSettings(activeBookId ?? "");
    if (view && viewSettings && viewSettings.scrolled) {
      view.prev(view.renderer.size / 2);
    }
  };

  const goForward = () => {
    getView(activeBookId)?.history.forward();
  };

  const reloadPage = () => {
    window.location.reload();
  };

  const showSearchBar = () => {
    eventDispatcher.dispatch("search", { term: "" });
  };

  const zoomIn = () => {
    if (!activeBookId) return;
    const view = getView(activeBookId);
    if (!view?.renderer?.setStyles) return;
    const viewSettings = getViewSettings(activeBookId)!;
    const zoomLevel = viewSettings!.zoomLevel + ZOOM_STEP;
    viewSettings!.zoomLevel = Math.min(zoomLevel, MAX_ZOOM_LEVEL);
    setViewSettings(activeBookId, viewSettings!);
    view?.renderer.setStyles?.(getStyles(viewSettings!));
  };

  const zoomOut = () => {
    if (!activeBookId) return;
    const view = getView(activeBookId);
    if (!view?.renderer?.setStyles) return;
    const viewSettings = getViewSettings(activeBookId)!;
    const zoomLevel = viewSettings!.zoomLevel - ZOOM_STEP;
    viewSettings!.zoomLevel = Math.max(zoomLevel, MIN_ZOOM_LEVEL);
    setViewSettings(activeBookId, viewSettings!);
    view?.renderer.setStyles?.(getStyles(viewSettings!));
  };

  const resetZoom = () => {
    if (!activeBookId) return;
    const view = getView(activeBookId);
    if (!view?.renderer?.setStyles) return;
    const viewSettings = getViewSettings(activeBookId)!;
    viewSettings!.zoomLevel = 100;
    setViewSettings(activeBookId, viewSettings!);
    view?.renderer.setStyles?.(getStyles(viewSettings!));
  };

  const toggleTTS = () => {
    if (!activeBookId) return;
    const bookKey = activeBookId;
    // 简化TTS切换逻辑，直接派发TTS开始事件
    eventDispatcher.dispatch("tts-speak", { bookKey });
  };

  useShortcuts(
    {
      onToggleScrollMode: toggleScrollMode,
      onOpenFontLayoutSettings: () => setFontLayoutSettingsDialogOpen(true),
      onToggleSearchBar: showSearchBar,
      onToggleTTS: toggleTTS,
      onReloadPage: reloadPage,
      onGoLeft: goLeft,
      onGoRight: goRight,
      onGoPrev: goPrev,
      onGoNext: goNext,
      onGoHalfPageDown: goHalfPageDown,
      onGoHalfPageUp: goHalfPageUp,
      onGoBack: goBack,
      onGoForward: goForward,
      onZoomIn: zoomIn,
      onZoomOut: zoomOut,
      onResetZoom: resetZoom,
    },
    [activeBookId],
  );
};

export default useBookShortcuts;
