import { useEnv } from "@/context/env-context";
import { useBookSettingsStore } from "@/store/book-settings-store";
import { useReaderStore } from "@/store/reader-store";
import { throttle } from "@/utils/throttle";
import { useCallback, useEffect } from "react";

export const useProgressAutoSave = (bookKey: string) => {
  const { envConfig } = useEnv();
  const { getConfig, saveConfig, getProgress } = useReaderStore();
  const progress = getProgress(bookKey);

  const immediateSaveConfig = useCallback(async () => {
    const config = getConfig(bookKey);
    if (!config) {
      console.warn("No config found for book:", bookKey);
      return;
    }

    const settings = useBookSettingsStore.getState().settings;
    await saveConfig(envConfig, bookKey, config, settings);
  }, [envConfig, bookKey, getConfig, saveConfig]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  const saveBookConfig = useCallback(
    throttle(async () => {
      const config = getConfig(bookKey);
      if (!config) {
        console.warn("No config found for book:", bookKey);
        return;
      }

      const settings = useBookSettingsStore.getState().settings;
      await saveConfig(envConfig, bookKey, config, settings);
    }, 5000),
    [envConfig, bookKey, getConfig, saveConfig],
  );

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    saveBookConfig();
    return () => {
      immediateSaveConfig().catch((error) => {
        console.error(`Failed to save progress on cleanup for book ${bookKey}:`, error);
      });
    };
  }, [progress, bookKey, saveBookConfig, immediateSaveConfig]);
};
