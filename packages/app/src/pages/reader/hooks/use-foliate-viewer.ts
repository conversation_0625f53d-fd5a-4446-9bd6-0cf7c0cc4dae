import { useEnv } from "@/context/env-context";
import { useUICSS } from "@/hooks/use-ui-css";
import { type BookDoc, getDirection } from "@/lib/document";
import { transformContent } from "@/services/transform-service";
import { useReaderStore } from "@/store/reader-store";
import { useThemeStore } from "@/store/theme-store";
import type { BookConfig } from "@/types/book";
import type { Insets } from "@/types/misc";
import { type FoliateView, wrappedFoliateView } from "@/types/view";
import { getBookDirFromLanguage, getBookDirFromWritingMode } from "@/utils/book";
import { getMaxInlineSize } from "@/utils/config";
import { mountAdditionalFonts } from "@/utils/font";
import { manageSyntaxHighlighting } from "@/utils/highlightjs";
import { isCJKLang } from "@/utils/lang";
import { getDirFromUILanguage } from "@/utils/rtl";
import {
  applyFixedlayoutStyles,
  applyImageStyle,
  applyTranslationStyle,
  getStyles,
  transformStylesheet,
} from "@/utils/style";
import { type RefObject, useCallback, useEffect, useMemo, useRef, useState } from "react";
import {
  handleClick,
  handleKeydown,
  handleMouseMove,
  handleMousedown,
  handleMouseup,
  handleTouchEnd,
  handleTouchMove,
  handleTouchStart,
  handleWheel,
} from "../utils/iframeEventHandlers";
import { useFoliateEvents } from "./use-foliate-events";
import { useMouseEvent } from "./use-iframe-events";
import { usePagination } from "./use-pagination";
import { useProgressAutoSave } from "./use-progress-auto-save";

export const useFoliateViewer = (bookKey: string, bookDoc: BookDoc, config: BookConfig, insets: Insets) => {
  const { getView, setView, getViewSettings, setViewSettings, setProgress, getBookData } = useReaderStore();

  const { appService } = useEnv();
  const { themeCode, isDarkMode } = useThemeStore();

  // 缓存viewSettings，避免重复计算
  const viewSettings = useMemo(() => {
    return getViewSettings(bookKey);
  }, [getViewSettings, bookKey]);

  const viewRef = useRef<FoliateView | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const isViewCreated = useRef(false);
  const [toastMessage, setToastMessage] = useState("");

  // expose styles for UI
  useUICSS(bookKey);
  useProgressAutoSave(bookKey);

  // dynamic container size
  const getContainerDimensions = useCallback(() => {
    if (!containerRef.current) {
      const viewWidth = appService?.isMobile ? screen.width : window.innerWidth;
      const viewHeight = appService?.isMobile ? screen.height : window.innerHeight;
      return { width: viewWidth - insets.left - insets.right, height: viewHeight - insets.top - insets.bottom };
    }
    const rect = containerRef.current.getBoundingClientRect();
    return { width: rect.width - insets.left - insets.right, height: rect.height - insets.top - insets.bottom };
  }, [appService?.isMobile, insets]);

  // Memoize layout calculations to avoid redundant computation
  const layoutCalculation = useMemo(() => {
    return (dimensions: { width: number; height: number }, viewSettings: any) => {
      const isVertical = !!viewSettings.vertical;
      const containerSize = isVertical ? dimensions.height : dimensions.width;

      // compute gap in px (same formula as paginator.js)
      const g = 1 / 100;
      const gapPx = (-g / (g - 1)) * containerSize;

      let computedMaxInlineSize = getMaxInlineSize(viewSettings);
      let computedMaxColumnCount = viewSettings.maxColumnCount ?? 2;
      const columnMode = viewSettings.columnMode ?? "auto";

      if (!viewSettings.scrolled) {
        if (columnMode === "one") {
          computedMaxColumnCount = 1;
          computedMaxInlineSize = Math.max(containerSize, 2000);
        } else if (columnMode === "two") {
          computedMaxColumnCount = 2;
          const target = Math.floor(containerSize / 2 - gapPx);
          computedMaxInlineSize = Math.max(120, target);
        }
      }

      return {
        maxColumnCount: computedMaxColumnCount,
        maxInlineSize: computedMaxInlineSize,
      };
    };
  }, []);

  // Cache styles to avoid regeneration on every update
  const cachedStyles = useMemo(() => {
    return viewSettings ? getStyles(viewSettings) : null;
  }, [viewSettings]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  const performUpdate = useCallback(
    (dimensions: { width: number; height: number }) => {
      try {
        if (!viewRef.current?.renderer) return;

        if (!viewSettings) return;

        // Use memoized calculation
        const { maxColumnCount, maxInlineSize } = layoutCalculation(dimensions, viewSettings);

        // Batch DOM updates to minimize reflows
        const renderer = viewRef.current.renderer;
        renderer.setAttribute("max-column-count", String(maxColumnCount));
        renderer.setAttribute("max-inline-size", `${maxInlineSize}px`);

        // Use cached styles if available
        if (cachedStyles) {
          renderer.setStyles?.(cachedStyles);
        }

        applyMarginAndGap();
      } catch (error) {
        console.error("Error in performUpdate:", error);
      }
    },
    [layoutCalculation, cachedStyles, getViewSettings, bookKey],
  );

  // Memoize stability checker configuration
  const stabilityConfig = useMemo(
    () => ({
      maxFrames: 15,
      requiredStableFrames: 5,
      initialDelay: 150,
      stableDelay: 150,
      timeoutDelay: 100,
    }),
    [],
  );

  // Optimize stability event dispatch
  const dispatchStableEvent = useCallback((bookKey: string) => {
    window.dispatchEvent(
      new CustomEvent("foliate-layout-stable", {
        detail: { bookIds: [bookKey] },
      }),
    );
  }, []);

  const checkLayoutStability = useCallback(
    (bookKey: string) => {
      const foliateView = document.getElementById(`foliate-view-${bookKey}`);
      if (!foliateView) {
        dispatchStableEvent(bookKey);
        return;
      }

      let frameCount = 0;
      let lastLayout: { scrollWidth: number; scrollHeight: number; childCount: number } | null = null;
      let stableFrames = 0;

      const checkFrame = () => {
        frameCount++;

        // Batch layout property reads to minimize DOM access
        const currentLayout = {
          scrollWidth: foliateView.scrollWidth,
          scrollHeight: foliateView.scrollHeight,
          childCount: foliateView.children.length,
        };

        if (lastLayout) {
          const isStable =
            currentLayout.scrollWidth === lastLayout.scrollWidth &&
            currentLayout.scrollHeight === lastLayout.scrollHeight &&
            currentLayout.childCount === lastLayout.childCount;

          if (isStable) {
            stableFrames++;

            if (stableFrames >= stabilityConfig.requiredStableFrames) {
              setTimeout(() => dispatchStableEvent(bookKey), stabilityConfig.stableDelay);
              return;
            }
          } else {
            stableFrames = 0;
          }
        }

        lastLayout = currentLayout;

        if (frameCount < stabilityConfig.maxFrames) {
          requestAnimationFrame(checkFrame);
        } else {
          setTimeout(() => dispatchStableEvent(bookKey), stabilityConfig.timeoutDelay);
        }
      };

      setTimeout(() => requestAnimationFrame(checkFrame), stabilityConfig.initialDelay);
    },
    [stabilityConfig, dispatchStableEvent],
  );

  const manualUpdate = useCallback(() => {
    const dimensions = getContainerDimensions();
    performUpdate(dimensions);

    // Start layout stability check
    checkLayoutStability(bookKey);
  }, [getContainerDimensions, performUpdate, bookKey, checkLayoutStability]);

  // Memoize event handler to prevent unnecessary re-registrations
  const handleFoliateResize = useCallback(
    (event: CustomEvent) => {
      const { bookIds } = event.detail;
      if (bookIds?.includes(bookKey)) {
        manualUpdate();
      }
    },
    [bookKey, manualUpdate],
  );

  useEffect(() => {
    window.addEventListener("foliate-resize-update", handleFoliateResize as EventListener);
    return () => {
      window.removeEventListener("foliate-resize-update", handleFoliateResize as EventListener);
    };
  }, [handleFoliateResize]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    const timer = setTimeout(() => setToastMessage(""), 2000);
    return () => clearTimeout(timer);
  }, [toastMessage]);

  const progressRelocateHandler = (event: Event) => {
    const detail = (event as CustomEvent).detail;
    // 只使用新的 store
    setProgress(
      {
        location: detail.cfi,
        sectionHref: detail.tocItem?.href || "",
        sectionLabel: detail.tocItem?.label || "",
        sectionId: detail.tocItem?.id ?? 0,
        section: detail.section,
        pageinfo: detail.location,
        timeinfo: detail.time,
        range: detail.range,
      },
      bookKey,
    );
  };

  const getDocTransformHandler = ({ width, height }: { width: number; height: number }) => {
    return (event: Event) => {
      const { detail } = event as CustomEvent;
      detail.data = Promise.resolve(detail.data)
        .then((data) => {
          if (viewSettings && detail.type === "text/css") return transformStylesheet(width, height, data);
          if (viewSettings && detail.type === "application/xhtml+xml") {
            const ctx = {
              bookKey,
              viewSettings,
              content: data,
              transformers: ["punctuation", "footnote"],
            };
            return Promise.resolve(transformContent(ctx));
          }
          return data;
        })
        .catch((e) => {
          console.error(new Error(`Failed to load ${detail.name}`, { cause: e }));
          return "";
        });
    };
  };

  const docLoadHandler = (event: Event) => {
    const detail = (event as CustomEvent).detail;
    if (detail.doc) {
      const writingDir = viewRef.current?.renderer.setStyles && getDirection(detail.doc);
      if (!viewSettings) return;
      const bookData = getBookData(bookKey);
      if (!bookData) return;

      const updatedViewSettings = {
        ...viewSettings,
        vertical: writingDir?.vertical || viewSettings.writingMode.includes("vertical") || false,
        rtl: writingDir?.rtl || getDirFromUILanguage() === "rtl" || viewSettings.writingMode.includes("rl") || false,
      };
      // 更新到新的 store
      setViewSettings(bookKey, updatedViewSettings);

      mountAdditionalFonts(detail.doc, isCJKLang(bookData.book?.primaryLanguage));

      if (bookDoc.rendition?.layout === "pre-paginated") applyFixedlayoutStyles(detail.doc, updatedViewSettings);

      applyImageStyle(detail.doc);

      if (updatedViewSettings.codeHighlighting) manageSyntaxHighlighting(detail.doc, updatedViewSettings);

      if (!detail.doc.isEventListenersAdded) {
        // listened events in iframes are posted to the main window
        // and then used by useMouseEvent and useTouchEvent
        // and more gesture events can be detected in the iframeEventHandlers
        detail.doc.isEventListenersAdded = true;
        detail.doc.addEventListener("keydown", handleKeydown.bind(null, bookKey));
        detail.doc.addEventListener("mousedown", handleMousedown.bind(null, bookKey));
        detail.doc.addEventListener("mouseup", handleMouseup.bind(null, bookKey));
        detail.doc.addEventListener("mousemove", handleMouseMove.bind(null, bookKey));
        detail.doc.addEventListener("click", handleClick.bind(null, bookKey));
        detail.doc.addEventListener("wheel", handleWheel.bind(null, bookKey));
        detail.doc.addEventListener("touchstart", handleTouchStart.bind(null, bookKey));
        detail.doc.addEventListener("touchmove", handleTouchMove.bind(null, bookKey));
        detail.doc.addEventListener("touchend", handleTouchEnd.bind(null, bookKey));
      }
    }
  };

  const docRelocateHandler = (event: Event) => {
    const detail = (event as CustomEvent).detail;
    if (detail.reason !== "scroll" && detail.reason !== "page") return;
  };

  const { handlePageFlip, handleContinuousScroll } = usePagination(
    bookKey,
    viewRef,
    containerRef as RefObject<HTMLDivElement>,
  );
  const mouseHandlers = useMouseEvent(bookKey, handlePageFlip, handleContinuousScroll);

  useFoliateEvents(viewRef.current, {
    onLoad: docLoadHandler,
    onRelocate: progressRelocateHandler,
    onRendererRelocate: docRelocateHandler,
  });

  // Clear search highlights on a single click inside the iframe for this book
  useEffect(() => {
    const handleIframeSingleClick = (event: MessageEvent) => {
      if (event?.data?.type === "iframe-single-click" && event?.data?.bookKey === bookKey) {
        try {
          getView(bookKey)?.clearSearch();
        } catch (e) {
          console.warn("Failed to clear search on single click:", e);
        }
      }
    };
    window.addEventListener("message", handleIframeSingleClick);
    return () => window.removeEventListener("message", handleIframeSingleClick);
  }, [bookKey, getView]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (isViewCreated.current) return;
    isViewCreated.current = true;

    const openBook = async () => {
      await import("foliate-js/view.js");
      const view = wrappedFoliateView(document.createElement("foliate-view") as FoliateView);
      view.id = `foliate-view-${bookKey}`;
      document.body.append(view);
      containerRef.current?.appendChild(view);

      if (!viewSettings) return;
      const writingMode = viewSettings.writingMode;
      if (writingMode) {
        const settingsDir = getBookDirFromWritingMode(writingMode);
        const languageDir = getBookDirFromLanguage(bookDoc.metadata.language);
        if (settingsDir !== "auto") bookDoc.dir = settingsDir;
        else if (languageDir !== "auto") bookDoc.dir = languageDir;
      }

      await view.open(bookDoc);
      viewRef.current = view;
      setView(view, bookKey);

      const { book } = view;
      book.transformTarget?.addEventListener("load", (event: Event) => {
        const { detail } = event as CustomEvent;
        if (detail.isScript) detail.allowScript = viewSettings.allowScript ?? false;
      });
      const { width, height } = getContainerDimensions();
      book.transformTarget?.addEventListener("data", getDocTransformHandler({ width, height }));
      view.renderer.setStyles?.(getStyles(viewSettings));
      applyTranslationStyle(viewSettings);

      const animated = viewSettings.animated!;
      const maxColumnCount = viewSettings.maxColumnCount!;
      const maxInlineSize = getMaxInlineSize(viewSettings);
      const maxBlockSize = viewSettings.maxBlockSize!;
      if (animated) view.renderer.setAttribute("animated", "");
      else view.renderer.removeAttribute("animated");
      view.renderer.setAttribute("max-column-count", maxColumnCount);
      view.renderer.setAttribute("max-inline-size", `${maxInlineSize}px`);
      view.renderer.setAttribute("max-block-size", `${maxBlockSize}px`);
      // view.renderer.setAttribute("gap", "4%");
      applyMarginAndGap();

      const lastLocation = config.location;
      if (lastLocation) await view.init({ lastLocation });
      else await view.goToFraction(0);

      manualUpdate();
    };

    openBook();

    return () => {
      if (viewRef.current) {
        try {
          const view = viewRef.current;
          viewRef.current = null;
          isViewCreated.current = false;
          setTimeout(() => {
            try {
              if (view.close) view.close();
              if (view.remove) view.remove();
            } catch (cleanupError) {
              console.warn("Error in delayed cleanup:", cleanupError);
            }
          }, 50);
        } catch (error) {
          console.warn("Error during foliate view cleanup:", error);
          viewRef.current = null;
          isViewCreated.current = false;
        }
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const applyMarginAndGap = () => {
    if (!viewSettings) return;
    if (viewSettings.scrolled) viewRef.current?.renderer.setAttribute("flow", "scrolled");
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (viewRef.current?.renderer) {
      if (!viewSettings) return;
      viewRef.current.renderer.setStyles?.(getStyles(viewSettings));
      if (bookDoc.rendition?.layout === "pre-paginated") {
        const docs = viewRef.current.renderer.getContents();
        docs.forEach(({ doc }) => applyFixedlayoutStyles(doc, viewSettings));
      }
    }
  }, [themeCode, isDarkMode, viewSettings, bookDoc.rendition?.layout]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (viewRef.current?.renderer && viewSettings) applyMarginAndGap();
  }, [insets.top, insets.right, insets.bottom, insets.left, viewSettings]);

  // Return the container ref and event handlers to attach in the component
  return { containerRef, mouseHandlers } as const;
};

export default useFoliateViewer;
