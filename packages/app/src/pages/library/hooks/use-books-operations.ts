import type { BookWithStatusAndUrls } from "@/types/simple-book";
import { useCallback } from "react";

export const useBooksOperations = (refreshBooks: () => Promise<void>) => {
  const handleBookDelete = useCallback(
    async (book: BookWithStatusAndUrls) => {
      try {
        const { deleteBook } = await import("@/services/book-service");
        await deleteBook(book.id);

        await refreshBooks();
        return true;
      } catch (error) {
        console.error("Failed to delete book:", error);
        return false;
      }
    },
    [refreshBooks],
  );

  const handleBookUpdate = useCallback(
    async (bookId: string, updateData: { title?: string; author?: string; coverPath?: string; tags?: string[] }) => {
      try {
        const { updateBook } = await import("@/services/book-service");
        await updateBook(bookId, updateData);

        await refreshBooks();
        return true;
      } catch (error) {
        console.error("Failed to update book:", error);
        return false;
      }
    },
    [refreshBooks],
  );

  return {
    handleBookDelete,
    handleBookUpdate,
  };
};
