import { Toaster } from "@/components/ui/sonner";
import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { HashRouter } from "react-router";
import Layout from "./components/layout.tsx";
import { EnvProvider } from "./context/env-context.tsx";
import { flushAllWrites } from "./lib/tauri-storage.ts";

import "./index.css";

window.addEventListener("beforeunload", () => {
  flushAllWrites().catch((error) => {
    console.error("Failed to flush writes on app close:", error);
  });
});

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <EnvProvider>
      <HashRouter>
        <Layout />
      </HashRouter>
      <Toaster position="top-center" />
    </EnvProvider>
  </StrictMode>,
);
