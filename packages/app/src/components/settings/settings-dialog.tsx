import { Collapsible, CollapsibleContent } from "@/components/ui/collapsible";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useTranslation } from "@/hooks/use-translation";
import { cn } from "@/lib/utils";
import { useProviderStore } from "@/store/provider-store";
import { ChevronRight, Cpu, Keyboard, Palette, Settings } from "lucide-react";
import { useState } from "react";
import AppearanceSettings from "./appearance";
import GeneralSettings from "./general";
import LlamaSettings from "./llama";
import ProviderDetailSettings from "./provider-detail";
import ProvidersSettings from "./providers";
import ShortcutsSettings from "./shortcuts";

interface SettingsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

type SettingsKey =
  | "general"
  | "appearance"
  | "llama"
  | "model-providers"
  | "shortcuts"
  | "provider-openai"
  | "provider-anthropic"
  | "provider-openrouter"
  | "provider-gemini"
  | "provider-deepseek"
  | "provider-grok";

interface Provider {
  id: string;
  name: string;
  icon: string;
  modelCount: number;
}

interface SettingsItem {
  key: SettingsKey;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  children?: SettingsItem[];
}

const providers: Provider[] = [
  { id: "openai", name: "OpenAI", icon: "/openai.svg", modelCount: 32 },
  { id: "anthropic", name: "Anthropic", icon: "/anthropic.svg", modelCount: 12 },
  { id: "openrouter", name: "OpenRouter", icon: "/openrouter.svg", modelCount: 2 },
  { id: "gemini", name: "Gemini", icon: "/gemini.svg", modelCount: 6 },
  { id: "grok", name: "Grok", icon: "/grok.svg", modelCount: 7 },
  { id: "deepseek", name: "DeepSeek", icon: "/deepseek.svg", modelCount: 6 },
];

export default function SettingsDialog({ open, onOpenChange }: SettingsDialogProps) {
  const [activeKey, setActiveKey] = useState<SettingsKey>("general");
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set(["model-providers"]));
  const { modelProviders } = useProviderStore();
  const _ = useTranslation();

  const settingsItems: SettingsItem[] = [
    { key: "general", label: _("General"), icon: Settings },
    { key: "appearance", label: _("Appearance"), icon: Palette },
    // Llama vector models above providers
    { key: "llama", label: "向量模型", icon: Cpu },
    {
      key: "model-providers",
      label: _("Model Providers"),
      icon: Cpu,
      children: providers.map((provider) => ({
        key: `provider-${provider.id}` as SettingsKey,
        label: provider.name,
        icon: () => (
          <div className="flex h-4 w-4 flex-shrink-0 items-center justify-center">
            {provider.icon.startsWith("/") ? (
              <img src={provider.icon} alt={provider.name} className="h-4 w-4 object-contain" />
            ) : (
              <span className="font-medium text-xs">{provider.icon}</span>
            )}
          </div>
        ),
      })),
    },
    { key: "shortcuts", label: _("Shortcuts"), icon: Keyboard },
  ];

  const getProviderStatus = (providerId: string) => {
    const provider = modelProviders.find((p) => p.provider === providerId);
    return provider?.active ?? false;
  };

  const toggleExpanded = (key: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(key)) {
      newExpanded.delete(key);
    } else {
      newExpanded.add(key);
    }
    setExpandedItems(newExpanded);
  };

  const renderSettingsContent = () => {
    switch (activeKey) {
      case "general":
        return <GeneralSettings />;
      case "llama":
        return <LlamaSettings />;
      case "model-providers":
        return (
          <ProvidersSettings onProviderSelect={(providerId) => setActiveKey(`provider-${providerId}` as SettingsKey)} />
        );
      case "appearance":
        return <AppearanceSettings />;
      case "shortcuts":
        return <ShortcutsSettings />;
      default:
        if (activeKey.startsWith("provider-")) {
          const providerId = activeKey.replace("provider-", "");
          return <ProviderDetailSettings providerId={providerId} onBack={() => setActiveKey("model-providers")} />;
        }
        return <GeneralSettings />;
    }
  };

  const renderSidebarItem = (item: SettingsItem, level = 0) => {
    const Icon = item.icon;
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.has(item.key);
    const isActive = activeKey === item.key;

    return (
      <div key={item.key}>
        {hasChildren ? (
          <Collapsible open={isExpanded}>
            <button
              onClick={() => setActiveKey(item.key)}
              className={cn(
                "flex w-full items-center justify-between gap-2 rounded-sm p-2 py-1.5 text-left text-neutral-700 text-sm transition-colors focus:outline-0",
                level === 0 ? "" : "ml-4",
                isActive ? "bg-muted dark:text-neutral-100" : "hover:bg-muted dark:text-neutral-300",
              )}
            >
              <div className="flex items-center gap-2">
                <Icon className="h-4 w-4 flex-shrink-0" />
                <span className="truncate text-sm">{item.label}</span>
              </div>
              <ChevronRight
                onClick={(e) => {
                  e.stopPropagation();
                  toggleExpanded(item.key);
                }}
                className={cn("h-4 w-4 flex-shrink-0 transition-transform", isExpanded && "rotate-90")}
              />
            </button>
            <CollapsibleContent className="mt-1 space-y-1">
              {item.children?.map((child) => renderSidebarItem(child, level + 1))}
            </CollapsibleContent>
          </Collapsible>
        ) : (
          <button
            onClick={() => setActiveKey(item.key)}
            className={cn(
              "flex w-full items-center gap-2 rounded-sm p-2 py-1.5 text-left text-neutral-700 text-sm transition-colors focus:outline-0",
              level === 0 ? "" : "ml-4 w-[91%]",
              isActive ? "bg-muted dark:text-neutral-100" : "hover:bg-muted dark:text-neutral-300",
            )}
          >
            <Icon className="h-4 w-4 flex-shrink-0" />
            <span className="truncate text-sm">{item.label}</span>
            {item.key.startsWith("provider-") && (
              <div
                className={cn(
                  "mr-1 ml-auto h-2 w-2 flex-shrink-0 rounded-full",
                  getProviderStatus(item.key.replace("provider-", "")) ? "bg-green-500" : "bg-red-500",
                )}
              />
            )}
          </button>
        )}
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="flex max-h-[80vh] min-h-[80vh] min-w-[800px] max-w-[800px] flex-col gap-0 overflow-y-auto bg-background p-0 dark:border-neutral-700">
        <DialogHeader className="flex-shrink-0 border-neutral-200 border-b px-6 py-4 dark:border-neutral-800 dark:bg-neutral-900">
          <DialogTitle className="dark:text-neutral-100">{_("Settings")}</DialogTitle>
        </DialogHeader>

        <div className="flex min-h-0 flex-1 dark:bg-neutral-900">
          <div className="w-52 flex-shrink-0 overflow-y-auto border-neutral-200 border-r p-4 px-2 dark:border-neutral-800 dark:bg-neutral-900">
            <nav className="space-y-1">{settingsItems.map((item) => renderSidebarItem(item))}</nav>
          </div>

          <div className="min-w-0 flex-1 overflow-y-auto dark:bg-neutral-900">{renderSettingsContent()}</div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
