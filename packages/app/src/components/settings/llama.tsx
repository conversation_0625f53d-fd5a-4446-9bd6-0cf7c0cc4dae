import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useLlamaStore } from "@/store/llama-store";
import { useEffect } from "react";
import { LlamaServerManager, LlamacppClient } from "./llama-client";
import VectorModelManager from "./vector-model-manager";

export default function LlamaSettings() {
  const {
    // 原有的 Llama.cpp 服务器相关状态
    serverStatus,
    currentSession,
    modelPath,
    testText,
    setServerStatus,
    setCurrentSession,
    setModelPath,
    setTestText,
  } = useLlamaStore();

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    (async () => {
      if (!currentSession) {
        try {
          const client = new LlamacppClient();
          const sessions = await client.getAllSessions();
          if (sessions && sessions.length > 0) {
            setCurrentSession(sessions[0]);
            setServerStatus(`检测到已运行的服务器 | PID: ${sessions[0].pid} | Port: ${sessions[0].port}`);
          }
        } catch {}
      }
    })();
  }, []);

  async function startServer() {
    setServerStatus("正在检测系统并准备后端…");
    try {
      const serverManager = new LlamaServerManager();

      setServerStatus("正在创建应用数据目录结构…");
      await new Promise((r) => setTimeout(r, 200));

      setServerStatus("检查并下载 llama-server（首次运行需下载）…");
      await new Promise((r) => setTimeout(r, 200));

      setServerStatus("启动 Embedding 服务器…");
      const session = await serverManager.startEmbeddingServer(modelPath);
      setCurrentSession(session);
      setServerStatus(`服务器启动成功 | PID: ${session.pid} | Port: ${session.port}`);
    } catch (error) {
      const msg = error instanceof Error ? error.message : String(error);
      if (msg.includes("Binary not found") || msg.includes("下载失败")) {
        setServerStatus("启动失败：后端下载失败，请检查网络或手动安装 llama.cpp");
      } else if (msg.includes("Model file not found")) {
        setServerStatus(`启动失败：模型文件未找到，请检查路径：${modelPath}`);
      } else {
        setServerStatus(`启动失败：${msg}`);
      }
      console.error("启动服务器失败:", error);
    }
  }

  async function testEmbedding() {
    if (!currentSession) {
      setServerStatus("请先启动服务器");
      return;
    }
    setServerStatus("测试 embedding…");
    try {
      const res = await fetch(`http://127.0.0.1:${currentSession.port}/v1/embeddings`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${currentSession.api_key}`,
        },
        body: JSON.stringify({
          input: [testText],
          model: currentSession.model_id,
          encoding_format: "float",
        }),
      });
      if (!res.ok) throw new Error(`HTTP ${res.status}: ${res.statusText}`);
      const json = await res.json();
      const len = json?.data?.[0]?.embedding?.length ?? 0;
      setServerStatus(`Embedding 测试成功 | 维度: ${len}`);
    } catch (error) {
      setServerStatus(`测试失败：${error}` as string);
      console.error("Embedding 测试失败:", error);
    }
  }

  async function stopServer() {
    if (!currentSession) {
      setServerStatus("没有运行中的服务器");
      return;
    }
    setServerStatus("正在停止服务器…");
    try {
      const serverManager = new LlamaServerManager();
      await serverManager.stopServer(currentSession);
      setCurrentSession(null);
      setServerStatus("服务器已停止");
    } catch (error) {
      setServerStatus(`停止失败：${error}` as string);
      console.error("停止服务器失败:", error);
    }
  }

  const labelClass = "block mb-2 font-medium text-neutral-800 dark:text-neutral-200";

  return (
    <div className="space-y-8 p-4 text-neutral-800 dark:text-neutral-100">
      {/* 使用新的 VectorModelManager 组件 */}
      <VectorModelManager />
      {/* 分割线 */}
      <div className="flex items-center justify-center">
        <div className="flex-grow border-neutral-300 border-t dark:border-neutral-600" />
        <span className="mx-4 font-medium text-neutral-500 text-sm dark:text-neutral-400">or</span>
        <div className="flex-grow border-neutral-300 border-t dark:border-neutral-600" />
      </div>
      <section className="rounded-lg bg-muted p-4 dark:bg-neutral-800/90">
        <div className="mb-4 flex items-center justify-between">
          <h2 className="font-semibold text-lg">Llama.cpp 服务器控制</h2>
          {currentSession ? (
            <span className="rounded-md border border-green-200 bg-green-200 px-2 py-1 font-medium text-green-800 text-xs dark:border-green-900/40 dark:bg-green-900/40 dark:text-green-300">
              运行中 · PID {currentSession.pid} · 端口 {currentSession.port}
            </span>
          ) : null}
        </div>

        <div className="space-y-4">
          <div>
            <div className={labelClass}>模型</div>
            <Select value={modelPath} onValueChange={(val) => setModelPath(val)}>
              <SelectTrigger className="h-8 w-full">
                <SelectValue placeholder="选择模型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="/Users/<USER>/Downloads/Qwen3-Embedding-0.6B-f16.gguf">
                  Qwen3-Embedding-0.6B-f16
                </SelectItem>
                <SelectItem value="/Users/<USER>/Downloads/bge-m3-FP16.gguf">Bge-m3-FP16</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <div className={labelClass}>测试文本</div>
            <Input
              type="text"
              value={testText}
              onChange={(e) => setTestText(e.target.value)}
              placeholder="输入要测试的文本…"
            />
          </div>

          <div className="flex flex-wrap gap-2">
            <Button onClick={startServer} disabled={currentSession !== null} variant="default" size="sm">
              启动服务器
            </Button>
            <Button onClick={testEmbedding} disabled={currentSession === null} size="sm">
              测试 Embedding
            </Button>
            <Button onClick={stopServer} disabled={currentSession === null} variant="outline" size="sm">
              停止服务器
            </Button>
          </div>

          <div className="rounded-lg border border-neutral-200 bg-neutral-50 p-3 text-sm dark:border-neutral-800 dark:bg-neutral-900">
            <strong>状态：</strong> {serverStatus || "等待操作…"}
          </div>

          {currentSession && (
            <div className="rounded-lg border border-neutral-200 bg-neutral-50 p-3 text-sm dark:border-neutral-800 dark:bg-neutral-900">
              <div className="mb-2 font-medium">服务器信息</div>
              <div>进程 ID: {currentSession.pid}</div>
              <div>端口: {currentSession.port}</div>
              <div>API 端点: http://127.0.0.1:{currentSession.port}/v1/embeddings</div>
            </div>
          )}
        </div>
      </section>
    </div>
  );
}
