import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { useTranslation } from "@/hooks/use-translation";
import { cn } from "@/lib/utils";
import { useProviderStore } from "@/store/provider-store";
import { Plus, Settings } from "lucide-react";

interface ProvidersSettingsProps {
  onProviderSelect?: (providerId: string) => void;
}

export default function ProvidersSettings({ onProviderSelect }: ProvidersSettingsProps) {
  const { modelProviders, setModelProviders } = useProviderStore();
  const _ = useTranslation();

  const toggleProviderEnabled = (providerId: string) => {
    const updatedProviders = modelProviders.map((provider) =>
      provider.provider === providerId ? { ...provider, active: !provider.active } : provider,
    );
    setModelProviders(updatedProviders);
  };

  return (
    <div className="p-4">
      <div className="rounded-lg bg-muted p-4 dark:bg-neutral-800/90">
        <div className="mb-4 flex items-center justify-between">
          <h2 className="text dark:text-neutral-200">{_("Model Providers")}</h2>
          <Button size="sm" className="rounded-sm text-xs">
            <Plus className="h-4 w-4" />
            {_("Add Provider")}
          </Button>
        </div>

        <div className="space-y-3">
          {modelProviders.map((provider, index) => {
            const providerName = provider.name;
            const providerIcon = provider.icon;
            const modelCount = provider.models.length;

            return (
              <div
                key={provider.provider}
                className={cn("pt-3", index === 0 ? "" : "border-neutral-200/50 border-t dark:border-neutral-700")}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {providerIcon && <img src={providerIcon} alt={providerName} className="h-6 w-6" />}
                    <div>
                      <span className="text-sm dark:text-neutral-200">{providerName}</span>
                      <p className="text-gray-600 text-xs dark:text-neutral-400">
                        {modelCount === 1 ? _("Model") : _("{{count}} Models", { count: modelCount })}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      onClick={() => onProviderSelect?.(provider.provider)}
                    >
                      <Settings className="h-4 w-4" />
                    </Button>
                    <Switch
                      checked={provider.active}
                      onCheckedChange={() => toggleProviderEnabled(provider.provider)}
                    />
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
