import { Switch } from "@/components/ui/switch";
import { useTranslation } from "@/hooks/use-translation";
import { fetchModelsFromProvider } from "@/services/provider-service";
import { useProviderStore } from "@/store/provider-store";
import { throttle } from "@/utils/throttle";
import { useMemo, useState } from "react";
import ApiConfigSection from "./api-config-section";
import ModelsManagement from "./models-management";

interface ProviderDetailSettingsProps {
  providerId: string;
  onBack: () => void;
}

export default function ProviderDetailSettings({ providerId }: ProviderDetailSettingsProps) {
  const { modelProviders, updateProvider } = useProviderStore();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [refreshError, setRefreshError] = useState<string | null>(null);
  const _ = useTranslation();

  const provider = modelProviders.find((p) => p.provider === providerId);

  const throttledUpdate = useMemo(
    () =>
      throttle((field: string, value: any) => {
        updateProvider(providerId, { [field]: value });
      }, 200),
    [providerId, updateProvider],
  );

  const handleFieldChange = (field: string, value: any) => {
    throttledUpdate(field, value);
  };

  const handleAddModel = (newModel: Omit<Model, "active" | "description" | "capabilities" | "manual">) => {
    if (!provider) return;

    const modelWithDefaults = {
      ...newModel,
      active: true,
      description: "",
      capabilities: [],
      manual: true,
    };

    updateProvider(providerId, {
      models: [...provider.models, modelWithDefaults],
    });
  };

  const handleClearAllModels = () => {
    if (provider) {
      updateProvider(providerId, {
        models: [],
      });
    }
  };

  const handleUpdateModel = (index: number, field: keyof Model, value: any) => {
    if (provider) {
      const updatedModels = provider.models.map((model, i) => (i === index ? { ...model, [field]: value } : model));
      updateProvider(providerId, {
        models: updatedModels,
      });
    }
  };

  const handleRemoveModel = (index: number) => {
    if (provider) {
      updateProvider(providerId, {
        models: provider.models.filter((_, i) => i !== index),
      });
    }
  };

  const handleRefreshModels = async () => {
    if (!provider || !provider.baseUrl) {
      setRefreshError(_("Please configure the base URL first"));
      return;
    }

    setIsRefreshing(true);
    setRefreshError(null);

    try {
      const modelIds = await fetchModelsFromProvider({
        ...provider,
        apiKey: provider.apiKey,
        baseUrl: provider.baseUrl,
      });

      const newModels: Model[] = modelIds.map((id) => ({
        id,
        name: id,
        active: providerId !== "openrouter",
        description: "",
        capabilities: [],
        manual: false,
      }));

      const existingManualModels = provider.models.filter((model) => model.manual === true);

      const updatedModels = [...newModels, ...existingManualModels];

      updateProvider(providerId, {
        models: updatedModels,
      });
    } catch (error) {
      console.error("Failed to refresh models:", error);
      setRefreshError(error instanceof Error ? error.message : "Failed to fetch models from provider");
    } finally {
      setIsRefreshing(false);
    }
  };

  if (!provider) {
    return (
      <div className="p-4">
        <div className="text-center text-gray-500 dark:text-neutral-400">{_("Provider not found")}</div>
      </div>
    );
  }

  const providerName = provider.name;
  const providerIcon = provider.icon;

  return (
    <div className="p-4">
      <div className="mb-4 flex items-center justify-between gap-2">
        <div className="flex items-center gap-2">
          {providerIcon && <img src={providerIcon} alt={providerName} className="h-4 w-4" />}
          <span className="text-sm dark:text-neutral-200">{providerName}</span>
        </div>
        <Switch
          checked={provider?.active ?? true}
          onCheckedChange={(checked) => handleFieldChange("active", checked)}
        />
      </div>
      <div className="space-y-6">
        <ApiConfigSection provider={provider} onFieldChange={handleFieldChange} />
        <ModelsManagement
          provider={provider}
          isRefreshing={isRefreshing}
          refreshError={refreshError}
          onRefreshModels={handleRefreshModels}
          onUpdateModel={handleUpdateModel}
          onRemoveModel={handleRemoveModel}
          onAddModel={handleAddModel}
          onClearAllModels={handleClearAllModels}
        />
      </div>
    </div>
  );
}
