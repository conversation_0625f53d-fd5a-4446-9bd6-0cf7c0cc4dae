import { Button } from "@/components/ui/button";
import { getThreadsByBookKey } from "@/services/thread-service";
import type { ThreadSummary } from "@/types/thread";
import { ArrowLeft, MessageCircle } from "lucide-react";
import { useEffect, useState } from "react";

interface ChatThreadsProps {
  bookKey: string;
  onBack: () => void;
  onSelectThread: (threadSummary: ThreadSummary) => void;
}

export function ChatThreads({ bookKey, onBack, onSelectThread }: ChatThreadsProps) {
  const [threads, setThreads] = useState<ThreadSummary[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadThreads = async () => {
      try {
        setLoading(true);
        setError(null);
        const threadList = await getThreadsByBookKey(bookKey);
        setThreads(threadList);
      } catch (err) {
        console.error("Failed to load threads:", err);
        setError("加载历史对话失败");
      } finally {
        setLoading(false);
      }
    };

    if (bookKey) {
      loadThreads();
    }
  }, [bookKey]);

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp * 1000);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return "今天";
    }
    if (diffDays === 2) {
      return "昨天";
    }
    if (diffDays <= 7) {
      return `${diffDays - 1} 天前`;
    }
    return date.toLocaleDateString("zh-CN", {
      month: "short",
      day: "numeric",
    });
  };

  if (loading) {
    return (
      <div className="flex h-full flex-col">
        <div className="flex h-8 items-center gap-2 border-neutral-300 pl-0.5 dark:border-neutral-700">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 rounded-full hover:bg-neutral-200 dark:hover:bg-neutral-700"
            onClick={onBack}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h2 className="font-medium text-neutral-900 dark:text-neutral-100">历史对话</h2>
        </div>
        <div className="flex flex-1 items-center justify-center">
          <div className="text-neutral-600 dark:text-neutral-400">加载中...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-full flex-col">
        <div className="flex h-8 items-center gap-2 border-neutral-300 pl-0.5 dark:border-neutral-700">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 rounded-full hover:bg-neutral-200 dark:hover:bg-neutral-700"
            onClick={onBack}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h2 className="font-medium text-neutral-900 dark:text-neutral-100">历史对话</h2>
        </div>
        <div className="flex flex-1 items-center justify-center">
          <div className="text-center">
            <div className="mb-2 text-neutral-600 dark:text-neutral-400">{error}</div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.location.reload()}
              className="border-neutral-200 dark:border-neutral-700"
            >
              重试
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col">
      <div className="flex-shrink-0 border-neutral-300 dark:border-neutral-700">
        <div className="flex h-10 items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            className="size-7 rounded-full hover:bg-neutral-200 dark:hover:bg-neutral-700"
            onClick={onBack}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h2 className="font-medium text-neutral-900 text-sm dark:text-neutral-100">历史对话</h2>
          <span className="text-neutral-500 text-xs dark:text-neutral-500">({threads.length})</span>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto px-2 pb-8">
        {threads.length === 0 ? (
          <div className="flex h-full items-center justify-center">
            <div className="text-center">
              <div className="mx-auto mb-3 w-fit rounded-full bg-neutral-100 p-3 dark:bg-neutral-800">
                <MessageCircle size={24} className="text-neutral-500 dark:text-neutral-500" />
              </div>
              <p className="text-neutral-600 text-sm dark:text-neutral-400">还没有历史对话</p>
              <p className="mt-1 text-neutral-500 text-xs dark:text-neutral-500">开始聊天来创建你的第一个对话</p>
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            {threads.map((thread) => (
              <button
                key={thread.id}
                onClick={() => onSelectThread(thread)}
                className="w-full cursor-pointer rounded-lg border border-neutral-200 p-2 text-left"
              >
                <div className="mb-1 flex items-start justify-between gap-2">
                  <h3 className="line-clamp-1 flex-1 font-medium text-neutral-900 text-sm dark:text-neutral-100">
                    {thread.title || "未命名对话"}
                  </h3>
                  <span className="flex-shrink-0 text-neutral-500 text-xs dark:text-neutral-500">
                    {formatDate(thread.updated_at)}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-neutral-600 text-xs dark:text-neutral-400">{thread.message_count} 条消息</span>
                </div>
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
