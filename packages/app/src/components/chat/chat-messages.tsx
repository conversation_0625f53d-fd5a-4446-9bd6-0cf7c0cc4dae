import { <PERSON><PERSON><PERSON><PERSON>r<PERSON><PERSON>nt, ChatContainerScrollAnchor } from "@/components/prompt-kit/chat-container";
import { Message, MessageAction, MessageActions, MessageContent } from "@/components/prompt-kit/message";
import { Reasoning, ReasoningContent, ReasoningTrigger } from "@/components/prompt-kit/reasoning";
import { Tool } from "@/components/prompt-kit/tool";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import type { UIMessage, UIMessagePart } from "ai";
import dayjs from "dayjs";
import { Brain, Copy, Pencil, ThumbsDown, ThumbsUp, Trash } from "lucide-react";
import { useEffect } from "react";
import { useStickToBottomContext } from "use-stick-to-bottom";

interface ChatMessagesProps {
  messages: any[];
  status: string;
  error: any;
  autoScroll?: boolean;
  scrollKey?: string | number;
}

export function reorderTextAndReasoning(message: UIMessage): UIMessage {
  const srcParts = Array.isArray(message?.parts) ? message.parts : [];
  const cloned = srcParts.map((p) => ({ ...p }));
  const reordered: UIMessagePart<any, any>[] = [];

  for (let i = 0; i < cloned.length; i++) {
    const a = cloned[i];
    const b = cloned[i + 1];

    if (a?.type === "text" && b?.type === "reasoning") {
      reordered.push(b, a);
      i++;
    } else {
      reordered.push(a);
    }
  }

  return { ...message, parts: reordered };
}

export function ChatMessages({ messages, status, error, autoScroll = true, scrollKey }: ChatMessagesProps) {
  const { scrollToBottom } = useStickToBottomContext();
  const lastMessage = messages[messages.length - 1];
  const reasoningPart = lastMessage?.parts?.find((part: UIMessagePart<any, any>) => part.type === "reasoning");
  const hasReasoningInLastMessage = !!reasoningPart;
  const isStreaming = status === "streaming";

  const lastPart = lastMessage?.parts?.[lastMessage.parts.length - 1];
  const reasoningActive = isStreaming && hasReasoningInLastMessage && lastPart?.type === "reasoning";

  useEffect(() => {
    if (!autoScroll) return;
    if (messages.length > 0) {
      setTimeout(() => {
        scrollToBottom("instant");
      }, 100);
    }
  }, [messages.length, scrollToBottom, autoScroll]);

  useEffect(() => {
    if (scrollKey === undefined) return;
    setTimeout(() => {
      scrollToBottom("instant");
    }, 50);
  }, [scrollKey, scrollToBottom]);

  const renderMessageParts = (parts: any[], isLastMessage: boolean) => {
    const elements: any[] = [];
    let textBuffer = "";

    const flushText = () => {
      if (!textBuffer) return;
      elements.push(
        <MessageContent
          key={`text-${elements.length}`}
          className="prose prose-neutral flex-1 rounded bg-transparent p-0 text-foreground"
          markdown
        >
          {textBuffer}
        </MessageContent>,
      );
      textBuffer = "";
    };

    const reasoningStreaming = isLastMessage && reasoningActive;
    const lastReasoningIndex = parts.reduce((lastIndex, part, index) => {
      return part?.type === "reasoning" ? index : lastIndex;
    }, -1);

    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];
      const type = part?.type as string | undefined;

      if (type === "text") {
        textBuffer += part.text ?? "";
        continue;
      }

      if (type === "reasoning") {
        flushText();
        const isCurrentlyStreaming = reasoningStreaming && i === lastReasoningIndex;
        console.log("isCurrentlyStreaming", isCurrentlyStreaming);
        let displayTime: number | undefined;
        const showTimer = displayTime !== undefined;

        elements.push(
          <Reasoning key={`reasoning-${i}`} isStreaming={isCurrentlyStreaming}>
            <ReasoningTrigger className="flex items-center gap-1 text-muted-foreground">
              <div className="flex items-center gap-1 text-muted-foreground">
                <Brain className="h-4 w-4" />
                <span className="text-sm">{isCurrentlyStreaming ? "Thinking..." : ""}</span>
                {showTimer && (
                  <div className="flex items-center gap-1 text-muted-foreground text-sm">
                    <span>
                      {isCurrentlyStreaming
                        ? `${displayTime}s`
                        : `Thought for ${displayTime} second${displayTime === 1 ? "" : "s"}`}
                    </span>
                  </div>
                )}
              </div>
            </ReasoningTrigger>
            <ReasoningContent
              className="ml-2 border-l-2 border-l-neutral-300 px-2 pl-4 dark:border-l-neutral-600"
              markdown
            >
              {part.text || ""}
            </ReasoningContent>
          </Reasoning>,
        );
        continue;
      }

      if (typeof type === "string" && type.startsWith("tool-")) {
        flushText();
        const toolType = type.replace(/^tool-/, "");
        elements.push(
          <Tool
            key={`tool-${i}`}
            className="w-full"
            toolPart={{
              type: toolType,
              state: part.state ?? "output-available",
              input: part.input,
              output: part.output,
              toolCallId: part.toolCallId,
              errorText: part.errorText,
            }}
          />,
        );
        continue;
      }

      flushText();
    }

    flushText();

    return <div className="flex flex-col gap-1">{elements}</div>;
  };

  return (
    <ChatContainerContent className="select-auto space-y-2 px-2 py-6">
      {error && (
        <div className="mx-auto max-w-3xl px-6">
          <div className="rounded-lg border border-red-200 bg-red-50 p-3 text-red-800 text-sm dark:border-red-800 dark:bg-red-900/20 dark:text-red-200">
            错误: {error.message}
          </div>
        </div>
      )}

      {messages.map((message, index) => {
        const isAssistant = message.role === "assistant";
        const isLastMessage = index === messages.length - 1;
        const isStreaming = status === "streaming";

        const reorderedMessage = reorderTextAndReasoning(message);

        return (
          <Message
            key={message.id}
            className={cn(
              "mx-auto flex w-full max-w-3xl flex-col gap-2 px-2",
              isAssistant ? "items-start" : "items-end",
            )}
          >
            {isAssistant ? (
              <div className="group flex w-full flex-col gap-0">
                {renderMessageParts(reorderedMessage.parts, isLastMessage)}
                {((!isStreaming && isLastMessage) || !isLastMessage) && (
                  <div className="flex items-center justify-between">
                    <MessageActions
                      className={cn(
                        "-ml-2.5 flex transform-gpu gap-0 opacity-0 transition-opacity duration-150 group-hover:opacity-100",
                        isLastMessage && "opacity-100",
                      )}
                    >
                      <MessageAction tooltip="复制" delayDuration={100}>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="size-7 rounded-full"
                          onClick={() => {
                            navigator.clipboard.writeText(
                              message.parts.map((part: any) => (part.type === "text" ? part.text : "")).join(""),
                            );
                          }}
                        >
                          <Copy size={10} />
                        </Button>
                      </MessageAction>
                      <MessageAction tooltip="点赞" delayDuration={100}>
                        <Button variant="ghost" size="icon" className="size-7 rounded-full">
                          <ThumbsUp size={12} />
                        </Button>
                      </MessageAction>
                      <MessageAction tooltip="点踩" delayDuration={100}>
                        <Button variant="ghost" size="icon" className="size-7 rounded-full">
                          <ThumbsDown size={12} />
                        </Button>
                      </MessageAction>
                    </MessageActions>
                    {message.metadata && (
                      <div className="flex items-center gap-2 text-neutral-500 text-xs opacity-0 transition-opacity duration-150 group-hover:opacity-100 dark:text-neutral-400">
                        {message.metadata.totalUsage && (
                          <span className="text-xs">{message.metadata.totalUsage.totalTokens} tokens</span>
                        )}
                        {message.metadata.updatedAt && (
                          <span className="text-xs">
                            {dayjs(message.metadata.updatedAt * 1000).format("YYYY-MM-DD HH:mm:ss")}
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            ) : (
              <div className="group flex w-full flex-col items-end gap-1">
                <MessageContent className="max-w-[85%] rounded-lg bg-muted px-2 text-base leading-5.5">
                  {reorderedMessage.parts.map((part: any) => (part.type === "text" ? part.text : "")).join("")}
                </MessageContent>
                <MessageActions
                  className={cn(
                    "flex transform-gpu gap-0 opacity-0 transition-opacity duration-150 group-hover:opacity-100",
                  )}
                >
                  <MessageAction tooltip="编辑" delayDuration={100}>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="size-7 rounded-full hover:bg-white dark:hover:bg-neutral-600"
                    >
                      <Pencil size={12} />
                    </Button>
                  </MessageAction>
                  <MessageAction tooltip="删除" delayDuration={100}>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="size-7 rounded-full hover:bg-white dark:hover:bg-neutral-600"
                    >
                      <Trash size={12} />
                    </Button>
                  </MessageAction>
                  <MessageAction tooltip="复制" delayDuration={100}>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="size-7 rounded-full hover:bg-white dark:hover:bg-neutral-600"
                      onClick={() => {
                        navigator.clipboard.writeText(
                          reorderedMessage.parts.map((part: any) => (part.type === "text" ? part.text : "")).join(""),
                        );
                      }}
                    >
                      <Copy size={12} />
                    </Button>
                  </MessageAction>
                </MessageActions>
              </div>
            )}
          </Message>
        );
      })}

      {(status === "submitted" || status === "streaming") && (
        <div className="mx-auto flex w-full max-w-3xl flex-col items-start gap-2 px-4">
          <div className="group flex w-full flex-col gap-0">
            <div className="flex items-center gap-2">Thinking...</div>
          </div>
        </div>
      )}

      <ChatContainerScrollAnchor />
    </ChatContainerContent>
  );
}
