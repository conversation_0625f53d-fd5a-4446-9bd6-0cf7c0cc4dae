import { useReaderStore } from "@/store/reader-store";
import type { BookSearchConfig, BookSearchResult } from "@/types/book";
import type { DocumentChunk } from "@/types/document";
import { createRejecttFilter } from "@/utils/node";
import { invoke } from "@tauri-apps/api/core";
import { useCallback, useState } from "react";
import { getBestSearchSentence } from "../text-utils";

export function useAnnotationSearch() {
  const [loading, setLoading] = useState(false);
  const [chunkData, setChunkData] = useState<DocumentChunk | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [searching, setSearching] = useState(false);

  const { activeBookId, getBookData, getConfig, getView, getProgress } = useReaderStore();

  const fetchChunkData = useCallback(
    async (chunkId: string) => {
      if (!activeBookId || !chunkId) return;

      setLoading(true);
      setError(null);

      try {
        const res = (await invoke("plugin:epub|get_chunk_with_context", {
          bookId: activeBookId,
          chunkId: Number.parseInt(chunkId),
          prevCount: 0,
          nextCount: 0,
        })) as DocumentChunk[];

        if (res.length > 0) {
          setChunkData(res[0]);
        } else {
          setError("未找到对应的文本片段");
        }
      } catch (e: any) {
        console.error("获取 chunk 数据失败:", e);
        setError(typeof e === "string" ? e : e?.message || "获取文本片段失败");
      } finally {
        setLoading(false);
      }
    },
    [activeBookId],
  );

  const searchAndNavigate = useCallback(async () => {
    if (!chunkData || !activeBookId) return false;

    setSearching(true);
    setError(null);

    try {
      const searchQuery = getBestSearchSentence(chunkData.chunk_text);

      if (!searchQuery || searchQuery.length < 3) {
        setError("无法提取有效的搜索关键词");
        return false;
      }

      const bookKey = activeBookId;
      const view = getView(bookKey);
      const config = getConfig(bookKey);
      const bookData = getBookData(bookKey);
      const progress = getProgress(bookKey);

      if (!view || !config || !bookData || !progress) {
        setError("阅读器未就绪，请稍后重试");
        return false;
      }

      try {
        view.clearSearch();
      } catch (e) {}

      const searchConfig = config.searchConfig as BookSearchConfig;
      const primaryLang = bookData.book?.primaryLanguage || "en";
      const { section } = progress;
      const index = searchConfig.scope === "section" ? section.current : undefined;

      view.setSearchIndicator("arrow", {
        color: "#ff4444",
        size: 24,
        animated: true,
        autoHide: true,
        hideDelay: 6000,
        offset: 15,
      });

      const generator = await view.search({
        ...searchConfig,
        index,
        query: searchQuery,
        acceptNode: createRejecttFilter({
          tags: primaryLang.startsWith("ja") ? ["rt"] : [],
        }),
      });

      const results: BookSearchResult[] = [];
      let foundFirst = false;

      for await (const result of generator) {
        if (typeof result === "string") {
          if (result === "done") {
            if (results.length === 0) {
              setError("未找到匹配的内容");
              return false;
            }
            return true;
          }
        } else {
          if (result.progress) {
          } else {
            results.push(result);

            if (!foundFirst) {
              foundFirst = true;
              let firstCfi: string | undefined;
              if ("subitems" in result && result.subitems && result.subitems.length > 0) {
                firstCfi = result.subitems[0].cfi;
              } else if ("cfi" in result) {
                firstCfi = (result as any).cfi;
              }

              if (firstCfi && view) {
                view.goTo(firstCfi);

                setTimeout(() => {
                  view.setSearchIndicator("outline", {});
                }, 100);
                return true;
              }
            }
          }
        }
        await new Promise((resolve) => setTimeout(resolve, 0));
      }

      return false;
    } catch (e: any) {
      console.error("搜索失败:", e);
      setError(typeof e === "string" ? e : e?.message || "搜索失败");
      return false;
    } finally {
      setSearching(false);
    }
  }, [chunkData, activeBookId, getView, getConfig, getBookData, getProgress]);

  return {
    loading,
    chunkData,
    error,
    searching,
    fetchChunkData,
    searchAndNavigate,
    resetError: () => setError(null),
    resetChunkData: () => setChunkData(null),
  };
}
