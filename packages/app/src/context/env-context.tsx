import type { AppService } from "@/types/system";
import React, { createContext, useContext, useState, type ReactNode } from "react";
import type { EnvConfigType } from "../services/environment";
import env from "../services/environment";

interface EnvContextType {
  envConfig: EnvConfigType;
  appService: AppService | null;
}

const EnvContext = createContext<EnvContextType | undefined>(undefined);

export const EnvProvider = ({ children }: { children: ReactNode }) => {
  const [envConfig] = useState<EnvConfigType>(env);
  const [appService, setAppService] = useState<AppService | null>(null);

  React.useEffect(() => {
    envConfig.getAppService().then((service) => setAppService(service));
    window.addEventListener("error", (e) => {
      if (e.message === "ResizeObserver loop limit exceeded") {
        e.stopImmediatePropagation();
        e.preventDefault();
        return true;
      }
      return false;
    });
  }, [envConfig]);

  return <EnvContext.Provider value={{ envConfig, appService }}>{children}</EnvContext.Provider>;
};

export const useEnv = (): EnvContextType => {
  const context = useContext(EnvContext);
  if (!context) throw new Error("useEnv must be used within EnvProvider");
  return context;
};
