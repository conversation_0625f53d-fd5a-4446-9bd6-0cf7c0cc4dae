import { getBooksWithStatus } from "@/services/book-service";
import type { EnvConfigType } from "@/services/environment";
import type { Book, BooksGroup } from "@/types/book";
import type { BookWithStatus, BookWithStatusAndUrls } from "@/types/simple-book";
import { convertFileSrc } from "@tauri-apps/api/core";
import { appDataDir } from "@tauri-apps/api/path";
import { create } from "zustand";

async function convertBookWithStatusUrls(book: BookWithStatus): Promise<BookWithStatusAndUrls> {
  try {
    const appDataDirPath = await appDataDir();
    const absoluteFilePath = book.filePath.startsWith("/") ? book.filePath : `${appDataDirPath}/${book.filePath}`;

    const absoluteCoverPath = book.coverPath
      ? book.coverPath.startsWith("/")
        ? book.coverPath
        : `${appDataDirPath}/${book.coverPath}`
      : undefined;

    const fileUrl = convertFileSrc(absoluteFilePath);
    const coverUrl = absoluteCoverPath ? convertFileSrc(absoluteCoverPath) : undefined;

    return {
      ...book,
      fileUrl,
      coverUrl,
    };
  } catch (error) {
    console.error("Error converting book URLs for:", book.title, error);
    throw error;
  }
}

interface LibraryState {
  library: Book[];
  currentBookshelf: (Book | BooksGroup)[];
  searchQuery: string;
  booksWithStatus: BookWithStatusAndUrls[];
  isLoading: boolean;
  getVisibleLibrary: () => Book[];
  setLibrary: (books: Book[]) => void;
  updateBook: (envConfig: EnvConfigType, book: Book) => void;
  setCurrentBookshelf: (bookshelf: (Book | BooksGroup)[]) => void;
  setSearchQuery: (query: string) => void;
  refreshBooks: () => Promise<void>;
  setBooksWithStatus: (books: BookWithStatusAndUrls[]) => void;
  setIsLoading: (loading: boolean) => void;
}

export const useLibraryStore = create<LibraryState>((set, get) => ({
  library: [],
  currentBookshelf: [],
  searchQuery: "",
  booksWithStatus: [],
  isLoading: false,
  getVisibleLibrary: () => get().library.filter((book) => !book.deletedAt),
  setCurrentBookshelf: (bookshelf: (Book | BooksGroup)[]) => {
    set({ currentBookshelf: bookshelf });
  },
  setLibrary: (books) => set({ library: books }),
  setSearchQuery: (query) => set({ searchQuery: query }),
  updateBook: async (envConfig: EnvConfigType, book: Book) => {
    const appService = await envConfig.getAppService();
    const { library } = get();
    const bookIndex = library.findIndex((b) => b.hash === book.hash);
    if (bookIndex !== -1) {
      library[bookIndex] = book;
    }
    set({ library: [...library] });
    appService.saveLibraryBooks(library);
  },
  setBooksWithStatus: (books) => set({ booksWithStatus: books }),
  setIsLoading: (loading) => set({ isLoading: loading }),
  refreshBooks: async () => {
    try {
      set({ isLoading: true });
      const libraryBooks = await getBooksWithStatus();
      const booksWithUrls = await Promise.all(libraryBooks.map(convertBookWithStatusUrls));
      set({ booksWithStatus: booksWithUrls });
    } catch (error) {
      console.error("Error refreshing books:", error);
    } finally {
      set({ isLoading: false });
    }
  },
}));
