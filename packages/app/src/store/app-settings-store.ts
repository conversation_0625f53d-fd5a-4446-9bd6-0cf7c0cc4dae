import { tauriStorageKey } from "@/constants/tauri-storage";
import { tauriStorage } from "@/lib/tauri-storage";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

interface AppSettingsState {
  isSettingsDialogOpen: boolean;
  toggleSettingsDialog: () => void;
}

export const useAppSettingsStore = create<AppSettingsState>()(
  persist(
    (set) => ({
      isSettingsDialogOpen: false,
      toggleSettingsDialog: () => set((state) => ({ isSettingsDialogOpen: !state.isSettingsDialogOpen })),
    }),
    {
      name: tauriStorageKey.appSettings,
      storage: createJSONStorage(() => tauriStorage),
      partialize: () => ({}), // 不需要持久化任何状态
    },
  ),
);
