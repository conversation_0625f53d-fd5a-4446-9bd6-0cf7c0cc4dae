import { type BookDoc, DocumentLoader } from "@/lib/document";
import type { EnvConfigType } from "@/services/environment";
import type { Book, BookConfig, BookNote, BookProgress, ViewSettings } from "@/types/book";
import type { SystemSettings } from "@/types/settings";
import type { FoliateView } from "@/types/view";
import { formatTitle, getBaseFilename, getPrimaryLanguage } from "@/utils/book";
import { updateToc } from "@/utils/toc";
import { appDataDir } from "@tauri-apps/api/path";
import { readTextFile } from "@tauri-apps/plugin-fs";
import type { TabProperties } from "app-tabs";
import { create } from "zustand";
import { useLibraryStore } from "./library-store";

export interface BookMetadata {
  title?: string;
  language?: string;
  published?: string;
  publisher?: string;
  author?: string | { name?: string }[] | { name?: string };
  base_dir?: string;
}

export interface BookDataState {
  id: string;
  book: Book | null;
  file: File | null;
  config: BookConfig | null;
  bookDoc: BookDoc | null;
}

export interface BookState {
  bookData: BookDataState | null;
  view: FoliateView | null;
  progress: BookProgress | null;
  viewSettings: ViewSettings | null;
  settings: SystemSettings;
  metadata: BookMetadata | null;
}

export interface TabInfo extends TabProperties {
  bookId: string;
}

interface ReaderStore {
  activeBookId: string | null;
  activeBookMetadata: BookMetadata | null;
  booksState: { [bookId: string]: BookState };
  tabs: TabInfo[];
  activeTabId: string | null;
  isLibraryActive: boolean;

  setActiveBookId: (bookId: string | null) => void;
  addTab: (bookId: string, title?: string) => void;
  removeTab: (tabId: string) => void;
  activateTab: (tabId: string) => void;
  updateTab: (tabId: string, updates: Partial<TabProperties>) => void;
  openReader: (bookId: string, title?: string) => void;
  navigateToLibrary: () => void;
  getBookData: (bookId?: string | null) => BookDataState | null;
  getConfig: (bookId?: string | null) => BookConfig | null;
  saveConfig: (envConfig: EnvConfigType, bookId: string, config: BookConfig, settings: SystemSettings) => Promise<void>;
  updateBooknotes: (key: string, booknotes: BookNote[]) => BookConfig | undefined;
  getView: (bookId?: string | null) => FoliateView | null;
  setView: (view: FoliateView, bookId?: string | null) => void;
  getProgress: (bookId?: string | null) => BookProgress | null;
  setProgress: (progress: BookProgress, bookId?: string | null) => void;
  getViewSettings: (bookId?: string | null) => ViewSettings | null;
  setViewSettings: (bookId: string, viewSettings: ViewSettings) => void;
  getSettings: (bookId?: string | null) => SystemSettings;
  setSettings: (settings: SystemSettings, bookId?: string | null) => void;
  initBookData: (envConfig: EnvConfigType, id: string, key?: string) => Promise<void>;
  getMetadata: (bookId?: string | null) => BookMetadata | null;
  setMetadata: (metadata: BookMetadata | null, bookId?: string | null) => void;
  loadBookMetadata: (bookId: string) => Promise<BookMetadata | null>;
}

async function loadBookMetadata(bookId: string): Promise<BookMetadata | null> {
  try {
    const appDataDirPath = await appDataDir();
    const metadataPath = `${appDataDirPath}/books/${bookId}/metadata.json`;
    const metadataContent = await readTextFile(metadataPath);
    const metadata = JSON.parse(metadataContent) as BookMetadata;
    return metadata;
  } catch (error) {
    console.error(`Failed to load metadata for book ${bookId}:`, error);
    return null;
  }
}

export const useReaderStore = create<ReaderStore>((set, get) => ({
  activeBookId: null,
  activeBookMetadata: null,
  booksState: {},
  tabs: [],
  activeTabId: null,
  isLibraryActive: true,

  setActiveBookId: async (bookId: string | null) => {
    set({ activeBookId: bookId, activeBookMetadata: null });

    if (bookId) {
      try {
        const metadata = await loadBookMetadata(bookId);
        if (get().activeBookId === bookId) {
          set({ activeBookMetadata: metadata });
        }
      } catch (error) {
        console.error(`Error loading metadata for book ${bookId}:`, error);
        if (get().activeBookId === bookId) {
          set({ activeBookMetadata: null });
        }
      }
    }
  },

  addTab: (bookId: string, title?: string) => {
    const tabId = `reader-${bookId}`;
    const tabTitle = title || `阅读器 - ${bookId}`;

    set((state) => {
      if (state.tabs.find((tab) => tab.id === tabId)) {
        return {
          ...state,
          tabs: state.tabs.map((tab) => ({
            ...tab,
            active: tab.id === tabId,
          })),
          activeTabId: tabId,
          isLibraryActive: false,
          activeBookId: bookId,
        };
      }

      const newTab: TabInfo = {
        id: tabId,
        title: tabTitle,
        active: true,
        isCloseIconVisible: true,
        bookId,
      };

      return {
        ...state,
        tabs: [...state.tabs.map((tab) => ({ ...tab, active: false })), newTab],
        activeTabId: tabId,
        isLibraryActive: false,
        activeBookId: bookId,
      };
    });
  },

  removeTab: (tabId: string) => {
    set((state) => {
      const removedTabIndex = state.tabs.findIndex((tab) => tab.id === tabId);
      const removedTab = state.tabs[removedTabIndex];
      const wasActive = removedTab?.active;
      const tabsAfterClose = state.tabs.filter((tab) => tab.id !== tabId);

      let newActiveTabId = state.activeTabId;
      let newActiveBookId = state.activeBookId;
      let newIsLibraryActive = state.isLibraryActive;

      if (wasActive) {
        if (tabsAfterClose.length > 0) {
          let newActiveIndex: number;
          if (removedTabIndex < tabsAfterClose.length) {
            newActiveIndex = removedTabIndex;
          } else {
            newActiveIndex = tabsAfterClose.length - 1;
          }

          if (newActiveIndex >= 0 && newActiveIndex < tabsAfterClose.length) {
            tabsAfterClose[newActiveIndex].active = true;
            newActiveTabId = tabsAfterClose[newActiveIndex].id;
            newActiveBookId = tabsAfterClose[newActiveIndex].bookId;
          }
        } else {
          newActiveTabId = null;
          newActiveBookId = null;
          newIsLibraryActive = true;
        }
      }

      const newBooksState = { ...state.booksState };
      if (removedTab?.bookId) {
        delete newBooksState[removedTab.bookId];
      }

      return {
        ...state,
        tabs: tabsAfterClose,
        activeTabId: newActiveTabId,
        activeBookId: newActiveBookId,
        isLibraryActive: newIsLibraryActive,
        booksState: newBooksState,
      };
    });
  },

  activateTab: (tabId: string) => {
    set((state) => {
      const targetTab = state.tabs.find((tab) => tab.id === tabId);
      return {
        ...state,
        tabs: state.tabs.map((tab) => ({ ...tab, active: tab.id === tabId })),
        activeTabId: tabId,
        activeBookId: targetTab?.bookId || null,
        isLibraryActive: false,
      };
    });
  },

  updateTab: (tabId: string, updates: Partial<TabProperties>) => {
    set((state) => ({
      ...state,
      tabs: state.tabs.map((tab) => (tab.id === tabId ? { ...tab, ...updates } : tab)),
    }));
  },

  openReader: (bookId: string, title?: string) => {
    get().addTab(bookId, title);
    if (!get().booksState[bookId]) {
      set((state) => ({
        ...state,
        booksState: {
          ...state.booksState,
          [bookId]: {
            bookData: null,
            view: null,
            progress: null,
            viewSettings: null,
            settings: {} as SystemSettings,
            metadata: null,
          },
        },
      }));
    }
  },

  navigateToLibrary: () => {
    set((state) => ({
      ...state,
      tabs: state.tabs.map((tab) => ({ ...tab, active: false })),
      activeTabId: null,
      activeBookId: null,
      isLibraryActive: true,
    }));
  },

  getBookData: (bookId?: string | null) => {
    const targetBookId = bookId ?? get().activeBookId;
    if (!targetBookId) return null;
    const result = get().booksState[targetBookId]?.bookData || null;
    if (!targetBookId) return null;
    return result;
  },

  getConfig: (bookId?: string | null) => {
    const targetBookId = bookId ?? get().activeBookId;
    if (!targetBookId) return null;
    return get().booksState[targetBookId]?.bookData?.config || null;
  },

  saveConfig: async (envConfig: EnvConfigType, bookId: string, config: BookConfig, settings: SystemSettings) => {
    const appService = await envConfig.getAppService();
    const { library, setLibrary } = useLibraryStore.getState();
    const bookKey = bookId.split("-")[0]!;
    const bookIndex = library.findIndex((b) => b.hash === bookKey);
    if (bookIndex === -1) return;

    const book = library.splice(bookIndex, 1)[0]!;
    book.progress = config.progress;
    book.updatedAt = Date.now();
    library.unshift(book);
    setLibrary(library);

    config.updatedAt = Date.now();
    await appService.saveBookConfig(book, config, settings);
    await appService.saveLibraryBooks(library);
  },

  updateBooknotes: (key: string, booknotes: BookNote[]) => {
    const state = get();
    const id = key.split("-")[0]!;
    const bookState = state.booksState[id];
    const bookData = bookState?.bookData;

    if (!bookData?.config) return undefined;

    const dedupedBooknotes = Array.from(
      new Map(booknotes.map((item) => [`${item.id}-${item.type}-${item.cfi}`, item])).values(),
    );

    const updatedConfig = {
      ...bookData.config,
      updatedAt: Date.now(),
      booknotes: dedupedBooknotes,
    } as BookConfig;

    set((state) => ({
      booksState: {
        ...state.booksState,
        [id]: {
          ...bookState!,
          bookData: {
            ...bookData,
            config: updatedConfig!,
          },
        },
      },
    }));

    return updatedConfig;
  },

  getView: (bookId?: string | null) => {
    const targetBookId = bookId ?? get().activeBookId;
    if (!targetBookId) return null;
    return get().booksState[targetBookId]?.view || null;
  },

  setView: (view: FoliateView, bookId?: string | null) => {
    const targetBookId = bookId ?? get().activeBookId;
    if (!targetBookId) return;

    set((state) => ({
      booksState: {
        ...state.booksState,
        [targetBookId]: {
          ...state.booksState[targetBookId]!,
          view,
        },
      },
    }));
  },

  getProgress: (bookId?: string | null) => {
    const targetBookId = bookId ?? get().activeBookId;
    if (!targetBookId) return null;
    return get().booksState[targetBookId]?.progress || null;
  },

  setProgress: (progress: BookProgress, bookId?: string | null) => {
    const targetBookId = bookId ?? get().activeBookId;
    if (!targetBookId) return;

    set((state) => {
      const currentBookState = state.booksState[targetBookId];
      if (!currentBookState) return state;
      const currentLocation = currentBookState.bookData?.config?.location;
      const newLocation = progress.location;
      const locationChanged = currentLocation !== newLocation;
      const updatedBookData =
        currentBookState.bookData && locationChanged
          ? {
              ...currentBookState.bookData,
              config: currentBookState.bookData.config
                ? {
                    ...currentBookState.bookData.config,
                    location: newLocation,
                  }
                : null,
            }
          : currentBookState.bookData;

      return {
        booksState: {
          ...state.booksState,
          [targetBookId]: {
            ...currentBookState,
            progress,
            bookData: updatedBookData,
          },
        },
      };
    });
  },

  getViewSettings: (bookId?: string | null) => {
    const targetBookId = bookId ?? get().activeBookId;
    if (!targetBookId) return null;
    const result = get().booksState[targetBookId]?.viewSettings || null;
    if (!targetBookId) return null;
    return result;
  },

  setViewSettings: (bookId: string, viewSettings: ViewSettings) => {
    const targetBookId = bookId ?? get().activeBookId;
    if (!targetBookId) return;

    set((state) => ({
      booksState: {
        ...state.booksState,
        [targetBookId]: {
          ...state.booksState[targetBookId]!,
          viewSettings,
        },
      },
    }));
  },

  getSettings: (bookId?: string | null) => {
    const targetBookId = bookId ?? get().activeBookId;
    if (!targetBookId) return {} as SystemSettings;
    return get().booksState[targetBookId]?.settings || ({} as SystemSettings);
  },

  setSettings: (settings: SystemSettings, bookId?: string | null) => {
    const targetBookId = bookId ?? get().activeBookId;
    if (!targetBookId) return;

    set((state) => ({
      booksState: {
        ...state.booksState,
        [targetBookId]: {
          ...state.booksState[targetBookId]!,
          settings,
        },
      },
    }));
  },

  initBookData: async (envConfig: EnvConfigType, id: string) => {
    try {
      const currentBookState = get().booksState[id];
      if (!currentBookState || !currentBookState.viewSettings) {
        const { useBookSettingsStore } = await import("@/store/book-settings-store");
        const globalSettings = useBookSettingsStore.getState().settings;
        const initialViewSettings = globalSettings?.globalViewSettings || ({} as ViewSettings);
        set((state) => ({
          booksState: {
            ...state.booksState,
            [id]: {
              ...(currentBookState || {
                bookData: null,
                view: null,
                progress: null,
                settings: globalSettings,
              }),
              viewSettings: initialViewSettings,
            },
          },
        }));
      }

      const settings = get().getSettings(id);
      const appService = await envConfig.getAppService();

      const { useLibraryStore } = await import("@/store/library-store");
      const { library } = useLibraryStore.getState();
      const book = library.find((b) => b.hash === id);
      if (!book) {
        throw new Error("Book not found");
      }
      const { convertFileSrc } = await import("@tauri-apps/api/core");
      const fileUrl = convertFileSrc(book.filePath!);
      const response = await fetch(fileUrl);
      const arrayBuffer = await response.arrayBuffer();
      const filename = book.filePath!.split("/").pop() || "book.epub";
      const file = new File([arrayBuffer], filename, { type: "application/epub+zip" });
      const config = await appService.loadBookConfig(book, settings);
      const { book: bookDoc } = await new DocumentLoader(file).open();
      updateToc(bookDoc, config.viewSettings?.sortedTOC ?? false);
      if (!bookDoc.metadata.title) {
        bookDoc.metadata.title = getBaseFilename(file.name);
      }
      book.sourceTitle = formatTitle(bookDoc.metadata.title);
      const primaryLanguage = getPrimaryLanguage(bookDoc.metadata.language);
      book.primaryLanguage = book.primaryLanguage ?? primaryLanguage;
      book.metadata = book.metadata ?? bookDoc.metadata;

      const configViewSettings = (config.viewSettings || {}) as ViewSettings;
      const globalViewSettings = settings.globalViewSettings as ViewSettings;

      set((state) => ({
        booksState: {
          ...state.booksState,
          [id]: {
            bookData: { id, book, file, config, bookDoc },
            view: null,
            progress: null,
            viewSettings: { ...globalViewSettings, ...configViewSettings },
            settings,
            metadata: null,
          },
        },
      }));
    } catch (error) {
      set((state) => ({
        booksState: {
          ...state.booksState,
          [id]: {
            bookData: { id, book: null, file: null, config: null, bookDoc: null },
            view: null,
            progress: null,
            viewSettings: null,
            settings: {} as SystemSettings,
            metadata: null,
          },
        },
      }));
    }
  },

  getMetadata: (bookId?: string | null) => {
    const targetBookId = bookId ?? get().activeBookId;
    if (!targetBookId) return null;

    if (targetBookId === get().activeBookId) {
      return get().activeBookMetadata;
    }
    return get().booksState[targetBookId]?.metadata || null;
  },

  setMetadata: (metadata: BookMetadata | null, bookId?: string | null) => {
    const targetBookId = bookId ?? get().activeBookId;
    if (!targetBookId) return;

    if (targetBookId === get().activeBookId) {
      set({ activeBookMetadata: metadata });
    }

    set((state) => ({
      booksState: {
        ...state.booksState,
        [targetBookId]: {
          ...state.booksState[targetBookId]!,
          metadata,
        },
      },
    }));
  },

  loadBookMetadata: async (bookId: string) => {
    try {
      const metadata = await loadBookMetadata(bookId);
      get().setMetadata(metadata, bookId);
      return metadata;
    } catch (error) {
      console.error(`Failed to load metadata for book ${bookId}:`, error);
      return null;
    }
  },
}));
