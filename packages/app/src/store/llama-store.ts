import type { SessionInfo } from "@/components/settings/llama-client";
import { tauriStorageKey } from "@/constants/tauri-storage";
import { tauriStorage } from "@/lib/tauri-storage";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

// 向量模型配置接口
export interface VectorModelConfig {
  id: string; // 唯一标识
  name: string; // 显示名称
  url: string; // API端点
  modelId: string; // 模型ID
  apiKey: string; // API Key
  description?: string; // 描述信息
}

interface LlamaState {
  // 原有的 Llama.cpp 服务器相关状态
  serverStatus: string;
  currentSession: SessionInfo | null;
  modelPath: string;
  testText: string;

  // 新增：多向量模型配置相关状态
  vectorModels: VectorModelConfig[]; // 向量模型配置列表
  selectedVectorModelId: string | null; // 当前选中的模型ID
  vectorModelEnabled: boolean; // 是否启用向量模型功能

  // 原有的方法
  setServerStatus: (status: string) => void;
  setCurrentSession: (session: SessionInfo | null) => void;
  setModelPath: (path: string) => void;
  setTestText: (text: string) => void;
  reset: () => void;

  // 新增：多向量模型配置相关方法
  setVectorModelEnabled: (enabled: boolean) => void;
  setVectorModels: (models: VectorModelConfig[]) => void;
  addVectorModel: (model: VectorModelConfig) => void;
  updateVectorModel: (id: string, updates: Partial<VectorModelConfig>) => void;
  deleteVectorModel: (id: string) => void;
  setSelectedVectorModelId: (id: string | null) => void;
  getSelectedVectorModel: () => VectorModelConfig | null;
  resetVectorModels: () => void;
}

export const useLlamaStore = create<LlamaState>()(
  persist(
    (set, get) => ({
      // 原有状态的默认值
      serverStatus: "",
      currentSession: null,
      modelPath: "/Users/<USER>/Downloads/Qwen3-Embedding-0.6B-f16.gguf",
      testText: "核心是什么？",

      // 新增：多向量模型配置的默认值
      vectorModels: [],
      selectedVectorModelId: null,
      vectorModelEnabled: false,

      // 原有的方法实现
      setServerStatus: (serverStatus) => set({ serverStatus }),
      setCurrentSession: (currentSession) => set({ currentSession }),
      setModelPath: (modelPath) => set({ modelPath }),
      setTestText: (testText) => set({ testText }),
      reset: () => set({ serverStatus: "", currentSession: null }),

      // 新增：多向量模型配置相关方法实现
      setVectorModelEnabled: (vectorModelEnabled) => set({ vectorModelEnabled }),
      setVectorModels: (vectorModels) => set({ vectorModels }),
      addVectorModel: (model) => {
        const { vectorModels } = get();
        set({ vectorModels: [...vectorModels, model] });
      },
      updateVectorModel: (id, updates) => {
        const { vectorModels } = get();
        set({
          vectorModels: vectorModels.map((model) => (model.id === id ? { ...model, ...updates } : model)),
        });
      },
      deleteVectorModel: (id) => {
        const { vectorModels, selectedVectorModelId } = get();
        const newModels = vectorModels.filter((model) => model.id !== id);
        const newSelected = selectedVectorModelId === id ? null : selectedVectorModelId;
        set({ vectorModels: newModels, selectedVectorModelId: newSelected });
      },
      setSelectedVectorModelId: (selectedVectorModelId) => set({ selectedVectorModelId }),
      getSelectedVectorModel: () => {
        const { vectorModels, selectedVectorModelId } = get();
        return vectorModels.find((model) => model.id === selectedVectorModelId) || null;
      },
      resetVectorModels: () =>
        set({
          vectorModels: [],
          selectedVectorModelId: null,
          vectorModelEnabled: false,
        }),
    }),
    {
      name: tauriStorageKey.llamaStore,
      storage: createJSONStorage(() => tauriStorage),
      // 持久化多向量模型配置相关的字段
      partialize: (state) => ({
        vectorModels: state.vectorModels,
        selectedVectorModelId: state.selectedVectorModelId,
        vectorModelEnabled: state.vectorModelEnabled,
        modelPath: state.modelPath,
        testText: state.testText,
      }),
    },
  ),
);
