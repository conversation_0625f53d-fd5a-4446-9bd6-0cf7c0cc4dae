import { buildReadingPrompt } from "@/constants/prompt";
import { useReaderStore } from "@/store/reader-store";
import type { UIMessage } from "@ai-sdk/react";
import { appDataDir } from "@tauri-apps/api/path";
import { exists, readTextFile } from "@tauri-apps/plugin-fs";
import {
  type ChatRequestOptions,
  type ChatTransport,
  type LanguageModel,
  type UIMessageChunk,
  convertToModelMessages,
  stepCountIs,
  streamText,
} from "ai";
import { ragContextTool, ragRangeTool, ragSearchTool, ragTocTool } from "./tools";

export class CustomChatTransport implements ChatTransport<UIMessage> {
  private model: LanguageModel;

  constructor(model: LanguageModel) {
    this.model = model;
  }

  updateModel(model: LanguageModel) {
    this.model = model;
  }

  async sendMessages(
    options: {
      chatId: string;
      messages: UIMessage[];
      abortSignal: AbortSignal | undefined;
    } & {
      trigger: "submit-message" | "regenerate-message";
      messageId: string | undefined;
    } & ChatRequestOptions,
  ): Promise<ReadableStream<UIMessageChunk>> {
    // Try load metadata.md for current active book and append to system prompt
    const activeBookId = useReaderStore.getState().activeBookId;
    let metadataMd: string | null = null;
    try {
      if (activeBookId) {
        const base = await appDataDir();
        const metaPath = `${base}/books/${activeBookId}/metadata.md`;
        if (await exists(metaPath)) {
          metadataMd = await readTextFile(metaPath);
        }
      }
    } catch (e) {
      console.warn("加载 metadata.md 失败：", e);
    }

    const result = streamText({
      model: this.model,
      messages: convertToModelMessages(options.messages.slice(-10)),
      abortSignal: options.abortSignal,
      toolChoice: "auto",
      stopWhen: stepCountIs(20),
      tools: {
        ragSearch: ragSearchTool,
        ragRange: ragRangeTool,
        ragToc: ragTocTool,
        ragContext: ragContextTool,
      },
      system: buildReadingPrompt(metadataMd ?? undefined),
    });

    return result.toUIMessageStream({
      onError: (error) => {
        console.log("error", error);
        if (error == null) {
          return "Unknown error";
        }
        if (typeof error === "string") {
          return error;
        }
        if (error instanceof Error) {
          return error.message;
        }
        return JSON.stringify(error);
      },
      messageMetadata: ({ part }) => {
        // console.log("part", part);
        if (part.type === "finish") {
          return {
            totalUsage: part.totalUsage,
          };
        }
      },
    });
  }

  async reconnectToStream(
    _options: {
      chatId: string;
    } & ChatRequestOptions,
  ): Promise<ReadableStream<UIMessageChunk> | null> {
    return null;
  }
}
