{"Notebook": "Notebook", "Try searching with different keywords": "Try searching with different keywords", "Font & Layout": "Font & Layout", "Scrolled Mode": "Scrolled Mode", "Fullscreen": "Fullscreen", "Dark Mode": "Dark Mode", "Light Mode": "Light Mode", "Auto Mode": "Auto Mode", "Invert Image In Dark Mode": "Invert Image In Dark Mode", "No notes match your search": "No notes match your search", "Excerpts": "Excerpts", "Delete": "Delete", "Notes": "Notes", "Add your notes here...": "Add your notes here...", "Save": "Save", "Bookmark": "Bookmark", "Copy": "Copy", "Explain": "Explain", "Ask AI": "Ask AI", "Search notes and excerpts...": "Search notes and excerpts...", "No table of contents available": "No table of contents available", "Search": "Search", "No search results found": "No search results found", "Enter search terms to find content": "Enter search terms to find content", "Ask AI Anything": "Ask AI Anything", "Previous Chapter": "Previous Chapter", "Previous Page": "Previous Page", "Next Chapter": "Next Chapter", "Next Page": "Next Page", "Book": "Book", "Chapter": "Chapter", "Match Case": "Match Case", "Match Whole Words": "Match Whole Words", "Match Diacritics": "Match Diacritics", "Font Size Settings": "Font Size Settings", "Font Family": "Font Family", "Font Size": "Font Size", "Decrease font size": "Decrease font size", "Increase font size": "Increase font size", "Reading Mode": "Reading Mode", "Paginated Mode": "Paginated Mode", "Paginated": "Paginated", "Scrolled": "Scrolled", "Cross-Chapter Scroll": "Cross-Chapter Scroll", "Theme Mode": "Theme Mode", "TOC": "TOC", "Annotate": "Annotate", "Parallel Read": "<PERSON><PERSON><PERSON>", "Disable": "Disable", "Enable": "Enable", "Export Annotations": "Export Annotations", "Sort TOC by Page": "Sort TOC by <PERSON>", "Reload Page": "Reload Page", "Download Readest": "Download Readest", "Edit": "Edit", "Global Settings": "Global Settings", "Apply to All Books": "Apply to All Books", "Apply to This Book": "Apply to This Book", "Reset Settings": "Reset Settings", "Book Cover": "Book Cover", "More Info": "More Info", "System Fonts": "System Fonts", "Press Enter to search...": "Press Enter to search...", "Custom Size": "Custom Size", "Advanced Font Settings": "Advanced Font Settings", "For advanced users who want to customize individual font families.": "For advanced users who want to customize individual font families.", "Open Full Font Settings": "Open Full Font Settings", "Custom": "Custom", "Custom Theme": "Custom Theme", "Theme Name": "Theme Name", "Text Color": "Text Color", "Background Color": "Background Color", "Link Color": "Link Color", "Preview": "Preview", "Font": "Font", "Layout": "Layout", "Color": "Color", "Behavior": "Behavior", "Language": "Language", "Cancel": "Cancel", "Open": "Open", "Edit Info": "Edit Info", "Mark as Read": "<PERSON> <PERSON>", "Mark as Unread": "<PERSON> as Unread", "Are you sure to delete the selected book?": "Are you sure to delete the selected book?", "This action cannot be undone.": "This action cannot be undone.", "Confirm Deletion": "Confirm Deletion", "Edit Book Info": "Edit Book Info", "Cover Image": "Cover Image", "Change Cover": "Change Cover", "Upload Cover": "Upload Cover", "Supported formats: PNG, JPG, GIF, WebP": "Supported formats: PNG, JPG, GIF, WebP", "Title": "Title", "Enter book title": "Enter book title", "Author": "Author", "Enter author name": "Enter author name", "Saving...": "Saving...", "Save Changes": "Save Changes", "Download Image": "Download Image", "Copy Image": "Copy Image", "Settings": "Settings", "General": "General", "Appearance": "Appearance", "Privacy": "Privacy", "Model Providers": "Model Providers", "Shortcuts": "Shortcuts", "App Version": "App Version", "Check for Updates": "Check for Updates", "Check if a newer version of Jan is available.": "Check if a newer version of Jan is available.", "Data Folder": "Data Folder", "App Data": "App Data", "App Logs": "App Logs", "View detailed logs of the App.": "View detailed logs of the App.", "Open Logs": "Open Logs", "Show in Finder": "Show in Finder", "Advanced": "Advanced", "Experimental Features": "Experimental Features", "Enable experimental features. They may be unstable or change at any time.": "Enable experimental features. They may be unstable or change at any time.", "Reset To Factory Settings": "Reset To Factory Settings", "Restore application to its initial state, erasing all models and chat history. This action is irreversible and recommended only if the application is corrupted.": "Restore application to its initial state, erasing all models and chat history. This action is irreversible and recommended only if the application is corrupted.", "Reset": "Reset", "Are you sure you want to reset to factory settings? This action is irreversible.": "Are you sure you want to reset to factory settings? This action is irreversible.", "Search models...": "Search models...", "Showing {{filteredCount}} of {{totalCount}} models": "Showing {{filteredCount}} of {{totalCount}} models", "Models": "Models", "Refreshing...": "Refreshing...", "Refresh": "Refresh", "Clear All": "Clear All", "Error:": "Error:", "Confirm Clear All Models": "Confirm Clear All Models", "This will permanently delete all models. This action cannot be undone.": "This will permanently delete all models. This action cannot be undone.", "Delete All": "Delete All", "Add New Model": "Add New Model", "Model ID": "Model ID", "Display Name": "Display Name", "Manual": "Manual", "No models configured. Click \"Add Model\" to get started.": "No models configured. Click \"Add Model\" to get started.", "No models match the current filters.": "No models match the current filters.", "Provider not found": "Provider not found", "Please configure the base URL first": "Please configure the base URL first", "Add Provider": "Add Provider", "Model": "Model", "{{count}} Models": "{{count}} Models", "Add Book": "Add Book", "Uploading...": "Uploading...", "Drop files to upload": "Drop files to upload", "Release to upload your books": "Release to upload your books"}