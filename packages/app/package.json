{"name": "app", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@ai-sdk/anthropic": "^2.0.5", "@ai-sdk/deepseek": "^1.0.10", "@ai-sdk/google": "^2.0.6", "@ai-sdk/openai": "^2.0.16", "@ai-sdk/react": "^2.0.15", "@openrouter/ai-sdk-provider": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/vite": "^4.1.12", "@tauri-apps/api": "^2.8.0", "@tauri-apps/plugin-dialog": "^2.3.3", "@tauri-apps/plugin-fs": "^2.4.1", "@tauri-apps/plugin-global-shortcut": "~2", "@tauri-apps/plugin-http": "~2", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-sql": "~2", "@zip.js/zip.js": "^2.7.52", "ai": "^5.0.15", "app-tabs": "workspace:*", "class-variance-authority": "^0.7.1", "cssbeautify": "^0.3.1", "dayjs": "^1.11.13", "foliate-js": "workspace:*", "highlight.js": "^11.11.1", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "iso-639-2": "^3.0.2", "js-md5": "^0.8.3", "jwt-decode": "^4.0.0", "lucide-react": "^0.528.0", "marked": "^16.1.1", "next-themes": "^0.4.6", "overlayscrollbars": "^2.11.4", "overlayscrollbars-react": "^0.5.6", "re-resizable": "^6.11.2", "react": "^19.1.0", "react-color": "^2.19.3", "react-dom": "^19.1.0", "react-i18next": "^15.6.1", "react-icons": "^5.5.0", "react-jsx-parser": "^2.4.0", "react-markdown": "^10.1.0", "react-responsive": "^10.0.1", "react-router": "^7.7.1", "react-router-dom": "^7.7.1", "react-window": "^1.8.11", "remark-breaks": "^4.0.0", "remark-cjk-friendly": "^1.2.0", "remark-gfm": "^4.0.1", "semver": "^7.7.2", "shiki": "^3.9.2", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tinycolor2": "^1.6.0", "use-stick-to-bottom": "^1.1.1", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@eslint/js": "^9.30.1", "@tailwindcss/typography": "^0.5.16", "@tauri-apps/cli": "^2", "@types/cssbeautify": "^0.3.5", "@types/marked": "^6.0.0", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-color": "^3.0.13", "@types/react-dom": "^19.1.6", "@types/react-window": "^1.8.8", "@types/tinycolor2": "^1.4.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "clsx": "^2.1.1", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tw-animate-css": "^1.3.6", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}