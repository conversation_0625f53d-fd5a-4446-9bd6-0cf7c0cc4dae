{"name": "@janhq/tauri-plugin-llamacpp-api", "version": "0.6.6", "private": true, "description": "", "type": "module", "types": "./dist-js/index.d.ts", "main": "./dist-js/index.cjs", "module": "./dist-js/index.js", "exports": {"types": "./dist-js/index.d.ts", "import": "./dist-js/index.js", "require": "./dist-js/index.cjs"}, "files": ["dist-js", "README.md"], "scripts": {"build": "rollup -c", "prepublishOnly": "yarn build", "pretest": "yarn build"}, "dependencies": {"@tauri-apps/api": ">=2.0.0-beta.6"}, "devDependencies": {"@rollup/plugin-typescript": "^12.0.0", "rollup": "^4.9.6", "tslib": "^2.6.2", "typescript": "^5.3.3"}}