# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-find-session-by-model"
description = "Enables the find_session_by_model command without any pre-configured scope."
commands.allow = ["find_session_by_model"]

[[permission]]
identifier = "deny-find-session-by-model"
description = "Denies the find_session_by_model command without any pre-configured scope."
commands.deny = ["find_session_by_model"]
