# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-read-gguf-metadata"
description = "Enables the read_gguf_metadata command without any pre-configured scope."
commands.allow = ["read_gguf_metadata"]

[[permission]]
identifier = "deny-read-gguf-metadata"
description = "Denies the read_gguf_metadata command without any pre-configured scope."
commands.deny = ["read_gguf_metadata"]
