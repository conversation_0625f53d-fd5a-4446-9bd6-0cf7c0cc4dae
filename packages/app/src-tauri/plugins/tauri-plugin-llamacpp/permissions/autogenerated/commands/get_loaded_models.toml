# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-get-loaded-models"
description = "Enables the get_loaded_models command without any pre-configured scope."
commands.allow = ["get_loaded_models"]

[[permission]]
identifier = "deny-get-loaded-models"
description = "Denies the get_loaded_models command without any pre-configured scope."
commands.deny = ["get_loaded_models"]
