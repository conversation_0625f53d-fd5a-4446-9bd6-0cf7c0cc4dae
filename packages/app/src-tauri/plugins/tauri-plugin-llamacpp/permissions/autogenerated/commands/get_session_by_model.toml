# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-get-session-by-model"
description = "Enables the get_session_by_model command without any pre-configured scope."
commands.allow = ["get_session_by_model"]

[[permission]]
identifier = "deny-get-session-by-model"
description = "Denies the get_session_by_model command without any pre-configured scope."
commands.deny = ["get_session_by_model"]
