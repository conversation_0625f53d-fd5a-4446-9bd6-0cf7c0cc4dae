# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-cleanup-llama-processes"
description = "Enables the cleanup_llama_processes command without any pre-configured scope."
commands.allow = ["cleanup_llama_processes"]

[[permission]]
identifier = "deny-cleanup-llama-processes"
description = "Denies the cleanup_llama_processes command without any pre-configured scope."
commands.deny = ["cleanup_llama_processes"]
