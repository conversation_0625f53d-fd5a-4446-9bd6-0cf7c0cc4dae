# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-get-devices"
description = "Enables the get_devices command without any pre-configured scope."
commands.allow = ["get_devices"]

[[permission]]
identifier = "deny-get-devices"
description = "Denies the get_devices command without any pre-configured scope."
commands.deny = ["get_devices"]
