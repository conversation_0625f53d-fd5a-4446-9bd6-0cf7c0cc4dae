# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-get-random-port"
description = "Enables the get_random_port command without any pre-configured scope."
commands.allow = ["get_random_port"]

[[permission]]
identifier = "deny-get-random-port"
description = "Denies the get_random_port command without any pre-configured scope."
commands.deny = ["get_random_port"]
