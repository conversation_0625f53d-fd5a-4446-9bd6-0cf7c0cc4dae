# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-is-process-running"
description = "Enables the is_process_running command without any pre-configured scope."
commands.allow = ["is_process_running"]

[[permission]]
identifier = "deny-is-process-running"
description = "Denies the is_process_running command without any pre-configured scope."
commands.deny = ["is_process_running"]
