## Default Permission

Default permissions for the llamacpp plugin

#### This default permission set includes the following:

- `allow-cleanup-llama-processes`
- `allow-load-llama-model`
- `allow-unload-llama-model`
- `allow-get-devices`
- `allow-generate-api-key`
- `allow-is-process-running`
- `allow-get-random-port`
- `allow-find-session-by-model`
- `allow-get-loaded-models`
- `allow-get-all-sessions`
- `allow-get-session-by-model`
- `allow-read-gguf-metadata`

## Permission Table

<table>
<tr>
<th>Identifier</th>
<th>Description</th>
</tr>


<tr>
<td>

`llamacpp:allow-cleanup-llama-processes`

</td>
<td>

Enables the cleanup_llama_processes command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`llamacpp:deny-cleanup-llama-processes`

</td>
<td>

Denies the cleanup_llama_processes command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`llamacpp:allow-find-session-by-model`

</td>
<td>

Enables the find_session_by_model command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`llamacpp:deny-find-session-by-model`

</td>
<td>

Denies the find_session_by_model command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`llamacpp:allow-generate-api-key`

</td>
<td>

Enables the generate_api_key command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`llamacpp:deny-generate-api-key`

</td>
<td>

Denies the generate_api_key command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`llamacpp:allow-get-all-sessions`

</td>
<td>

Enables the get_all_sessions command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`llamacpp:deny-get-all-sessions`

</td>
<td>

Denies the get_all_sessions command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`llamacpp:allow-get-devices`

</td>
<td>

Enables the get_devices command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`llamacpp:deny-get-devices`

</td>
<td>

Denies the get_devices command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`llamacpp:allow-get-loaded-models`

</td>
<td>

Enables the get_loaded_models command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`llamacpp:deny-get-loaded-models`

</td>
<td>

Denies the get_loaded_models command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`llamacpp:allow-get-random-port`

</td>
<td>

Enables the get_random_port command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`llamacpp:deny-get-random-port`

</td>
<td>

Denies the get_random_port command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`llamacpp:allow-get-session-by-model`

</td>
<td>

Enables the get_session_by_model command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`llamacpp:deny-get-session-by-model`

</td>
<td>

Denies the get_session_by_model command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`llamacpp:allow-is-process-running`

</td>
<td>

Enables the is_process_running command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`llamacpp:deny-is-process-running`

</td>
<td>

Denies the is_process_running command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`llamacpp:allow-load-llama-model`

</td>
<td>

Enables the load_llama_model command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`llamacpp:deny-load-llama-model`

</td>
<td>

Denies the load_llama_model command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`llamacpp:allow-read-gguf-metadata`

</td>
<td>

Enables the read_gguf_metadata command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`llamacpp:deny-read-gguf-metadata`

</td>
<td>

Denies the read_gguf_metadata command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`llamacpp:allow-unload-llama-model`

</td>
<td>

Enables the unload_llama_model command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`llamacpp:deny-unload-llama-model`

</td>
<td>

Denies the unload_llama_model command without any pre-configured scope.

</td>
</tr>
</table>
