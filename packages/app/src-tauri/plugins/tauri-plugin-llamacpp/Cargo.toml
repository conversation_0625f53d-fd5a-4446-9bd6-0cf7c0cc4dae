[package]
name = "tauri-plugin-llamacpp"
version = "0.6.599"
authors = ["Jan <<EMAIL>>"]
description = "Tauri plugin for managing Jan <PERSON>lam<PERSON> server processes and model loading"
license = "MIT"
repository = "https://github.com/menloresearch/jan"
edition = "2021"
rust-version = "1.77.2"
exclude = ["/examples", "/dist-js", "/guest-js", "/node_modules"]
links = "tauri-plugin-llamacpp"

[dependencies]
base64 = "0.22.1"
byteorder = "1.5.0"
hmac = "0.12.1"
jan-utils = { path = "../../utils" }
log = "0.4"
rand = "0.8"
serde = { version = "1.0", features = ["derive"] }
sha2 = "0.10.9"
sysinfo = "0.34.2"
tauri = { version = "2.5.0", default-features = false, features = [] }
thiserror = "2.0.12"
tokio = { version = "1", features = ["full"] }

# Windows-specific dependencies
[target.'cfg(windows)'.dependencies]
windows-sys = { version = "0.60.2", features = ["Win32_Storage_FileSystem"] }

# Unix-specific dependencies  
[target.'cfg(unix)'.dependencies]
nix = { version = "=0.30.1", features = ["signal", "process"] }

[build-dependencies]
tauri-plugin = { version = "2.3.1", features = ["build"] }
