# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-convert-to-mdbook"
description = "Enables the convert_to_mdbook command without any pre-configured scope."
commands.allow = ["convert_to_mdbook"]

[[permission]]
identifier = "deny-convert-to-mdbook"
description = "Denies the convert_to_mdbook command without any pre-configured scope."
commands.deny = ["convert_to_mdbook"]

