# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-get-chunk-with-context"
description = "Enables the get_chunk_with_context command without any pre-configured scope."
commands.allow = ["get_chunk_with_context"]

[[permission]]
identifier = "deny-get-chunk-with-context"
description = "Denies the get_chunk_with_context command without any pre-configured scope."
commands.deny = ["get_chunk_with_context"] 