# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-get-toc-chunks"
description = "Enables the get_toc_chunks command without any pre-configured scope."
commands.allow = ["get_toc_chunks"]

[[permission]]
identifier = "deny-get-toc-chunks"
description = "Denies the get_toc_chunks command without any pre-configured scope."
commands.deny = ["get_toc_chunks"] 