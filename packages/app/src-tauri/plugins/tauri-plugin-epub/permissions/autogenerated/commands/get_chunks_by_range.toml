# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-get-chunks-by-range"
description = "Enables the get_chunks_by_range command without any pre-configured scope."
commands.allow = ["get_chunks_by_range"]

[[permission]]
identifier = "deny-get-chunks-by-range"
description = "Denies the get_chunks_by_range command without any pre-configured scope."
commands.deny = ["get_chunks_by_range"] 