## Default Permission

Default permissions for the epub plugin

#### This default permission set includes the following:

- `allow-parse-epub`
- `allow-index-epub`
- `allow-search-db`
- `allow-convert-to-mdbook`
- `allow-parse-toc`
- `allow-get-chunk-with-context`
- `allow-get-toc-chunks`
- `allow-get-chunks-by-range`

## Permission Table

<table>
<tr>
<th>Identifier</th>
<th>Description</th>
</tr>


<tr>
<td>

`epub:allow-convert-to-mdbook`

</td>
<td>

Enables the convert_to_mdbook command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`epub:deny-convert-to-mdbook`

</td>
<td>

Denies the convert_to_mdbook command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`epub:allow-get-chunk-with-context`

</td>
<td>

Enables the get_chunk_with_context command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`epub:deny-get-chunk-with-context`

</td>
<td>

Denies the get_chunk_with_context command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`epub:allow-get-chunks-by-range`

</td>
<td>

Enables the get_chunks_by_range command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`epub:deny-get-chunks-by-range`

</td>
<td>

Denies the get_chunks_by_range command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`epub:allow-get-toc-chunks`

</td>
<td>

Enables the get_toc_chunks command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`epub:deny-get-toc-chunks`

</td>
<td>

Denies the get_toc_chunks command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`epub:allow-index-epub`

</td>
<td>

Enables the index_epub command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`epub:deny-index-epub`

</td>
<td>

Denies the index_epub command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`epub:allow-parse-epub`

</td>
<td>

Enables the parse_epub command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`epub:deny-parse-epub`

</td>
<td>

Denies the parse_epub command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`epub:allow-parse-toc`

</td>
<td>

Enables the parse_toc command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`epub:deny-parse-toc`

</td>
<td>

Denies the parse_toc command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`epub:allow-search-db`

</td>
<td>

Enables the search_db command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`epub:deny-search-db`

</td>
<td>

Denies the search_db command without any pre-configured scope.

</td>
</tr>
</table>
