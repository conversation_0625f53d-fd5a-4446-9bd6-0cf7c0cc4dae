{"$schema": "https://schema.tauri.app/config/2", "productName": "tauri-app", "version": "0.1.0", "identifier": "com.chenyuming.tauri-app", "build": {"beforeDevCommand": "pnpm dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "pnpm build", "frontendDist": "../dist"}, "app": {"macOSPrivateApi": true, "windows": [{"zoomHotkeysEnabled": true, "label": "main", "title": "Jan", "width": 1200, "minWidth": 960, "minHeight": 720, "height": 830, "resizable": true, "fullscreen": false, "center": true, "hiddenTitle": true, "transparent": true, "trafficLightPosition": {"x": 12, "y": 19}, "decorations": true, "titleBarStyle": "Overlay", "windowEffects": {"effects": ["fullScreenUI", "mica", "tabbed", "blur", "acrylic"], "state": "active", "radius": 8}, "dragDropEnabled": false}], "security": {"dangerousDisableAssetCspModification": true, "csp": {"default-src": ["'self'", "ipc://localhost", "blob:"], "style-src": ["'self'", "'unsafe-inline'", "blob:", "https://fonts.googleapis.com", "https://cdn.jsdelivr.net"], "img-src": ["'self'", "asset:", "http://asset.localhost", "data:", "https:", "blob:"], "font-src": ["'self'", "https://fonts.gstatic.com", "https://cdn.jsdelivr.net", "https://db.onlinewebfonts.com", "data:"], "connect-src": ["'self'", "ipc://localhost", "http://asset.localhost", "asset:", "https:", "blob:"], "script-src": ["'self'", "'unsafe-eval'"], "frame-src": ["'self'", "data:", "blob:"], "child-src": ["'self'", "blob:"]}, "assetProtocol": {"enable": true, "scope": ["**"]}}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}