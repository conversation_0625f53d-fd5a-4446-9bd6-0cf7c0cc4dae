{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "default", "description": "Capability for the main window", "windows": ["main"], "permissions": ["core:default", "opener:default", "core:window:default", "core:window:allow-start-dragging", "core:path:default", "global-shortcut:allow-is-registered", "global-shortcut:allow-register", "global-shortcut:allow-unregister", "fs:allow-appconfig-read-recursive", "fs:allow-appconfig-write-recursive", "fs:allow-appdata-read-recursive", "fs:allow-appdata-write-recursive", "fs:allow-temp-read-recursive", "fs:allow-temp-write-recursive", {"identifier": "http:default", "allow": [{"url": "https://*:*"}, {"url": "http://*:*"}], "deny": []}, "sql:default", "sql:allow-execute", "dialog:allow-ask", "dialog:allow-confirm", "dialog:allow-message", "dialog:allow-open", "dialog:allow-save", "log:default", "fs:allow-write-file", {"identifier": "llamacpp:default", "allow": [{"path": "$APPDATA"}, {"path": "$APPDATA/**"}]}, "epub:default"]}