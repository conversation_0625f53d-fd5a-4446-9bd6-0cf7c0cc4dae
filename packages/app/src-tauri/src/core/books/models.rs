use serde::{Deserialize, Serialize};

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct SimpleBook {
    pub id: String,
    pub title: String,
    pub author: String,
    pub format: String,
    #[serde(rename = "filePath")]
    pub file_path: String,
    #[serde(rename = "coverPath")]
    pub cover_path: Option<String>,
    #[serde(rename = "fileSize")]
    pub file_size: i64,
    pub language: String,
    pub tags: Option<Vec<String>>,
    #[serde(rename = "createdAt")]
    pub created_at: i64,
    #[serde(rename = "updatedAt")]
    pub updated_at: i64,
}

#[derive(Deserialize, Debug)]
pub struct BookUploadData {
    pub id: String,
    pub title: String,
    pub author: String,
    pub format: String,
    #[serde(rename = "fileSize")]
    pub file_size: i64,
    pub language: String,
    #[serde(rename = "tempFilePath")]
    pub temp_file_path: String,
    #[serde(rename = "coverTempFilePath")]
    pub cover_temp_file_path: Option<String>,
    pub metadata: serde_json::Value,
}

#[derive(Deserialize, Debug)]
pub struct BookQueryOptions {
    pub limit: Option<i64>,
    pub offset: Option<i64>,
    #[serde(rename = "searchQuery")]
    pub search_query: Option<String>,
    pub tags: Option<Vec<String>>,
    #[serde(rename = "sortBy")]
    pub sort_by: Option<String>,
    #[serde(rename = "sortOrder")]
    pub sort_order: Option<String>,
}

#[derive(Deserialize, Debug)]
pub struct BookUpdateData {
    pub title: Option<String>,
    pub author: Option<String>,
    pub tags: Option<Vec<String>>,
    #[serde(rename = "updatedAt")]
    pub updated_at: Option<i64>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct BookStatus {
    #[serde(rename = "bookId")]
    pub book_id: String,
    pub status: String, // 'unread', 'reading', 'completed'
    #[serde(rename = "progressCurrent")]
    pub progress_current: i64,
    #[serde(rename = "progressTotal")]
    pub progress_total: i64,
    #[serde(rename = "lastReadingPosition")]
    pub last_reading_position: Option<String>,
    #[serde(rename = "readingTimeMinutes")]
    pub reading_time_minutes: i64,
    #[serde(rename = "lastReadAt")]
    pub last_read_at: Option<i64>,
    #[serde(rename = "startedAt")]
    pub started_at: Option<i64>,
    #[serde(rename = "completedAt")]
    pub completed_at: Option<i64>,
    pub metadata: Option<serde_json::Value>,
    #[serde(rename = "createdAt")]
    pub created_at: i64,
    #[serde(rename = "updatedAt")]
    pub updated_at: i64,
}

#[derive(Deserialize, Debug)]
pub struct BookStatusUpdateData {
    pub status: Option<String>,
    #[serde(rename = "progressCurrent")]
    pub progress_current: Option<i64>,
    #[serde(rename = "progressTotal")]
    pub progress_total: Option<i64>,
    #[serde(rename = "lastReadingPosition")]
    pub last_reading_position: Option<String>,
    #[serde(rename = "readingTimeMinutes")]
    pub reading_time_minutes: Option<i64>,
    #[serde(rename = "lastReadAt")]
    pub last_read_at: Option<i64>,
    #[serde(rename = "startedAt")]
    pub started_at: Option<i64>,
    #[serde(rename = "completedAt")]
    pub completed_at: Option<i64>,
    pub metadata: Option<serde_json::Value>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct BookWithStatus {
    #[serde(flatten)]
    pub book: SimpleBook,
    pub status: Option<BookStatus>,
}

impl SimpleBook {
    pub fn new(
        id: String,
        title: String,
        author: String,
        format: String,
        file_path: String,
        cover_path: Option<String>,
        file_size: i64,
        language: String,
    ) -> Self {
        let now = chrono::Utc::now().timestamp();
        Self {
            id,
            title,
            author,
            format,
            file_path,
            cover_path,
            file_size,
            language,
            tags: None,
            created_at: now,
            updated_at: now,
        }
    }

    pub fn from_db_row(row: &sqlx::sqlite::SqliteRow) -> Result<Self, sqlx::Error> {
        use sqlx::Row;

        let tags_str: Option<String> = row.try_get("tags")?;
        let tags = tags_str.and_then(|s| serde_json::from_str(&s).ok());

        Ok(Self {
            id: row.try_get("id")?,
            title: row.try_get("title")?,
            author: row.try_get("author")?,
            format: row.try_get("format")?,
            file_path: row.try_get("file_path")?,
            cover_path: row.try_get("cover_path")?,
            file_size: row.try_get("file_size")?,
            language: row.try_get("language")?,
            tags,
            created_at: row.try_get("created_at")?,
            updated_at: row.try_get("updated_at")?,
        })
    }
}

impl BookStatus {
    #[allow(dead_code)]
    pub fn new(book_id: String) -> Self {
        let now = chrono::Utc::now().timestamp();
        Self {
            book_id,
            status: "unread".to_string(),
            progress_current: 0,
            progress_total: 0,
            last_reading_position: None,
            reading_time_minutes: 0,
            last_read_at: None,
            started_at: None,
            completed_at: None,
            metadata: None,
            created_at: now,
            updated_at: now,
        }
    }

    pub fn from_db_row(row: &sqlx::sqlite::SqliteRow) -> Result<Self, sqlx::Error> {
        use sqlx::Row;

        Ok(Self {
            book_id: row.try_get("book_id")?,
            status: row.try_get("status")?,
            progress_current: row.try_get("progress_current")?,
            progress_total: row.try_get("progress_total")?,
            last_reading_position: row.try_get("last_reading_position")?,
            reading_time_minutes: row.try_get("reading_time_minutes")?,
            last_read_at: row.try_get("last_read_at")?,
            started_at: row.try_get("started_at")?,
            completed_at: row.try_get("completed_at")?,
            metadata: row
                .try_get::<Option<String>, _>("metadata")?
                .and_then(|s| serde_json::from_str(&s).ok()),
            created_at: row.try_get("created_at")?,
            updated_at: row.try_get("updated_at")?,
        })
    }
}
