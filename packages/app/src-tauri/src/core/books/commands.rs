use super::models::*;
use sqlx::{Row, SqlitePool};
use std::fs;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>};

#[tauri::command]
pub async fn save_book(app_handle: AppHandle, data: BookUploadData) -> Result<SimpleBook, String> {
    let db_pool = get_db_pool(&app_handle).await?;

    let existing_book = get_book_by_id(app_handle.clone(), data.id.clone()).await?;
    if let Some(book) = existing_book {
        return Err(format!("书籍已存在: {} (ID: {})", book.title, book.id));
    }

    let app_data_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("获取应用目录失败: {}", e))?;

    let books_dir = app_data_dir.join("books");
    let book_dir = books_dir.join(&data.id);
    fs::create_dir_all(&book_dir).map_err(|e| format!("创建目录失败: {}", e))?;

    let epub_filename = format!("book.{}", data.format.to_lowercase());
    let epub_path = book_dir.join(&epub_filename);
    std::fs::rename(&data.temp_file_path, &epub_path)
        .map_err(|e| format!("移动书籍文件失败: {}", e))?;

    let cover_path = if let Some(cover_temp_path) = &data.cover_temp_file_path {
        let cover_file = book_dir.join("cover.jpg");
        std::fs::rename(cover_temp_path, &cover_file)
            .map_err(|e| format!("移动封面文件失败: {}", e))?;
        Some(format!("books/{}/cover.jpg", data.id))
    } else {
        None
    };

    let metadata_path = book_dir.join("metadata.json");
    let metadata_json = serde_json::to_string_pretty(&data.metadata)
        .map_err(|e| format!("序列化元数据失败: {}", e))?;
    fs::write(&metadata_path, metadata_json).map_err(|e| format!("保存元数据失败: {}", e))?;

    let file_path = format!("books/{}/{}", data.id, epub_filename);
    let now = chrono::Utc::now().timestamp();

    let mut tx = db_pool
        .begin()
        .await
        .map_err(|e| format!("开启事务失败: {}", e))?;

    sqlx::query(
        r#"
        INSERT INTO books (
            id, title, author, format, file_path, cover_path,
            file_size, language, tags,
            created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        "#,
    )
    .bind(&data.id)
    .bind(&data.title)
    .bind(&data.author)
    .bind(&data.format)
    .bind(&file_path)
    .bind(&cover_path)
    .bind(data.file_size)
    .bind(&data.language)
    .bind(None::<String>) // tags
    .bind(now)
    .bind(now)
    .execute(&mut *tx)
    .await
    .map_err(|e| format!("数据库插入失败: {}", e))?;

    sqlx::query(
        r#"
        INSERT INTO book_status (
            book_id, status, progress_current, progress_total,
            reading_time_minutes, metadata, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        "#,
    )
    .bind(&data.id)
    .bind("unread")
    .bind(0i64)
    .bind(0i64)
    .bind(0i64)
    .bind(None::<String>)
    .bind(now)
    .bind(now)
    .execute(&mut *tx)
    .await
    .map_err(|e| format!("创建书籍状态失败: {}", e))?;

    tx.commit()
        .await
        .map_err(|e| format!("提交事务失败: {}", e))?;

    Ok(SimpleBook::new(
        data.id,
        data.title,
        data.author,
        data.format,
        file_path,
        cover_path,
        data.file_size,
        data.language,
    ))
}

#[tauri::command]
pub async fn get_books(
    app_handle: AppHandle,
    options: Option<BookQueryOptions>,
) -> Result<Vec<SimpleBook>, String> {
    let db_pool = get_db_pool(&app_handle).await?;
    let opts = options.unwrap_or_default();

    let mut query = String::from("SELECT * FROM books");
    let mut conditions = Vec::new();

    if let Some(search_query) = &opts.search_query {
        if !search_query.trim().is_empty() {
            conditions.push(format!(
                "(title LIKE '%{}%' OR author LIKE '%{}%')",
                search_query.replace('\'', "''"),
                search_query.replace('\'', "''")
            ));
        }
    }

    if let Some(tags) = &opts.tags {
        if !tags.is_empty() {
            let tag_conditions: Vec<String> = tags
                .iter()
                .map(|tag| format!("tags LIKE '%\"{}\"%%'", tag.replace('\'', "''")))
                .collect();
            conditions.push(format!("({})", tag_conditions.join(" OR ")));
        }
    }

    if !conditions.is_empty() {
        query.push_str(&format!(" WHERE {}", conditions.join(" AND ")));
    }

    let sort_by = opts.sort_by.as_deref().unwrap_or("updated_at");
    let sort_order = opts.sort_order.as_deref().unwrap_or("desc");
    query.push_str(&format!(
        " ORDER BY {} {}",
        sort_by,
        sort_order.to_uppercase()
    ));

    if let Some(limit) = opts.limit {
        query.push_str(&format!(" LIMIT {}", limit));
        if let Some(offset) = opts.offset {
            query.push_str(&format!(" OFFSET {}", offset));
        }
    }

    let rows = sqlx::query(&query)
        .fetch_all(&db_pool)
        .await
        .map_err(|e| format!("查询书籍失败: {}", e))?;

    let books: Result<Vec<SimpleBook>, sqlx::Error> =
        rows.iter().map(SimpleBook::from_db_row).collect();

    books.map_err(|e| format!("转换查询结果失败: {}", e))
}

#[tauri::command]
pub async fn get_book_by_id(
    app_handle: AppHandle,
    id: String,
) -> Result<Option<SimpleBook>, String> {
    let db_pool = get_db_pool(&app_handle).await?;

    let row = sqlx::query("SELECT * FROM books WHERE id = ?")
        .bind(&id)
        .fetch_optional(&db_pool)
        .await
        .map_err(|e| format!("查询书籍失败: {}", e))?;

    match row {
        Some(row) => Ok(Some(
            SimpleBook::from_db_row(&row).map_err(|e| format!("转换查询结果失败: {}", e))?,
        )),
        None => Ok(None),
    }
}

#[tauri::command]
pub async fn update_book(
    app_handle: AppHandle,
    id: String,
    update_data: BookUpdateData,
) -> Result<SimpleBook, String> {
    let db_pool = get_db_pool(&app_handle).await?;
    let now = update_data
        .updated_at
        .unwrap_or_else(|| chrono::Utc::now().timestamp());

    if let Some(title) = &update_data.title {
        sqlx::query("UPDATE books SET title = ?, updated_at = ? WHERE id = ?")
            .bind(title)
            .bind(now)
            .bind(&id)
            .execute(&db_pool)
            .await
            .map_err(|e| format!("更新标题失败: {}", e))?;
    }

    if let Some(author) = &update_data.author {
        sqlx::query("UPDATE books SET author = ?, updated_at = ? WHERE id = ?")
            .bind(author)
            .bind(now)
            .bind(&id)
            .execute(&db_pool)
            .await
            .map_err(|e| format!("更新作者失败: {}", e))?;
    }

    if let Some(tags) = &update_data.tags {
        let tags_json =
            serde_json::to_string(tags).map_err(|e| format!("序列化标签失败: {}", e))?;
        sqlx::query("UPDATE books SET tags = ?, updated_at = ? WHERE id = ?")
            .bind(tags_json)
            .bind(now)
            .bind(&id)
            .execute(&db_pool)
            .await
            .map_err(|e| format!("更新标签失败: {}", e))?;
    }

    if update_data.title.is_none() && update_data.author.is_none() && update_data.tags.is_none() {
        sqlx::query("UPDATE books SET updated_at = ? WHERE id = ?")
            .bind(now)
            .bind(&id)
            .execute(&db_pool)
            .await
            .map_err(|e| format!("更新时间戳失败: {}", e))?;
    }

    get_book_by_id(app_handle, id)
        .await?
        .ok_or_else(|| "更新后无法找到书籍".to_string())
}

#[tauri::command]
pub async fn delete_book(app_handle: AppHandle, id: String) -> Result<(), String> {
    let db_pool = get_db_pool(&app_handle).await?;

    let app_data_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("获取应用目录失败: {}", e))?;

    let book_dir = app_data_dir.join("books").join(&id);
    if book_dir.exists() {
        std::fs::remove_dir_all(&book_dir).map_err(|e| format!("删除书籍文件失败: {}", e))?;
    }

    // 先删除书籍状态（不依赖外键级联）
    let _ = sqlx::query("DELETE FROM book_status WHERE book_id = ?")
        .bind(&id)
        .execute(&db_pool)
        .await
        .map_err(|e| format!("删除书籍状态失败: {}", e))?;

    let result = sqlx::query("DELETE FROM books WHERE id = ?")
        .bind(&id)
        .execute(&db_pool)
        .await
        .map_err(|e| format!("删除书籍失败: {}", e))?;

    if result.rows_affected() == 0 {
        return Err("书籍不存在".to_string());
    }

    Ok(())
}

#[tauri::command]
pub async fn get_book_status(
    app_handle: AppHandle,
    book_id: String,
) -> Result<Option<BookStatus>, String> {
    let db_pool = get_db_pool(&app_handle).await?;

    let result = sqlx::query("SELECT * FROM book_status WHERE book_id = ?")
        .bind(&book_id)
        .fetch_optional(&db_pool)
        .await
        .map_err(|e| format!("查询书籍状态失败: {}", e))?;

    match result {
        Some(row) => Ok(Some(
            BookStatus::from_db_row(&row).map_err(|e| format!("解析数据失败: {}", e))?,
        )),
        None => Ok(None),
    }
}

#[tauri::command]
pub async fn update_book_status(
    app_handle: AppHandle,
    book_id: String,
    update_data: BookStatusUpdateData,
) -> Result<BookStatus, String> {
    let db_pool = get_db_pool(&app_handle).await?;
    let now = chrono::Utc::now().timestamp();

    let current_status = get_book_status(app_handle.clone(), book_id.clone())
        .await?
        .ok_or_else(|| "书籍状态不存在".to_string())?;

    let new_status = update_data.status.unwrap_or(current_status.status);
    let new_progress_current = update_data
        .progress_current
        .unwrap_or(current_status.progress_current);
    let new_progress_total = update_data
        .progress_total
        .unwrap_or(current_status.progress_total);
    let new_last_reading_position = update_data
        .last_reading_position
        .or(current_status.last_reading_position);
    let new_reading_time_minutes = update_data
        .reading_time_minutes
        .unwrap_or(current_status.reading_time_minutes);
    let new_last_read_at = update_data.last_read_at.or(current_status.last_read_at);
    let new_started_at = update_data.started_at.or(current_status.started_at);
    let new_completed_at = update_data.completed_at.or(current_status.completed_at);
    let new_metadata = update_data.metadata.or(current_status.metadata);

    let result = sqlx::query(
        r#"
        UPDATE book_status SET 
            status = ?, progress_current = ?, progress_total = ?, 
            last_reading_position = ?, reading_time_minutes = ?, 
            last_read_at = ?, started_at = ?, completed_at = ?, metadata = ?, updated_at = ?
        WHERE book_id = ?
        "#,
    )
    .bind(&new_status)
    .bind(new_progress_current)
    .bind(new_progress_total)
    .bind(&new_last_reading_position)
    .bind(new_reading_time_minutes)
    .bind(new_last_read_at)
    .bind(new_started_at)
    .bind(new_completed_at)
    .bind(
        new_metadata
            .as_ref()
            .map(|v| serde_json::to_string(v).unwrap_or_default()),
    )
    .bind(now)
    .bind(&book_id)
    .execute(&db_pool)
    .await
    .map_err(|e| format!("更新书籍状态失败: {}", e))?;

    if result.rows_affected() == 0 {
        return Err("书籍状态不存在".to_string());
    }

    get_book_status(app_handle, book_id)
        .await?
        .ok_or_else(|| "更新后无法找到书籍状态".to_string())
}

#[tauri::command]
pub async fn get_books_with_status(
    app_handle: AppHandle,
    options: Option<BookQueryOptions>,
) -> Result<Vec<BookWithStatus>, String> {
    let db_pool = get_db_pool(&app_handle).await?;
    let opts = options.unwrap_or_default();

    let mut query = String::from(
        "SELECT b.*, s.book_id as status_book_id, s.status, s.progress_current, s.progress_total, 
         s.last_reading_position, s.reading_time_minutes, s.last_read_at, s.started_at, 
         s.completed_at, s.metadata, s.created_at as status_created_at, s.updated_at as status_updated_at 
         FROM books b LEFT JOIN book_status s ON b.id = s.book_id"
    );
    let mut conditions = Vec::new();

    if let Some(search_query) = &opts.search_query {
        if !search_query.trim().is_empty() {
            conditions.push("(b.title LIKE ? OR b.author LIKE ?)");
        }
    }

    let tag_condition = if let Some(tags) = &opts.tags {
        if !tags.is_empty() {
            let tag_conditions: Vec<String> =
                tags.iter().map(|_| "b.tags LIKE ?".to_string()).collect();
            Some(format!("({})", tag_conditions.join(" OR ")))
        } else {
            None
        }
    } else {
        None
    };

    if let Some(ref condition) = tag_condition {
        conditions.push(condition);
    }

    if !conditions.is_empty() {
        query.push_str(&format!(" WHERE {}", conditions.join(" AND ")));
    }

    if let Some(sort_by) = &opts.sort_by {
        let order = opts.sort_order.as_deref().unwrap_or("asc");
        match sort_by.as_str() {
            "title" => query.push_str(&format!(" ORDER BY b.title {}", order)),
            "author" => query.push_str(&format!(" ORDER BY b.author {}", order)),
            "createdAt" => query.push_str(&format!(" ORDER BY b.created_at {}", order)),
            "updatedAt" => query.push_str(&format!(" ORDER BY b.updated_at {}", order)),
            _ => query.push_str(" ORDER BY b.updated_at DESC"),
        }
    } else {
        query.push_str(" ORDER BY b.updated_at DESC");
    }

    if let Some(limit) = opts.limit {
        query.push_str(&format!(" LIMIT {}", limit));
        if let Some(offset) = opts.offset {
            query.push_str(&format!(" OFFSET {}", offset));
        }
    }

    let mut sql_query = sqlx::query(&query);

    let search_patterns = if let Some(search_query) = &opts.search_query {
        if !search_query.trim().is_empty() {
            let pattern = format!("%{}%", search_query);
            Some((pattern.clone(), pattern))
        } else {
            None
        }
    } else {
        None
    };

    if let Some((pattern1, pattern2)) = &search_patterns {
        sql_query = sql_query.bind(pattern1).bind(pattern2);
    }

    let tag_patterns: Vec<String> = if let Some(tags) = &opts.tags {
        tags.iter().map(|tag| format!("%\"{}\"", tag)).collect()
    } else {
        Vec::new()
    };

    for tag_pattern in &tag_patterns {
        sql_query = sql_query.bind(tag_pattern);
    }

    let rows = sql_query
        .fetch_all(&db_pool)
        .await
        .map_err(|e| format!("查询书籍失败: {}", e))?;

    let mut results = Vec::new();
    for row in rows {
        let book = SimpleBook::from_db_row(&row).map_err(|e| format!("解析书籍数据失败: {}", e))?;

        let status = if row
            .try_get::<Option<String>, _>("status_book_id")
            .unwrap_or(None)
            .is_some()
        {
            Some(BookStatus {
                book_id: row.try_get("status_book_id").unwrap_or_default(),
                status: row.try_get("status").unwrap_or_default(),
                progress_current: row.try_get("progress_current").unwrap_or_default(),
                progress_total: row.try_get("progress_total").unwrap_or_default(),
                last_reading_position: row.try_get("last_reading_position").unwrap_or_default(),
                reading_time_minutes: row.try_get("reading_time_minutes").unwrap_or_default(),
                last_read_at: row.try_get("last_read_at").unwrap_or_default(),
                started_at: row.try_get("started_at").unwrap_or_default(),
                completed_at: row.try_get("completed_at").unwrap_or_default(),
                metadata: {
                    let metadata_str: Option<String> = row.try_get("metadata").unwrap_or_default();
                    metadata_str.and_then(|s| serde_json::from_str(&s).ok())
                },
                created_at: row.try_get("status_created_at").unwrap_or_default(),
                updated_at: row.try_get("status_updated_at").unwrap_or_default(),
            })
        } else {
            None
        };

        results.push(BookWithStatus { book, status });
    }

    Ok(results)
}

async fn get_db_pool(app_handle: &AppHandle) -> Result<SqlitePool, String> {
    let app_data_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("获取应用目录失败: {}", e))?;

    let db_path = app_data_dir.join("database").join("app.db");
    let db_url = format!("sqlite:{}", db_path.display());

    SqlitePool::connect(&db_url)
        .await
        .map_err(|e| format!("数据库连接失败: {}", e))
}

impl Default for BookQueryOptions {
    fn default() -> Self {
        Self {
            limit: None,
            offset: None,
            search_query: None,
            tags: None,
            sort_by: None,
            sort_order: None,
        }
    }
}
