use std::process::Command;
use tauri::{App<PERSON><PERSON><PERSON>};

use super::{
    app_data_dir, backends_root, lib_root, llama_backend_download_url, llama_backend_path,
    llama_server_binary_name, models_root,
};

#[tauri::command]
pub fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
pub async fn get_app_data_dir(app: AppHandle) -> Result<String, String> {
    app_data_dir(&app).map(|p| p.to_string_lossy().to_string())
}

#[tauri::command]
pub async fn get_llamacpp_backend_path(app: AppHandle) -> Result<String, String> {
    let path = llama_backend_path(&app)?;
    Ok(path.to_string_lossy().to_string())
}

#[tauri::command]
pub async fn ensure_llamacpp_directories(app: AppHandle) -> Result<String, String> {
    let llamacpp_dir = app_data_dir(&app)?.join(super::LLAMACPP_DIR);
    let backends_dir = backends_root(&app)?;
    let models_dir = models_root(&app)?;
    let lib_dir = lib_root(&app)?;

    std::fs::create_dir_all(&backends_dir)
        .map_err(|e| format!("Failed to create backends directory: {}", e))?;
    std::fs::create_dir_all(&models_dir)
        .map_err(|e| format!("Failed to create models directory: {}", e))?;
    std::fs::create_dir_all(&lib_dir)
        .map_err(|e| format!("Failed to create lib directory: {}", e))?;

    log::info!("Created llamacpp directories at: {}", llamacpp_dir.display());
    Ok(format!(
        "LlamaCpp directories created at: {}",
        llamacpp_dir.to_string_lossy()
    ))
}

#[tauri::command]
pub async fn download_llama_server(app: AppHandle) -> Result<String, String> {
    let backend_path = llama_backend_path(&app)?;

    if backend_path.exists() {
        return Ok(format!(
            "llama-server already exists at: {}",
            backend_path.to_string_lossy()
        ));
    }

    log::info!("开始下载 llama-server 到: {}", backend_path.display());

    if let Some(parent) = backend_path.parent() {
        std::fs::create_dir_all(parent)
            .map_err(|e| format!("Failed to create parent directory: {}", e))?;
    }

    let download_url = llama_backend_download_url()?;
    log::info!("正在从 {} 下载后端", download_url);

    let temp_file = format!("{}.download", backend_path.to_string_lossy());
    let download_result = Command::new("curl")
        .args(["-L", "-o", &temp_file, &download_url])
        .output();

    match download_result {
        Ok(output) if output.status.success() => {
            log::info!("下载完成，开始解压...");

            // Extract to backends/<version>/
            let extract_dir = backend_path
                .parent()
                .and_then(|p| p.parent())
                .and_then(|p| p.parent())
                .ok_or_else(|| "Failed to resolve extract dir".to_string())?;

            log::info!("解压到目录: {}", extract_dir.display());

            let extract_result = Command::new("unzip")
                .args(["-o", &temp_file, "-d", extract_dir.to_str().unwrap()])
                .output();

            match extract_result {
                Ok(extract_output) if extract_output.status.success() => {
                    let _ = std::fs::remove_file(&temp_file);

                    if backend_path.exists() {
                        #[cfg(unix)]
                        {
                            use std::os::unix::fs::PermissionsExt;
                            let mut perms = std::fs::metadata(&backend_path)
                                .map_err(|e| e.to_string())?
                                .permissions();
                            perms.set_mode(0o755);
                            let _ = std::fs::set_permissions(&backend_path, perms);
                        }

                        Ok(format!(
                            "✅ llama-server 下载并解压成功: {}",
                            backend_path.to_string_lossy()
                        ))
                    } else {
                        Err(format!(
                            "解压后未找到 llama-server 文件: {}",
                            backend_path.to_string_lossy()
                        ))
                    }
                }
                Ok(extract_output) => {
                    let stderr = String::from_utf8_lossy(&extract_output.stderr);
                    Err(format!("解压失败: {}", stderr))
                }
                Err(e) => Err(format!("解压命令执行失败: {}", e)),
            }
        }
        Ok(output) => {
            let stderr = String::from_utf8_lossy(&output.stderr);
            Err(format!("下载失败: {}", stderr))
        }
        Err(e) => Err(format!("下载命令执行失败: {}", e)),
    }
}

// Utility: expose only the binary name for consumers if needed
#[tauri::command]
pub fn llama_server_binary_name_cmd() -> String {
    llama_server_binary_name().to_string()
}
