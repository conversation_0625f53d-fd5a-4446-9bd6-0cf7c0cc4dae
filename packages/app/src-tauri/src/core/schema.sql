CREATE TABLE IF NOT EXISTS threads (
    id TEXT PRIMARY KEY NOT NULL,
    book_key TEXT NOT NULL,
    metadata TEXT NOT NULL,
    title TEXT NOT NULL,
    messages TEXT NOT NULL,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS books (
    id TEXT PRIMARY KEY NOT NULL,
    title TEXT NOT NULL,
    author TEXT NOT NULL,
    format TEXT NOT NULL,
    file_path TEXT NOT NULL,
    cover_path TEXT,
    
    file_size INTEGER NOT NULL,
    language TEXT NOT NULL,
    
    tags TEXT,
    
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS book_status (
    book_id TEXT PRIMARY KEY NOT NULL,
    status TEXT NOT NULL DEFAULT 'unread',  -- 'unread', 'reading', 'completed'
    progress_current INTEGER DEFAULT 0,
    progress_total INTEGER DEFAULT 0,
    last_reading_position TEXT,
    reading_time_minutes INTEGER DEFAULT 0,
    last_read_at INTEGER,
    started_at INTEGER,
    completed_at INTEGER,
    metadata TEXT,                 -- JSON 存储其他信息（设置、偏好等）
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);



CREATE INDEX IF NOT EXISTS idx_books_title ON books(title);
CREATE INDEX IF NOT EXISTS idx_books_author ON books(author);
CREATE INDEX IF NOT EXISTS idx_books_updated_at ON books(updated_at DESC);

CREATE INDEX IF NOT EXISTS idx_book_status_status ON book_status(status);
CREATE INDEX IF NOT EXISTS idx_book_status_progress ON book_status(progress_current, progress_total);
CREATE INDEX IF NOT EXISTS idx_book_status_last_read ON book_status(last_read_at DESC);
CREATE INDEX IF NOT EXISTS idx_book_status_updated_at ON book_status(updated_at DESC);

CREATE INDEX IF NOT EXISTS idx_threads_book_key ON threads(book_key);

CREATE TABLE IF NOT EXISTS tags (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT NOT NULL UNIQUE,
    color TEXT,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL
);

CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name);
CREATE INDEX IF NOT EXISTS idx_tags_updated_at ON tags(updated_at DESC);