.chrome-tabs.chrome-tabs-dark-theme {
  background: transparent;
}
.chrome-tabs.chrome-tabs-dark-theme .chrome-tab .chrome-tab-dividers::after {
  background: #6b7280;
}
.chrome-tabs.chrome-tabs-dark-theme .chrome-tab .chrome-tab-background > svg .chrome-tab-geometry {
  display: none;
}
.chrome-tabs.chrome-tabs-dark-theme .chrome-tab[active] .chrome-tab-background > svg .chrome-tab-geometry {
  display: none;
}
.chrome-tabs.chrome-tabs-dark-theme .chrome-tab {
  background: transparent;
}
.chrome-tabs.chrome-tabs-dark-theme .chrome-tab[active] {
  background: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}
@media (hover: hover) {
  .chrome-tabs.chrome-tabs-dark-theme .chrome-tab:not([active]):hover {
    background: transparent;
  }
}
.chrome-tabs.chrome-tabs-dark-theme .chrome-tab .chrome-tab-title {
  color: #9ca3af;
}
.chrome-tabs.chrome-tabs-dark-theme .chrome-tab[active] .chrome-tab-title {
  color: #262626;
}
.chrome-tabs.chrome-tabs-dark-theme .chrome-tab .chrome-tab-close {
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path stroke='rgba(163, 163, 163, .8)' stroke-linecap='square' stroke-width='1.5' d='M0 0 L8 8 M8 0 L0 8'></path></svg>");
}
.chrome-tabs.chrome-tabs-dark-theme .chrome-tab .chrome-tab-close:hover {
  background-color: #737373;
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path stroke='rgba(229, 229, 229, .9)' stroke-linecap='square' stroke-width='1.5' d='M0 0 L8 8 M8 0 L0 8'></path></svg>");
}
.chrome-tabs.chrome-tabs-dark-theme .chrome-tab .chrome-tab-close:hover:active {
  background-color: #525252;
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path stroke='rgba(229, 229, 229, .9)' stroke-linecap='square' stroke-width='1.5' d='M0 0 L8 8 M8 0 L0 8'></path></svg>");
}
.chrome-tabs.chrome-tabs-dark-theme .chrome-tabs-bottom-bar {
  display: none;
}
