{"name": "foliate-js", "version": "0.0.0", "description": "Render e-books in the browser", "repository": {"type": "git", "url": "git+https://github.com/johnfactotum/foliate-js.git"}, "bugs": {"url": "https://github.com/johnfactotum/foliate-js/issues"}, "homepage": "https://github.com/johnfactotum/foliate-js#readme", "author": "<PERSON>", "license": "MIT", "type": "module", "exports": {"./*.js": "./*.js"}, "devDependencies": {"@eslint/js": "^9.9.1", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.4", "@zip.js/zip.js": "^2.7.52", "fflate": "^0.8.2", "fs-extra": "^11.2.0", "globals": "^15.9.0", "pdfjs-dist": "^4.7.76", "rollup": "^4.22.4"}, "scripts": {"build": "npx rollup -c"}, "keywords": ["ebook", "reader", "epub", "cfi", "mobi", "azw", "azw3", "fb2", "cbz", "dictd", "stardict", "opds"]}