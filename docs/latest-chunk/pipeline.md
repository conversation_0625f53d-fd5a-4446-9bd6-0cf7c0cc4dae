# 核心流水线模块

## 概述

核心流水线模块是整个 EPUB 处理系统的中枢，负责协调各个处理模块，实现从 EPUB 文件到向量数据库的完整转换流程。该模块采用流水线架构，支持增量处理、错误恢复和性能优化。

## 模块结构

```
pipeline.rs                # 核心流水线实现
├── process_epub_to_db()   # 主要处理函数
├── search_db()            # 搜索功能
├── write_metadata_markdown() # 元数据处理
└── sanitize_filename()    # 文件名处理
```

## 核心功能

### 1. 主处理流水线

**函数签名**：
```rust
pub async fn process_epub_to_db<P: AsRef<Path>, F>(
    book_dir: P,
    opts: ProcessOptions,
    mut on_progress: Option<F>,
) -> Result<ProcessReport>
where
    F: FnMut(ProgressUpdate) + Send,
```

**处理步骤**：

#### Step 1: EPUB 文件验证和读取
```rust
// 验证 EPUB 文件存在
let epub_path = book_dir.join("book.epub");
if !epub_path.exists() {
    anyhow::bail!("EPUB not found: {:?}", epub_path);
}

// 初始化 EPUB 读取器
let reader = EpubReader::new()?;
let epub_content = reader.read_epub(&epub_path)?;
```

#### Step 2: EPUB 到 MDBook 转换
```rust
// 转换为 MDBook 格式
let mdbook_dir = book_dir.join("mdbook");
if need_conversion {
    convert_epub_to_mdbook(&epub_path, &mdbook_dir, true)?;
}
```

#### Step 3: TOC 解析和扁平化
```rust
// 查找和解析 TOC 文件
let toc_path = find_toc_ncx_in_mdbook(&mdbook_dir)?;
let toc_nodes = parse_toc_file(&toc_path)?;
let flat_toc = flatten_toc(&toc_nodes);
```

#### Step 4: 文件分组和层级路径生成
```rust
// 按 md_src 分组 TOC 节点
let mut md_groups: BTreeMap<String, Vec<(usize, FlatTocNode)>> = BTreeMap::new();
for (idx, node) in flat_toc.iter().cloned().enumerate() {
    md_groups.entry(node.md_src.clone()).or_default().push((idx, node));
}

// 为每个文件生成完整的层级路径
for (md_src, nodes) in md_groups.iter() {
    let deepest_node = nodes.iter().max_by_key(|(_, node)| node.depth);
    let related_chapter_titles = if let Some((_, node)) = deepest_node {
        node.hierarchy_path.join("|")
    } else {
        String::new()
    };
}
```

#### Step 5: 流水线处理（分片→向量化→入库）
```rust
// 逐文件处理
for (file_index, (md_src, nodes)) in md_groups.iter().enumerate() {
    // 读取 MD 文件
    let md_content = fs::read_to_string(&md_file_path)?;
    
    // 分片处理
    let chunks = reader.chunk_md_file(&md_content, 50, 400);
    
    // 批量向量化和入库
    for chunk_content in chunks {
        let embedding = vectorizer.vectorize_text(&chunk_content).await?;
        
        let chunk = DocumentChunk {
            book_title: epub_content.title.clone(),
            book_author: epub_content.author.clone(),
            md_file_path: md_src.to_string(),
            related_chapter_titles: related_chapter_titles.clone(),
            chunk_text: chunk_content,
            embedding,
            // ... 其他字段
        };
        
        db.insert_chunk(&chunk)?;
    }
}
```

### 2. 层级路径处理（新特性）

**核心改进**：支持完整的层级路径生成

```rust
// 生成章节标题字符串：使用最深层级节点的完整层级路径
let related_chapter_titles = if let Some(deepest_node) = related_toc_nodes
    .iter()
    .max_by_key(|node| node.depth) {
    // 使用最深层级节点的完整层级路径
    deepest_node.hierarchy_path.join("|")
} else {
    // 兜底：如果没有节点，使用空字符串
    String::new()
};
```

**示例效果**：
- 原来：`"第一章 人类：一种也没什么特别的动物"`
- 现在：`"第一部分 认知革命|第一章 人类：一种也没什么特别的动物"`

### 3. 进度报告系统

```rust
pub struct ProgressUpdate {
    pub current: usize,                    // 当前进度
    pub total: usize,                      // 总数
    pub percent: f32,                      // 百分比
    pub md_file_path: String,              // 当前文件
    pub chunk_index: usize,                // 当前分片索引
    pub related_chapter_titles: String,   // 相关章节标题
}

// 进度回调使用
if let Some(cb) = on_progress.as_mut() {
    let file_progress = ((chunk_index + 1) as f32 / total_chunks_in_file as f32) * 100.0;
    let overall_progress = ((file_index as f32 + file_progress / 100.0) / total_files as f32) * 100.0;
    
    cb(ProgressUpdate {
        current: total_processed_chunks + chunk_index + 1,
        total: estimated_total,
        percent: overall_progress,
        md_file_path: md_src.to_string(),
        chunk_index,
        related_chapter_titles: related_chapter_titles.clone(),
    });
}
```

### 4. 错误统计和处理

```rust
pub struct ErrorStats {
    pub failed_files: usize,
    pub failed_chunks: usize,
    pub failed_db_operations: usize,
    pub file_errors: Vec<String>,
}

impl ErrorStats {
    pub fn new() -> Self;
    pub fn add_file_error(&mut self, file: &str, error: &str);
    pub fn add_chunk_error(&mut self);
    pub fn add_db_error(&mut self);
}

// 错误处理示例
let md_content = match fs::read_to_string(&md_file_path) {
    Ok(content) => content,
    Err(e) => {
        let error_msg = format!("Failed to read MD file {:?}: {}", md_file_path, e);
        log::error!("{}", error_msg);
        error_stats.add_file_error(md_src, &e.to_string());
        continue; // 跳过这个文件，继续处理其他文件
    }
};
```

### 5. 元数据处理

```rust
fn write_metadata_markdown(
    book_dir: &Path, 
    epub_content: &EpubContent, 
    flat_toc: &[FlatTocNode], 
    toc_base_dir: &Path
) -> Result<()> {
    // 读取现有 metadata.json
    let metadata_path = book_dir.join("metadata.json");
    let meta_file: Option<BookMetadataFile> = match fs::read_to_string(&metadata_path) {
        Ok(s) => serde_json::from_str(&s).ok(),
        Err(_) => None,
    };
    
    // 合并字段，EPUB 信息作为兜底
    let title = meta_file.as_ref()
        .and_then(|m| m.title.as_ref())
        .cloned()
        .unwrap_or_else(|| epub_content.title.clone());
    
    // 生成 metadata.md
    let mut md = String::new();
    md.push_str(&format!("# {}\n\n", title));
    md.push_str("## 目录\n\n");
    
    for node in flat_toc {
        let indent = "  ".repeat(node.depth as usize);
        md.push_str(&format!("{}- {}\n", indent, node.title));
    }
    
    fs::write(book_dir.join("metadata.md"), md)?;
    Ok(())
}
```

## 配置选项

### 处理选项

```rust
#[derive(Debug, Clone)]
pub struct ProcessOptions {
    pub dimension: usize,                  // 向量维度
    pub vectorizer: VectorizerConfig,      // 向量化配置
    pub batch_size: Option<usize>,         // 批处理大小
}

#[derive(Debug, Clone)]
pub struct VectorizerConfig {
    pub base_url: String,                  // API 基础 URL
    pub model_name: String,                // 模型名称
    pub api_key: Option<String>,           // API 密钥
}
```

### 处理报告

```rust
#[derive(Debug)]
pub struct ProcessReport {
    pub db_path: PathBuf,                  // 数据库路径
    pub book_title: String,                // 书籍标题
    pub book_author: String,               // 书籍作者
    pub total_chunks: usize,               // 总分片数
    pub vector_dimension: usize,           // 向量维度
}
```

## 性能优化

### 1. 流水线处理

```rust
// 边分片边向量化，避免内存积累
for (chunk_index, chunk_content) in chunks.into_iter().enumerate() {
    // 立即向量化
    let embedding = vectorizer.vectorize_text(&chunk_content).await?;
    
    // 立即入库
    let chunk = DocumentChunk { /* ... */ };
    db.insert_chunk(&chunk)?;
    
    // 不保存在内存中，立即释放
}
```

### 2. 批量操作

```rust
// 批量入库优化
let mut file_batch: Option<Vec<DocumentChunk>> = None;

for chunk in chunks {
    if file_batch.is_none() {
        file_batch = Some(Vec::new());
    }
    file_batch.as_mut().unwrap().push(chunk);
    
    // 达到批次大小时入库
    if file_batch.as_ref().unwrap().len() >= batch_size {
        db.insert_chunks_batch(file_batch.as_ref().unwrap())?;
        file_batch = Some(Vec::new());
    }
}

// 处理剩余分片
if let Some(batch) = file_batch {
    if !batch.is_empty() {
        db.insert_chunks_batch(&batch)?;
    }
}
```

### 3. 内存管理

```rust
// 及时释放不需要的数据
drop(epub_content); // 在不需要时释放

// 流式处理大文件
for (file_index, (md_src, nodes)) in md_groups.iter().enumerate() {
    // 处理单个文件
    process_single_file(md_src, nodes)?;
    
    // 文件处理完成后，相关数据自动释放
}
```

## 错误处理策略

### 1. 分层错误处理

```rust
// 文件级错误：跳过文件，继续处理
match fs::read_to_string(&md_file_path) {
    Ok(content) => content,
    Err(e) => {
        error_stats.add_file_error(md_src, &e.to_string());
        continue; // 跳过这个文件
    }
}

// 分片级错误：跳过分片，继续处理
match vectorizer.vectorize_text(&chunk_content).await {
    Ok(emb) => emb,
    Err(e) => {
        error_stats.add_chunk_error();
        continue; // 跳过这个分片
    }
}

// 数据库级错误：记录但继续
if let Err(e) = db.insert_chunks_batch(&batch) {
    error_stats.add_db_error();
    log::error!("批量入库失败: {}", e);
}
```

### 2. 容错机制

```rust
// 空文件处理
if md_content.trim().is_empty() {
    log::debug!("跳过空文件: {}", md_src);
    continue;
}

// 无效分片处理
let chunks = reader.chunk_md_file(&md_content, 50, 400);
if chunks.is_empty() {
    log::debug!("文件无有效分片: {}", md_src);
    continue;
}
```

## 使用示例

### 基本使用

```rust
use crate::pipeline::{process_epub_to_db, ProcessOptions};
use crate::models::{VectorizerConfig, ProgressUpdate};

let opts = ProcessOptions {
    dimension: 1024,
    vectorizer: VectorizerConfig {
        base_url: "http://localhost:3544".to_string(),
        model_name: "local-embed".to_string(),
        api_key: None,
    },
    batch_size: Some(10),
};

// 带进度回调的处理
let report = process_epub_to_db(
    &book_dir,
    opts,
    Some(|progress: ProgressUpdate| {
        println!("进度: {:.1}% - 文件: {} - 分片: {}", 
            progress.percent, 
            progress.md_file_path, 
            progress.chunk_index
        );
    })
).await?;

println!("处理完成:");
println!("- 书籍: {}", report.book_title);
println!("- 作者: {}", report.book_author);
println!("- 总分片数: {}", report.total_chunks);
println!("- 向量维度: {}", report.vector_dimension);
```

### 搜索功能

```rust
use crate::pipeline::search_db;

let search_results = search_db(
    &book_dir,
    "搜索查询",
    5,  // 限制结果数
    1024,  // 向量维度
    vectorizer_config,
).await?;

for result in search_results {
    println!("相似度: {:.3}", result.similarity);
    println!("章节: {}", result.related_chapter_titles);
    println!("内容: {}", &result.chunk_text[..100]);
    println!("---");
}
```

## 监控和日志

### 日志级别

```rust
// 信息级别
log::info!("Starting EPUB processing pipeline for book directory: {:?}", book_dir);
log::info!("Loaded book: {} (author: {}), chapters: {}", title, author, chapter_count);

// 调试级别
log::debug!("向量化分片 {}/{} (文件: {})", chunk_index + 1, total_chunks, md_src);
log::debug!("跳过空文件: {}", md_src);

// 警告级别
log::warn!("生成 metadata.md 失败：{}", e);
log::warn!("MD file not found: {:?}, skipping", md_file_path);

// 错误级别
log::error!("向量化失败 (文件: {}, 分片: {}): {}", md_src, chunk_index, e);
log::error!("批量入库失败 (文件: {}): {}", md_src, e);
```

### 性能监控

```rust
// 处理时间统计
let start_time = std::time::Instant::now();
// ... 处理逻辑
let duration = start_time.elapsed();
log::info!("文件 {} 处理完成，耗时: {:?}", md_src, duration);

// 内存使用监控
let memory_usage = get_memory_usage();
if memory_usage > threshold {
    log::warn!("内存使用过高: {} MB", memory_usage / 1024 / 1024);
}
```
