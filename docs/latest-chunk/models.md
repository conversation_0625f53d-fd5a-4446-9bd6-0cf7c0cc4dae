# 数据模型

## 概述

`tauri-plugin-epub` 插件定义了完整的数据模型体系，涵盖了从EPUB解析到向量存储的整个数据流。数据模型分为以下几个主要类别：

- **核心数据模型**：业务逻辑的核心数据结构
- **DTO 数据传输对象**：用于前后端通信的序列化结构
- **配置模型**：系统配置和选项结构
- **辅助模型**：进度报告、错误统计等辅助结构

## 核心数据模型

### 1. DocumentChunk - 文档分片

**用途**：表示一个文档的分片单元，是整个系统的核心数据结构。

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentChunk {
    pub id: Option<i64>,                    // 数据库主键ID
    pub book_title: String,                 // 书籍标题
    pub book_author: String,                // 书籍作者

    // 文件信息
    pub md_file_path: String,               // MD文件路径 "text/part001.md"
    pub file_order_in_book: u32,            // 文件在书中的顺序

    // 章节关联信息（使用 | 分隔符）
    pub related_chapter_titles: String,     // "第一章 引言|1.1 背景介绍|1.2 研究目标"

    // 分片信息
    pub chunk_text: String,                 // 分片的实际文本内容
    pub chunk_order_in_file: usize,         // 在文件中的分片顺序
    pub total_chunks_in_file: usize,        // 该文件的总分片数

    // 向量信息
    pub embedding: Vec<f32>,                // 向量化后的嵌入向量

    // 全局位置信息
    pub global_chunk_index: usize,          // 在整本书中的全局分块序号
}
```

**字段说明**：
- `id`：数据库自增ID，新建时为None
- `related_chapter_titles`：用管道符`|`连接的相关章节标题，支持多层级章节
- `file_order_in_book`：确保跨文件检索时的正确排序
- `global_chunk_index`：全局唯一的分片索引，用于范围检索和上下文获取

### 2. SearchResult - 搜索结果

**用途**：向量搜索的结果结构，包含相似度分数。

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    pub chunk_id: i64,                     // 分片ID
    pub book_title: String,                // 书籍标题
    pub book_author: String,               // 书籍作者
    pub md_file_path: String,              // MD文件路径
    pub file_order_in_book: u32,           // 文件顺序
    pub related_chapter_titles: String,    // 相关章节标题
    pub chunk_text: String,                // 分片文本
    pub chunk_order_in_file: usize,        // 文件内顺序
    pub total_chunks_in_file: usize,       // 文件总分片数
    pub global_chunk_index: usize,         // 全局索引
    pub similarity_score: f32,             // 相似度分数 [0.0, 1.0]
}
```

**特点**：
- 继承了 `DocumentChunk` 的所有信息字段
- 额外包含 `similarity_score` 相似度分数
- 分数越高表示与查询越相似

## EPUB 处理模型

### 3. EpubContent - EPUB内容

**用途**：表示解析后的EPUB文件完整内容。

```rust
#[derive(Debug)]
pub struct EpubContent {
    pub title: String,                     // EPUB标题
    pub author: String,                    // EPUB作者
    pub chapters: Vec<EpubChapter>,        // 章节列表
}
```

### 4. EpubChapter - EPUB章节

**用途**：表示EPUB中的单个章节。

```rust
#[derive(Debug, Clone)]
pub struct EpubChapter {
    pub title: String,                     // 章节标题
    pub content: String,                   // 章节内容（HTML或文本）
    pub order: usize,                      // 章节顺序
}
```

### 5. TocNode - 目录节点

**用途**：表示EPUB目录的层级结构。

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TocNode {
    pub id: String,                        // 节点ID
    pub play_order: u32,                   // 播放顺序
    pub title: String,                     // 节点标题
    pub src: String,                       // 源文件路径
    pub children: Vec<TocNode>,            // 子节点
}
```

### 6. FlatTocNode - 扁平化目录节点

**用途**：将层级目录结构扁平化为线性列表，便于处理。

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FlatTocNode {
    pub id: String,                        // 节点ID
    pub play_order: u32,                   // 播放顺序
    pub title: String,                     // 节点标题
    pub md_src: String,                    // 转换后的MD文件路径（不包含锚点）
    pub depth: u32,                        // 节点深度，从0开始
    #[serde(default)]
    pub anchor: Option<String>,            // 原始src的锚点（如果有）
}
```

**转换过程**：
```
TocNode (层级) → FlatTocNode (扁平) → DocumentChunk (分片)
```

### 7. BookMetadataFile - 书籍元数据

**用途**：存储和管理书籍的完整元数据信息。

```rust
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct BookMetadataFile {
    pub title: Option<String>,             // 书籍标题
    pub language: Option<String>,          // 语言
    pub published: Option<String>,         // 出版日期
    pub publisher: Option<String>,         // 出版社
    pub author: Option<AuthorField>,       // 作者信息（支持多种格式）
    pub base_dir: Option<String>,          // 基础目录路径
}
```

### 8. AuthorField - 作者字段

**用途**：灵活支持多种作者信息格式。

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum AuthorField {
    String(String),                        // 简单字符串格式
    Person(PersonAuthor),                  // 单个作者对象
    List(Vec<PersonAuthor>),               // 多作者列表
}
```

### 9. PersonAuthor - 作者详细信息

```rust
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct PersonAuthor {
    pub name: Option<String>,              // 作者姓名
    pub role: Option<String>,              // 作者角色（作者、编辑等）
    pub sort_as: Option<String>,           // 排序名称
}
```

## 配置和选项模型

### 10. ProcessOptions - 处理选项

**用途**：控制EPUB处理流程的各种选项。

```rust
#[derive(Debug, Clone)]
pub struct ProcessOptions {
    pub dimension: usize,                  // 向量维度
    pub batch_size: Option<usize>,         // 批处理大小
    pub vectorizer: VectorizerConfig,      // 向量化器配置
}
```

### 11. VectorizerConfig - 向量化器配置

**用途**：配置向量化服务的连接参数。

```rust
#[derive(Debug, Clone)]
pub struct VectorizerConfig {
    pub base_url: String,                  // 向量化服务基础URL
    pub model_name: String,                // 模型名称
    pub api_key: Option<String>,           // API密钥（可选）
}
```

**支持的服务类型**：
- 本地 llama.cpp 服务：`http://localhost:3544`
- OpenAI API：`https://api.openai.com/v1`
- 自定义向量化端点

## 进度和报告模型

### 12. ProgressUpdate - 进度更新

**用途**：实时报告处理进度信息。

```rust
#[derive(Debug, Clone, Serialize)]
pub struct ProgressUpdate {
    pub current: usize,                    // 当前处理数量
    pub total: usize,                      // 总数量（估算）
    pub percent: f32,                      // 进度百分比
    pub md_file_path: String,              // 当前处理的MD文件
    pub chunk_index: usize,                // 当前分片索引
    pub related_chapter_titles: String,    // 相关章节标题
}
```

### 13. ProcessReport - 处理报告

**用途**：处理完成后的总结报告。

```rust
#[derive(Debug, Clone)]
pub struct ProcessReport {
    pub db_path: PathBuf,                  // 数据库文件路径
    pub book_title: String,                // 书籍标题
    pub book_author: String,               // 书籍作者
    pub total_chunks: usize,               // 总分片数
    pub vector_dimension: usize,           // 向量维度
}
```

### 14. ErrorStats - 错误统计

**用途**：收集和统计处理过程中的错误信息。

```rust
#[derive(Debug, Clone)]
pub struct ErrorStats {
    pub failed_files: usize,               // 失败的文件数
    pub failed_chunks: usize,              // 失败的分片数
    pub failed_db_operations: usize,       // 失败的数据库操作数
    pub file_errors: Vec<String>,          // 详细的文件错误信息
}

impl ErrorStats {
    pub fn new() -> Self;
    pub fn add_file_error(&mut self, file_path: &str, error: &str);
    pub fn add_chunk_error(&mut self);
    pub fn add_db_error(&mut self);
}
```

## DTO 数据传输对象

### 15. SearchItemDto - 搜索结果DTO

**用途**：前端API返回的搜索结果格式。

```rust
#[derive(Serialize)]
pub struct SearchItemDto {
    pub book_title: String,
    pub book_author: String,
    pub content: String,                   // 分片内容
    pub similarity: f32,                   // 相似度分数

    // 文件级别信息
    pub md_file_path: String,
    pub file_order_in_book: u32,

    // 章节关联信息
    pub related_chapter_titles: String,

    // 分片位置信息
    pub chunk_id: i64,
    pub chunk_order_in_file: usize,
    pub total_chunks_in_file: usize,
    pub global_chunk_index: usize,
}
```

### 16. DocumentChunkDto - 文档分片DTO

**用途**：前端API返回的文档分片格式（不包含向量数据）。

```rust
#[derive(Serialize)]
pub struct DocumentChunkDto {
    pub id: Option<i64>,
    pub book_title: String,
    pub book_author: String,
    pub md_file_path: String,
    pub file_order_in_book: u32,
    pub related_chapter_titles: String,
    pub chunk_text: String,
    pub chunk_order_in_file: usize,
    pub total_chunks_in_file: usize,
    pub global_chunk_index: usize,
    // 注意：不包含 embedding 字段，减少数据传输量
}

impl From<DocumentChunk> for DocumentChunkDto {
    fn from(chunk: DocumentChunk) -> Self;
}
```

### 17. ProcessReportDto - 处理报告DTO

```rust
#[derive(Serialize)]
pub struct ProcessReportDto {
    pub db_path: String,                   // 数据库路径（字符串格式）
    pub book_title: String,
    pub book_author: String,
    pub total_chunks: usize,
    pub vector_dimension: usize,
}

impl From<ProcessReport> for ProcessReportDto {
    fn from(r: ProcessReport) -> Self;
}
```

### 18. 其他API响应DTO

```rust
#[derive(Serialize)]
pub struct ParsedBook {
    pub title: String,
    pub author: String,
    pub chapters: usize,
}

#[derive(Serialize)]
pub struct IndexResult {
    pub success: bool,
    pub message: String,
    pub report: Option<ProcessReportDto>,
}

#[derive(Serialize)]
pub struct MdbookResult {
    pub success: bool,
    pub message: String,
    pub output_dir: Option<String>,
}
```