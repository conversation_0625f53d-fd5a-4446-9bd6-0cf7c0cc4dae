# 文本处理模块

## 概述

文本处理模块负责对提取的文本内容进行清理、分片、分词和向量化处理。该模块确保文本内容能够被正确处理并转换为可搜索的向量表示。

## 模块结构

```
text/
├── chunker.rs         # 文本分片处理
├── sanitizer.rs       # 内容清理和标准化
├── tokenizer.rs       # 分词和标记化
├── vectorizer.rs      # 向量化处理
└── mod.rs             # 模块导出
```

## 核心组件

### 1. Text Chunker (`text/chunker.rs`)

**职责**：智能文本分片，保持语义完整性

#### 主要功能

1. **Markdown 感知分片**
   - 基于 Markdown 结构（标题、段落、列表）
   - 识别语义边界
   - 保持代码块完整性

2. **尺寸控制**
   - 控制分片的最小和最大长度
   - 动态调整分片大小
   - 避免过小或过大的分片

3. **重叠处理**
   - 智能重叠策略
   - 避免语义断裂
   - 保持上下文连续性

#### 核心数据结构

```rust
pub struct MarkdownChunker {
    pub min_chunk_size: usize,     // 最小分片大小
    pub max_chunk_size: usize,     // 最大分片大小
    pub overlap_size: usize,       // 重叠大小
}

pub struct Chunk {
    pub content: String,           // 分片内容
    pub start_offset: usize,       // 起始偏移量
    pub end_offset: usize,         // 结束偏移量
    pub metadata: ChunkMetadata,   // 元数据
}

pub struct ChunkMetadata {
    pub has_heading: bool,         // 是否包含标题
    pub heading_level: Option<u8>, // 标题级别
    pub word_count: usize,         // 词数统计
    pub char_count: usize,         // 字符统计
}
```

#### 分片算法

```rust
impl MarkdownChunker {
    pub fn new(min_size: usize, max_size: usize, overlap: usize) -> Self;
    
    pub fn chunk_markdown(&self, content: &str) -> Vec<Chunk>;
    
    pub fn find_semantic_boundaries(&self, text: &str) -> Vec<usize>;
    
    fn split_by_headings(&self, content: &str) -> Vec<String>;
    
    fn split_by_paragraphs(&self, content: &str) -> Vec<String>;
    
    fn merge_small_chunks(&self, chunks: Vec<String>) -> Vec<String>;
}
```

#### 使用示例

```rust
use crate::text::MarkdownChunker;

let chunker = MarkdownChunker::new(50, 400, 20);
let chunks = chunker.chunk_markdown(&markdown_content);

for (i, chunk) in chunks.iter().enumerate() {
    println!("分片 {}: {} 字符", i + 1, chunk.content.len());
    println!("内容: {}", &chunk.content[..100.min(chunk.content.len())]);
    println!("---");
}
```

### 2. Text Sanitizer (`text/sanitizer.rs`)

**职责**：清理和标准化文本内容

#### 主要功能

1. **HTML 标签清理**
   - 移除 HTML/XML 标签
   - 保留重要的结构信息
   - 处理特殊字符

2. **文本标准化**
   - 统一换行符
   - 清理多余空白
   - 标准化标点符号

3. **编码处理**
   - UTF-8 编码确保
   - HTML 实体解码
   - URL 解码

#### 核心函数

```rust
pub struct TextSanitizer {
    preserve_structure: bool,      // 保持结构
    remove_extra_whitespace: bool, // 移除多余空白
}

impl TextSanitizer {
    pub fn new() -> Self;
    
    pub fn sanitize(&self, content: &str) -> String;
    
    pub fn remove_html_tags(&self, content: &str) -> String;
    
    pub fn normalize_whitespace(&self, content: &str) -> String;
    
    pub fn decode_html_entities(&self, content: &str) -> String;
}
```

#### 使用示例

```rust
use crate::text::TextSanitizer;

let sanitizer = TextSanitizer::new();
let clean_text = sanitizer.sanitize(&html_content);

println!("原始长度: {}", html_content.len());
println!("清理后长度: {}", clean_text.len());
```

### 3. Tokenizer (`text/tokenizer.rs`)

**职责**：文本分词和标记化处理

#### 主要功能

1. **多语言分词**
   - 中文分词支持
   - 英文词汇分割
   - 标点符号处理

2. **词汇统计**
   - 词频统计
   - 关键词提取
   - 文本长度计算

3. **特殊处理**
   - 数字和日期识别
   - URL 和邮箱处理
   - 专有名词保护

#### 核心数据结构

```rust
pub struct Tokenizer {
    language: Language,            // 语言类型
    preserve_case: bool,           // 保持大小写
}

pub enum Language {
    Chinese,
    English,
    Mixed,
}

pub struct TokenResult {
    pub tokens: Vec<String>,       // 分词结果
    pub positions: Vec<(usize, usize)>, // 位置信息
    pub word_count: usize,         // 词数
    pub char_count: usize,         // 字符数
}
```

#### 使用示例

```rust
use crate::text::{Tokenizer, Language};

let tokenizer = Tokenizer::new(Language::Mixed);
let result = tokenizer.tokenize(&text_content);

println!("词数: {}", result.word_count);
println!("字符数: {}", result.char_count);
println!("前10个词: {:?}", &result.tokens[..10.min(result.tokens.len())]);
```

### 4. Vectorizer (`text/vectorizer.rs`)

**职责**：文本向量化处理

#### 主要功能

1. **向量生成**
   - 支持本地和远程向量化服务
   - 多种模型支持
   - 批量向量化

2. **API 集成**
   - OpenAI API 兼容
   - 本地模型服务
   - 自定义端点支持

3. **缓存机制**
   - 向量缓存
   - 避免重复计算
   - 性能优化

#### 核心数据结构

```rust
pub struct TextVectorizer {
    config: VectorizerConfig,      // 配置信息
    client: reqwest::Client,       // HTTP 客户端
    cache: Option<VectorCache>,    // 缓存
}

#[derive(Debug, Clone)]
pub struct VectorizerConfig {
    pub base_url: String,          // API 基础 URL
    pub model_name: String,        // 模型名称
    pub api_key: Option<String>,   // API 密钥
    pub timeout: Duration,         // 超时时间
    pub max_retries: u32,          // 最大重试次数
}

pub struct VectorCache {
    cache: HashMap<String, Vec<f32>>, // 内存缓存
    max_size: usize,               // 最大缓存大小
}
```

#### API 接口

```rust
impl TextVectorizer {
    pub async fn new(config: VectorizerConfig) -> Result<Self, String>;
    
    pub async fn vectorize_text(&self, text: &str) -> Result<Vec<f32>, String>;
    
    pub async fn vectorize_batch(&self, texts: &[String]) -> Result<Vec<Vec<f32>>, String>;
    
    pub fn get_embedding_dimension(&self) -> usize;
    
    pub async fn test_connection(&self) -> Result<(), String>;
}
```

#### 使用示例

```rust
use crate::text::{TextVectorizer, VectorizerConfig};

let config = VectorizerConfig {
    base_url: "http://localhost:3544".to_string(),
    model_name: "local-embed".to_string(),
    api_key: None,
    timeout: Duration::from_secs(30),
    max_retries: 3,
};

let vectorizer = TextVectorizer::new(config).await?;

// 单个文本向量化
let embedding = vectorizer.vectorize_text("这是一段测试文本").await?;
println!("向量维度: {}", embedding.len());

// 批量向量化
let texts = vec!["文本1".to_string(), "文本2".to_string()];
let embeddings = vectorizer.vectorize_batch(&texts).await?;
println!("批量向量化完成: {} 个向量", embeddings.len());
```

## 处理流程

### 1. 文本预处理流程

```rust
// 1. 内容清理
let sanitizer = TextSanitizer::new();
let clean_content = sanitizer.sanitize(&raw_content);

// 2. 文本分片
let chunker = MarkdownChunker::new(50, 400, 20);
let chunks = chunker.chunk_markdown(&clean_content);

// 3. 分词处理
let tokenizer = Tokenizer::new(Language::Mixed);
for chunk in &chunks {
    let tokens = tokenizer.tokenize(&chunk.content);
    println!("分片词数: {}", tokens.word_count);
}
```

### 2. 向量化流程

```rust
// 1. 初始化向量化器
let vectorizer = TextVectorizer::new(config).await?;

// 2. 批量向量化
let mut embeddings = Vec::new();
for chunk_batch in chunks.chunks(batch_size) {
    let texts: Vec<String> = chunk_batch.iter()
        .map(|c| c.content.clone())
        .collect();
    
    let batch_embeddings = vectorizer.vectorize_batch(&texts).await?;
    embeddings.extend(batch_embeddings);
}

// 3. 存储向量
for (chunk, embedding) in chunks.iter().zip(embeddings.iter()) {
    let document_chunk = DocumentChunk {
        chunk_text: chunk.content.clone(),
        embedding: embedding.clone(),
        // ... 其他字段
    };
    
    db.insert_chunk(&document_chunk)?;
}
```

## 性能优化

### 1. 分片优化

```rust
// 并行分片处理
use rayon::prelude::*;

let chunks: Vec<_> = content_sections
    .par_iter()
    .map(|section| chunker.chunk_markdown(section))
    .flatten()
    .collect();
```

### 2. 向量化优化

```rust
// 批量向量化
pub async fn vectorize_batch_optimized(
    &self, 
    texts: &[String]
) -> Result<Vec<Vec<f32>>, String> {
    const BATCH_SIZE: usize = 10;
    
    let mut results = Vec::new();
    for batch in texts.chunks(BATCH_SIZE) {
        let batch_result = self.vectorize_batch_internal(batch).await?;
        results.extend(batch_result);
        
        // 避免 API 限流
        tokio::time::sleep(Duration::from_millis(100)).await;
    }
    
    Ok(results)
}
```

### 3. 内存管理

```rust
// 流式处理大文件
pub fn process_large_file<P: AsRef<Path>>(
    &self, 
    file_path: P
) -> Result<impl Iterator<Item = Chunk>, String> {
    let file = File::open(file_path)?;
    let reader = BufReader::new(file);
    
    // 返回迭代器，避免一次性加载整个文件
    Ok(reader.lines()
        .map(|line| self.process_line(line.unwrap()))
        .flatten())
}
```

## 错误处理

### 常见错误类型

1. **网络错误**
   - API 连接失败
   - 超时错误
   - 限流错误

2. **格式错误**
   - 文本编码问题
   - 分片大小异常
   - 向量维度不匹配

3. **资源错误**
   - 内存不足
   - 磁盘空间不足
   - 文件权限问题

### 错误处理策略

```rust
// 重试机制
pub async fn vectorize_with_retry(
    &self, 
    text: &str
) -> Result<Vec<f32>, String> {
    let mut attempts = 0;
    
    loop {
        match self.vectorize_text(text).await {
            Ok(embedding) => return Ok(embedding),
            Err(e) if attempts < self.config.max_retries => {
                attempts += 1;
                let delay = Duration::from_secs(2_u64.pow(attempts));
                tokio::time::sleep(delay).await;
                continue;
            }
            Err(e) => return Err(e),
        }
    }
}

// 降级处理
pub fn fallback_chunking(&self, content: &str) -> Vec<String> {
    if content.len() <= self.max_chunk_size {
        return vec![content.to_string()];
    }
    
    // 简单按字符数分片
    content.chars()
        .collect::<Vec<_>>()
        .chunks(self.max_chunk_size)
        .map(|chunk| chunk.iter().collect())
        .collect()
}
```

## 配置选项

### 文本处理配置

```rust
#[derive(Debug, Clone)]
pub struct TextProcessingConfig {
    // 分片配置
    pub min_chunk_size: usize,
    pub max_chunk_size: usize,
    pub overlap_size: usize,
    
    // 清理配置
    pub preserve_structure: bool,
    pub remove_html: bool,
    pub normalize_whitespace: bool,
    
    // 分词配置
    pub language: Language,
    pub preserve_case: bool,
    
    // 向量化配置
    pub vectorizer: VectorizerConfig,
    pub batch_size: usize,
}
```

### 默认配置

```rust
impl Default for TextProcessingConfig {
    fn default() -> Self {
        Self {
            min_chunk_size: 50,
            max_chunk_size: 400,
            overlap_size: 20,
            preserve_structure: true,
            remove_html: true,
            normalize_whitespace: true,
            language: Language::Mixed,
            preserve_case: false,
            vectorizer: VectorizerConfig::default(),
            batch_size: 10,
        }
    }
}
```
