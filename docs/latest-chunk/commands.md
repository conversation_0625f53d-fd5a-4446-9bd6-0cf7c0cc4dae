# 命令接口模块

## 概述

命令接口模块提供 Tauri 前端调用的 API 接口，是前端与后端 Rust 代码交互的桥梁。该模块定义了所有可供前端调用的命令，包括 EPUB 处理、搜索、TOC 解析等功能。

## 模块结构

```
commands.rs                    # 所有 Tauri 命令定义
├── index_epub()              # EPUB 向量化处理
├── search_db()               # 数据库搜索
├── parse_toc()               # TOC 解析
├── get_chapter_content()     # 章节内容获取
├── get_book_stats()          # 书籍统计信息
└── EpubState                 # 全局状态管理
```

## 核心命令

### 1. EPUB 向量化处理

**命令名称**：`plugin:epub|index_epub`

```rust
#[tauri::command]
pub async fn index_epub<R: Runtime>(
    app: AppHandle<R>,
    _state: State<'_, EpubState>,
    book_id: String,
    dimension: usize,
    base_url: String,
    model: String,
    api_key: Option<String>,
    batch_size: Option<usize>,
) -> Result<ProcessReport, String> {
    // 参数验证
    if book_id.trim().is_empty() {
        return Err("book_id is empty".into());
    }
    
    if dimension == 0 {
        return Err("dimension must be greater than 0".into());
    }
    
    // 构建配置
    let opts = ProcessOptions {
        dimension,
        vectorizer: VectorizerConfig {
            base_url,
            model_name: model,
            api_key,
        },
        batch_size,
    };
    
    // 获取书籍目录
    let app_data_dir = app.path().app_data_dir().map_err(|e| e.to_string())?;
    let book_dir = app_data_dir.join("books").join(&book_id);
    
    // 执行处理流水线
    let report = process_epub_to_db(&book_dir, opts, None::<fn(ProgressUpdate)>)
        .await
        .map_err(|e| e.to_string())?;
    
    Ok(report)
}
```

**前端调用示例**：
```typescript
import { invoke } from "@tauri-apps/api/core";

const result = await invoke("plugin:epub|index_epub", {
  bookId: "book-123",
  dimension: 1024,
  baseUrl: "http://localhost:3544",
  model: "local-embed",
  apiKey: null,
  batchSize: 10,
});

console.log("处理完成:", result);
```

### 2. 数据库搜索

**命令名称**：`plugin:epub|search_db`

```rust
#[tauri::command]
pub async fn search_db<R: Runtime>(
    app: AppHandle<R>,
    _state: State<'_, EpubState>,
    book_id: String,
    query: String,
    limit: usize,
    dimension: usize,
    base_url: String,
    model: String,
    api_key: Option<String>,
) -> Result<Vec<SearchResult>, String> {
    // 参数验证
    if book_id.trim().is_empty() {
        return Err("book_id is empty".into());
    }
    
    if query.trim().is_empty() {
        return Err("query is empty".into());
    }
    
    if limit == 0 {
        return Err("limit must be greater than 0".into());
    }
    
    // 构建向量化配置
    let vectorizer_config = VectorizerConfig {
        base_url,
        model_name: model,
        api_key,
    };
    
    // 获取书籍目录
    let app_data_dir = app.path().app_data_dir().map_err(|e| e.to_string())?;
    let book_dir = app_data_dir.join("books").join(&book_id);
    
    // 执行搜索
    let results = crate::pipeline::search_db(
        &book_dir,
        &query,
        limit,
        dimension,
        vectorizer_config,
    )
    .await
    .map_err(|e| e.to_string())?;
    
    Ok(results)
}
```

**前端调用示例**：
```typescript
const searchResults = await invoke("plugin:epub|search_db", {
  bookId: "book-123",
  query: "人工智能的发展",
  limit: 5,
  dimension: 1024,
  baseUrl: "http://localhost:3544",
  model: "local-embed",
  apiKey: null,
});

searchResults.forEach(result => {
  console.log(`相似度: ${result.similarity}`);
  console.log(`章节: ${result.related_chapter_titles}`);
  console.log(`内容: ${result.chunk_text.substring(0, 100)}...`);
});
```

### 3. TOC 解析

**命令名称**：`plugin:epub|parse_toc`

```rust
#[tauri::command]
pub async fn parse_toc<R: Runtime>(
    app: AppHandle<R>,
    _state: State<'_, EpubState>,
    book_id: String,
) -> Result<Vec<FlatTocNode>, String> {
    if book_id.trim().is_empty() {
        return Err("book_id is empty".into());
    }

    let app_data_dir = app.path().app_data_dir().map_err(|e| e.to_string())?;
    let book_dir = app_data_dir.join("books").join(&book_id);
    let mdbook_dir = book_dir.join("mdbook");

    // 在 mdbook 目录下递归搜索 toc.ncx
    let toc_path = find_toc_ncx_in_mdbook(&mdbook_dir)
        .ok_or_else(|| "TOC file (toc.ncx) not found in MDBook directory".to_string())?;

    let toc_nodes = parse_toc_file(&toc_path)?;
    let flat_toc = flatten_toc(&toc_nodes);
    Ok(flat_toc)
}
```

**前端调用示例**：
```typescript
const tocNodes = await invoke("plugin:epub|parse_toc", {
  bookId: "book-123",
});

tocNodes.forEach(node => {
  const indent = "  ".repeat(node.depth);
  console.log(`${indent}- ${node.title}`);
  console.log(`${indent}  文件: ${node.md_src}`);
  console.log(`${indent}  层级: ${node.hierarchy_path.join(" > ")}`);
});
```

### 4. 章节内容获取

**命令名称**：`plugin:epub|get_chapter_content`

```rust
#[tauri::command]
pub async fn get_chapter_content<R: Runtime>(
    app: AppHandle<R>,
    _state: State<'_, EpubState>,
    book_id: String,
    chapter_title: String,
    limit: Option<usize>,
) -> Result<Vec<SearchResult>, String> {
    if book_id.trim().is_empty() {
        return Err("book_id is empty".into());
    }
    
    if chapter_title.trim().is_empty() {
        return Err("chapter_title is empty".into());
    }
    
    let app_data_dir = app.path().app_data_dir().map_err(|e| e.to_string())?;
    let book_dir = app_data_dir.join("books").join(&book_id);
    let db_path = book_dir.join("vectors.sqlite");
    
    // 打开数据库
    let db = VectorDatabase::new(db_path, 1024).map_err(|e| e.to_string())?;
    
    // 按章节搜索
    let results = db.search_by_chapter(&chapter_title, limit.unwrap_or(50))
        .map_err(|e| e.to_string())?;
    
    Ok(results)
}
```

**前端调用示例**：
```typescript
const chapterContent = await invoke("plugin:epub|get_chapter_content", {
  bookId: "book-123",
  chapterTitle: "第一章",
  limit: 20,
});

console.log(`找到 ${chapterContent.length} 个分片`);
chapterContent.forEach((chunk, index) => {
  console.log(`分片 ${index + 1}: ${chunk.chunk_text.substring(0, 100)}...`);
});
```

### 5. 书籍统计信息

**命令名称**：`plugin:epub|get_book_stats`

```rust
#[tauri::command]
pub async fn get_book_stats<R: Runtime>(
    app: AppHandle<R>,
    _state: State<'_, EpubState>,
    book_id: String,
) -> Result<BookStats, String> {
    if book_id.trim().is_empty() {
        return Err("book_id is empty".into());
    }
    
    let app_data_dir = app.path().app_data_dir().map_err(|e| e.to_string())?;
    let book_dir = app_data_dir.join("books").join(&book_id);
    let db_path = book_dir.join("vectors.sqlite");
    
    // 检查数据库是否存在
    if !db_path.exists() {
        return Err("Database not found. Please index the book first.".into());
    }
    
    let db = VectorDatabase::new(db_path, 1024).map_err(|e| e.to_string())?;
    let stats = db.get_database_stats().map_err(|e| e.to_string())?;
    
    Ok(BookStats {
        total_chunks: stats.total_chunks,
        total_files: stats.total_files,
        avg_chunk_length: stats.avg_chunk_length,
        database_size: get_file_size(&db_path)?,
        last_updated: get_last_modified(&db_path)?,
    })
}
```

**前端调用示例**：
```typescript
const stats = await invoke("plugin:epub|get_book_stats", {
  bookId: "book-123",
});

console.log(`总分片数: ${stats.total_chunks}`);
console.log(`总文件数: ${stats.total_files}`);
console.log(`平均分片长度: ${stats.avg_chunk_length}`);
console.log(`数据库大小: ${stats.database_size} bytes`);
```

## 状态管理

### EpubState

```rust
#[derive(Default)]
pub struct EpubState {
    // 可以添加全局状态，如缓存、连接池等
    pub processing_status: Arc<Mutex<HashMap<String, ProcessingStatus>>>,
}

#[derive(Debug, Clone)]
pub enum ProcessingStatus {
    Idle,
    Processing { progress: f32 },
    Completed,
    Failed { error: String },
}

impl EpubState {
    pub fn new() -> Self {
        Self {
            processing_status: Arc::new(Mutex::new(HashMap::new())),
        }
    }
    
    pub fn set_status(&self, book_id: &str, status: ProcessingStatus) {
        let mut statuses = self.processing_status.lock().unwrap();
        statuses.insert(book_id.to_string(), status);
    }
    
    pub fn get_status(&self, book_id: &str) -> Option<ProcessingStatus> {
        let statuses = self.processing_status.lock().unwrap();
        statuses.get(book_id).cloned()
    }
}
```

### 状态查询命令

```rust
#[tauri::command]
pub async fn get_processing_status<R: Runtime>(
    _app: AppHandle<R>,
    state: State<'_, EpubState>,
    book_id: String,
) -> Result<Option<ProcessingStatus>, String> {
    Ok(state.get_status(&book_id))
}
```

## 错误处理

### 统一错误处理

```rust
// 自定义错误类型
#[derive(Debug, thiserror::Error)]
pub enum CommandError {
    #[error("Invalid parameter: {0}")]
    InvalidParameter(String),
    
    #[error("Book not found: {0}")]
    BookNotFound(String),
    
    #[error("Database error: {0}")]
    Database(String),
    
    #[error("Processing error: {0}")]
    Processing(String),
    
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
}

// 错误转换
impl From<CommandError> for String {
    fn from(error: CommandError) -> Self {
        error.to_string()
    }
}
```

### 参数验证

```rust
fn validate_book_id(book_id: &str) -> Result<(), CommandError> {
    if book_id.trim().is_empty() {
        return Err(CommandError::InvalidParameter("book_id cannot be empty".into()));
    }
    
    if book_id.contains("..") || book_id.contains("/") || book_id.contains("\\") {
        return Err(CommandError::InvalidParameter("book_id contains invalid characters".into()));
    }
    
    Ok(())
}

fn validate_dimension(dimension: usize) -> Result<(), CommandError> {
    if dimension == 0 {
        return Err(CommandError::InvalidParameter("dimension must be greater than 0".into()));
    }
    
    if dimension > 4096 {
        return Err(CommandError::InvalidParameter("dimension too large (max: 4096)".into()));
    }
    
    Ok(())
}
```

## 性能优化

### 异步处理

```rust
// 带进度回调的异步处理
#[tauri::command]
pub async fn index_epub_with_progress<R: Runtime>(
    app: AppHandle<R>,
    state: State<'_, EpubState>,
    book_id: String,
    // ... 其他参数
) -> Result<ProcessReport, String> {
    let book_id_clone = book_id.clone();
    let state_clone = state.inner().clone();
    
    // 设置初始状态
    state.set_status(&book_id, ProcessingStatus::Processing { progress: 0.0 });
    
    // 异步处理
    let report = process_epub_to_db(
        &book_dir,
        opts,
        Some(move |progress: ProgressUpdate| {
            state_clone.set_status(
                &book_id_clone,
                ProcessingStatus::Processing { progress: progress.percent }
            );
        })
    ).await.map_err(|e| {
        state.set_status(&book_id, ProcessingStatus::Failed { 
            error: e.to_string() 
        });
        e.to_string()
    })?;
    
    // 设置完成状态
    state.set_status(&book_id, ProcessingStatus::Completed);
    
    Ok(report)
}
```

### 缓存机制

```rust
// 结果缓存
use std::collections::HashMap;
use std::sync::{Arc, Mutex};

pub struct ResultCache {
    cache: Arc<Mutex<HashMap<String, (std::time::Instant, Vec<SearchResult>)>>>,
    ttl: std::time::Duration,
}

impl ResultCache {
    pub fn new(ttl_seconds: u64) -> Self {
        Self {
            cache: Arc::new(Mutex::new(HashMap::new())),
            ttl: std::time::Duration::from_secs(ttl_seconds),
        }
    }
    
    pub fn get(&self, key: &str) -> Option<Vec<SearchResult>> {
        let mut cache = self.cache.lock().unwrap();
        
        if let Some((timestamp, results)) = cache.get(key) {
            if timestamp.elapsed() < self.ttl {
                return Some(results.clone());
            } else {
                cache.remove(key);
            }
        }
        
        None
    }
    
    pub fn set(&self, key: String, results: Vec<SearchResult>) {
        let mut cache = self.cache.lock().unwrap();
        cache.insert(key, (std::time::Instant::now(), results));
    }
}
```

## 插件注册

### Tauri 插件初始化

```rust
use tauri::{plugin::Builder, Runtime};

pub fn init<R: Runtime>() -> tauri::plugin::TauriPlugin<R> {
    Builder::new("epub")
        .invoke_handler(tauri::generate_handler![
            index_epub,
            search_db,
            parse_toc,
            get_chapter_content,
            get_book_stats,
            get_processing_status,
        ])
        .setup(|app, _api| {
            // 初始化全局状态
            app.manage(EpubState::new());
            Ok(())
        })
        .build()
}
```

### 前端类型定义

```typescript
// types/epub.ts
export interface ProcessReport {
  db_path: string;
  book_title: string;
  book_author: string;
  total_chunks: number;
  vector_dimension: number;
}

export interface SearchResult {
  id: number;
  book_title: string;
  book_author: string;
  md_file_path: string;
  related_chapter_titles: string;
  chunk_text: string;
  chunk_order_in_file: number;
  total_chunks_in_file: number;
  global_chunk_index: number;
  similarity: number;
}

export interface FlatTocNode {
  id: string;
  play_order: number;
  title: string;
  md_src: string;
  depth: number;
  anchor?: string;
  hierarchy_path: string[];
}

export interface BookStats {
  total_chunks: number;
  total_files: number;
  avg_chunk_length: number;
  database_size: number;
  last_updated: string;
}
```

## 使用示例

### 完整的前端集成

```typescript
import { invoke } from "@tauri-apps/api/core";
import type { ProcessReport, SearchResult, FlatTocNode } from "./types/epub";

class EpubService {
  async indexBook(bookId: string, config: {
    dimension: number;
    baseUrl: string;
    model: string;
    apiKey?: string;
    batchSize?: number;
  }): Promise<ProcessReport> {
    return await invoke("plugin:epub|index_epub", {
      bookId,
      ...config,
    });
  }
  
  async searchBook(bookId: string, query: string, config: {
    limit: number;
    dimension: number;
    baseUrl: string;
    model: string;
    apiKey?: string;
  }): Promise<SearchResult[]> {
    return await invoke("plugin:epub|search_db", {
      bookId,
      query,
      ...config,
    });
  }
  
  async getToc(bookId: string): Promise<FlatTocNode[]> {
    return await invoke("plugin:epub|parse_toc", { bookId });
  }
  
  async getChapterContent(bookId: string, chapterTitle: string, limit?: number): Promise<SearchResult[]> {
    return await invoke("plugin:epub|get_chapter_content", {
      bookId,
      chapterTitle,
      limit,
    });
  }
}

// 使用示例
const epubService = new EpubService();

// 索引书籍
const report = await epubService.indexBook("book-123", {
  dimension: 1024,
  baseUrl: "http://localhost:3544",
  model: "local-embed",
});

console.log(`索引完成: ${report.total_chunks} 个分片`);

// 搜索
const results = await epubService.searchBook("book-123", "人工智能", {
  limit: 5,
  dimension: 1024,
  baseUrl: "http://localhost:3544",
  model: "local-embed",
});

console.log(`找到 ${results.length} 个相关结果`);
```
