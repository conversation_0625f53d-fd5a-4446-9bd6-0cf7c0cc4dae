# EPUB 处理模块

## 概述

EPUB 处理模块负责解析 EPUB 文件、提取内容、处理目录结构，并将其转换为可处理的格式。该模块是整个向量化流水线的起点，确保 EPUB 文件能够被正确解析和处理。

## 模块结构

```
epub/
├── reader.rs          # EPUB 文件读取和解析
├── toc_parser.rs      # 目录结构解析和扁平化
└── mod.rs             # 模块导出
```

## 核心组件

### 1. EPUB Reader (`epub/reader.rs`)

**职责**：解析 EPUB 文件结构，提取章节内容和元数据

#### 主要功能

1. **EPUB 文件解析**
   - 解压 EPUB 文件（ZIP 格式）
   - 解析 `META-INF/container.xml` 获取根文件路径
   - 解析 `content.opf` 获取元数据和清单

2. **内容提取**
   - 提取所有章节的 HTML/XHTML 内容
   - 清理 HTML 标签，转换为纯文本
   - 保持章节顺序和结构

3. **Markdown 分片**
   - 支持对 Markdown 文件进行智能分片
   - 基于语义边界进行分片
   - 控制分片大小和重叠

#### 核心数据结构

```rust
pub struct EpubReader {
    // EPUB 解析器配置
}

impl EpubReader {
    pub fn new() -> Result<Self, String>;
    pub fn read_epub(&self, path: &Path) -> Result<EpubContent, String>;
    pub fn chunk_md_file(&self, content: &str, min_size: usize, max_size: usize) -> Vec<String>;
}
```

#### 使用示例

```rust
use crate::epub::EpubReader;

let reader = EpubReader::new()?;
let epub_content = reader.read_epub(&epub_path)?;

println!("书名: {}", epub_content.title);
println!("作者: {}", epub_content.author);
println!("章节数: {}", epub_content.chapters.len());

// 对 Markdown 内容进行分片
let chunks = reader.chunk_md_file(&md_content, 50, 400);
```

### 2. TOC Parser (`epub/toc_parser.rs`)

**职责**：解析和处理 EPUB 目录结构，支持层级关系和扁平化

#### 主要功能

1. **TOC 文件解析**
   - 解析 `toc.ncx` 文件（NCX 格式）
   - 处理嵌套的 `navPoint` 结构
   - 提取章节标题、链接和层级信息

2. **层级结构处理**
   - 构建完整的层级目录树
   - 记录每个节点的深度和父子关系
   - 生成完整的层级路径

3. **扁平化处理**
   - 将层级结构转换为线性列表
   - 保留层级信息和路径
   - 处理文件路径和锚点

4. **智能文件查找**
   - 递归搜索 `toc.ncx` 文件
   - 支持多种目录结构
   - 容错处理

#### 核心数据结构

```rust
/// 层级 TOC 节点
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TocNode {
    pub id: String,                    // 节点 ID
    pub play_order: u32,               // 播放顺序
    pub title: String,                 // 章节标题
    pub src: String,                   // 源文件路径
    pub children: Vec<TocNode>,        // 子节点
}

/// 扁平化 TOC 节点
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FlatTocNode {
    pub id: String,                    // 节点 ID
    pub play_order: u32,               // 播放顺序
    pub title: String,                 // 章节标题
    pub md_src: String,                // 转换后的 MD 文件路径
    pub depth: u32,                    // 节点深度（从0开始）
    pub anchor: Option<String>,        // 锚点（如果有）
    pub hierarchy_path: Vec<String>,   // 完整层级路径
}
```

#### 层级路径处理

**新特性**：支持完整的层级路径生成

对于嵌套的 TOC 结构：
```xml
<navPoint id="navPoint-3" playOrder="3">
  <navLabel><text>第一部分 认知革命</text></navLabel>
  <content src="Text/part1.xhtml"/>
  <navPoint id="navPoint-4" playOrder="4">
    <navLabel><text>第一章 人类：一种也没什么特别的动物</text></navLabel>
    <content src="Text/chapter01.xhtml"/>
    <navPoint id="navPoint-5" playOrder="5">
      <navLabel><text>家族秘史</text></navLabel>
      <content src="Text/chapter01.xhtml#sigil_toc_id_1"/>
    </navPoint>
  </navPoint>
</navPoint>
```

生成的 `hierarchy_path`：
- "第一部分 认知革命": `["第一部分 认知革命"]`
- "第一章 人类：一种也没什么特别的动物": `["第一部分 认知革命", "第一章 人类：一种也没什么特别的动物"]`
- "家族秘史": `["第一部分 认知革命", "第一章 人类：一种也没什么特别的动物", "家族秘史"]`

#### 核心函数

```rust
/// 解析 toc.ncx 文件
pub fn parse_toc_file<P: AsRef<Path>>(toc_path: P) -> Result<Vec<TocNode>, String>;

/// 解析 NCX 内容
pub fn parse_toc_content(content: &str) -> Result<Vec<TocNode>, String>;

/// 扁平化 TOC 结构
pub fn flatten_toc(toc_nodes: &[TocNode]) -> Vec<FlatTocNode>;

/// 在 MDBook 目录中查找 toc.ncx
pub fn find_toc_ncx_in_mdbook<P: AsRef<Path>>(mdbook_dir: P) -> Option<PathBuf>;
```

#### 使用示例

```rust
use crate::epub::{parse_toc_file, flatten_toc, find_toc_ncx_in_mdbook};

// 查找 TOC 文件
let toc_path = find_toc_ncx_in_mdbook(&mdbook_dir)
    .ok_or("TOC file not found")?;

// 解析 TOC 结构
let toc_nodes = parse_toc_file(&toc_path)?;

// 扁平化处理
let flat_toc = flatten_toc(&toc_nodes);

for node in &flat_toc {
    println!("标题: {}", node.title);
    println!("深度: {}", node.depth);
    println!("层级路径: {}", node.hierarchy_path.join(" > "));
    println!("文件: {}", node.md_src);
    if let Some(anchor) = &node.anchor {
        println!("锚点: {}", anchor);
    }
    println!("---");
}
```

## 处理流程

### 1. EPUB 到 MDBook 转换

```rust
// 1. 转换 EPUB 为 MDBook 格式
convert_epub_to_mdbook(&epub_path, &mdbook_dir, true)?;

// 2. 查找 TOC 文件
let toc_path = find_toc_ncx_in_mdbook(&mdbook_dir)?;

// 3. 解析 TOC 结构
let toc_nodes = parse_toc_file(&toc_path)?;
let flat_toc = flatten_toc(&toc_nodes);
```

### 2. 文件分组和处理

```rust
// 按 md_src 分组 TOC 节点
let mut md_groups: BTreeMap<String, Vec<(usize, FlatTocNode)>> = BTreeMap::new();
for (idx, node) in flat_toc.iter().cloned().enumerate() {
    md_groups.entry(node.md_src.clone()).or_default().push((idx, node));
}

// 为每个文件生成层级标题
for (md_src, nodes) in md_groups.iter() {
    let deepest_node = nodes.iter()
        .max_by_key(|(_, node)| node.depth)
        .map(|(_, node)| node);
    
    if let Some(node) = deepest_node {
        let related_chapter_titles = node.hierarchy_path.join("|");
        println!("文件 {} 的层级标题: {}", md_src, related_chapter_titles);
    }
}
```

## 错误处理

### 常见错误类型

1. **文件不存在**
   - EPUB 文件路径错误
   - TOC 文件缺失
   - MD 文件不存在

2. **格式错误**
   - EPUB 文件损坏
   - NCX 格式不正确
   - XML 解析失败

3. **编码问题**
   - 字符编码不支持
   - URL 编码处理

### 错误处理策略

```rust
// 容错的 TOC 查找
pub fn find_toc_ncx_in_mdbook<P: AsRef<Path>>(mdbook_dir: P) -> Option<PathBuf> {
    // 1. 检查常见位置
    let common_locations = [
        mdbook_dir.join("src").join("toc.ncx"),
        mdbook_dir.join("toc.ncx"),
        mdbook_dir.join("book").join("src").join("toc.ncx"),
    ];
    
    for location in &common_locations {
        if location.exists() {
            return Some(location.clone());
        }
    }
    
    // 2. 递归搜索
    find_toc_ncx_recursive(mdbook_dir)
}

// 鲁棒的 XML 处理
fn strip_doctype(input: &str) -> String {
    let re = Regex::new(r"(?is)<!DOCTYPE.*?>").unwrap();
    re.replace(input, "").to_string()
}
```

## 性能优化

### 1. 内存管理
- 流式处理大文件
- 及时释放不需要的数据
- 避免重复加载

### 2. 文件 I/O 优化
- 批量文件操作
- 缓存常用数据
- 异步 I/O 处理

### 3. 解析优化
- 预编译正则表达式
- 复用解析器实例
- 增量解析支持

## 测试

### 单元测试

```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_flatten_toc() {
        let toc_nodes = vec![
            TocNode {
                id: "chapter1".to_string(),
                play_order: 1,
                title: "Chapter 1".to_string(),
                src: "chapter1.xhtml".to_string(),
                children: vec![
                    TocNode {
                        id: "section1_1".to_string(),
                        play_order: 2,
                        title: "Section 1.1".to_string(),
                        src: "chapter1.xhtml#section1".to_string(),
                        children: vec![],
                    }
                ],
            }
        ];

        let flattened = flatten_toc(&toc_nodes);
        assert_eq!(flattened.len(), 2);
        assert_eq!(flattened[0].hierarchy_path, vec!["Chapter 1"]);
        assert_eq!(flattened[1].hierarchy_path, vec!["Chapter 1", "Section 1.1"]);
    }
}
```

## 配置选项

### EPUB 处理配置

```rust
pub struct EpubProcessingConfig {
    pub min_chunk_size: usize,      // 最小分片大小
    pub max_chunk_size: usize,      // 最大分片大小
    pub overlap_size: usize,        // 重叠大小
    pub preserve_structure: bool,   // 保持结构
}
```

### TOC 解析配置

```rust
pub struct TocParsingConfig {
    pub recursive_search: bool,     // 递归搜索
    pub max_depth: u32,            // 最大深度
    pub include_anchors: bool,     // 包含锚点
}
```
