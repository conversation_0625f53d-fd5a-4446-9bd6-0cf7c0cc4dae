# 数据库模块

## 概述

数据库模块负责向量数据的存储、检索和管理。基于 SQLite + sqlite-vec 扩展，提供高性能的向量存储和相似性搜索功能。该模块支持事务管理、批量操作和多种搜索策略。

## 模块结构

```
database/
├── connection.rs      # 数据库连接管理
├── operations.rs      # 数据操作（增删改查）
├── search.rs          # 搜索和检索功能
├── schema.rs          # 数据库模式定义
└── mod.rs             # 模块导出
```

## 核心组件

### 1. Vector Database (`database/mod.rs`)

**职责**：向量数据库的主要接口和管理

#### 核心数据结构

```rust
pub struct VectorDatabase {
    conn: Connection,              // SQLite 连接
    dimension: usize,              // 向量维度
    vec_enabled: bool,             // sqlite-vec 是否可用
}

impl VectorDatabase {
    pub fn new<P: AsRef<Path>>(db_path: P, dimension: usize) -> Result<Self>;
    pub fn initialize_vec_table(&mut self) -> Result<()>;
    pub fn insert_chunk(&self, chunk: &DocumentChunk) -> Result<i64>;
    pub fn insert_chunks_batch(&self, chunks: &[DocumentChunk]) -> Result<Vec<i64>>;
    pub fn search_similar(&self, query_embedding: &[f32], limit: usize) -> Result<Vec<SearchResult>>;
}
```

#### 数据库初始化

```rust
impl VectorDatabase {
    pub fn new<P: AsRef<Path>>(db_path: P, dimension: usize) -> Result<Self> {
        let conn = Connection::open(db_path)?;
        
        // 创建基础表结构
        conn.execute(&format!(r#"
            CREATE TABLE IF NOT EXISTS document_chunks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                book_title TEXT NOT NULL,
                book_author TEXT NOT NULL,
                md_file_path TEXT NOT NULL,
                file_order_in_book INTEGER NOT NULL,
                related_chapter_titles TEXT NOT NULL,
                chunk_text TEXT NOT NULL,
                chunk_order_in_file INTEGER NOT NULL,
                total_chunks_in_file INTEGER NOT NULL,
                global_chunk_index INTEGER NOT NULL,
                embedding_blob BLOB,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        "#), [])?;
        
        // 创建索引
        conn.execute(r#"
            CREATE INDEX IF NOT EXISTS idx_book_title ON document_chunks(book_title);
            CREATE INDEX IF NOT EXISTS idx_md_file_path ON document_chunks(md_file_path);
            CREATE INDEX IF NOT EXISTS idx_global_chunk_index ON document_chunks(global_chunk_index);
        "#, [])?;
        
        Ok(Self {
            conn,
            dimension,
            vec_enabled: false,
        })
    }
}
```

#### sqlite-vec 集成

```rust
pub fn initialize_vec_table(&mut self) -> Result<()> {
    // 尝试加载 sqlite-vec 扩展
    match self.conn.execute(&format!(r#"
        CREATE VIRTUAL TABLE IF NOT EXISTS vec_chunks USING vec0(
            id INTEGER PRIMARY KEY,
            embedding float[{}]
        )
    "#, self.dimension), []) {
        Ok(_) => {
            self.vec_enabled = true;
            log::info!("sqlite-vec 扩展已启用，向量维度: {}", self.dimension);
            Ok(())
        }
        Err(e) => {
            log::warn!("sqlite-vec 不可用，使用标准存储: {}", e);
            Err(e.into())
        }
    }
}
```

### 2. 数据操作 (`database/operations.rs`)

#### 单条插入

```rust
pub fn insert_chunk(&self, chunk: &DocumentChunk) -> Result<i64> {
    let embedding_blob = bincode::serialize(&chunk.embedding)?;
    
    let mut stmt = self.conn.prepare(r#"
        INSERT INTO document_chunks (
            book_title, book_author, md_file_path, file_order_in_book,
            related_chapter_titles, chunk_text, chunk_order_in_file,
            total_chunks_in_file, global_chunk_index, embedding_blob
        ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10)
    "#)?;
    
    let chunk_id = stmt.insert(params![
        chunk.book_title,
        chunk.book_author,
        chunk.md_file_path,
        chunk.file_order_in_book,
        chunk.related_chapter_titles,
        chunk.chunk_text,
        chunk.chunk_order_in_file,
        chunk.total_chunks_in_file,
        chunk.global_chunk_index,
        embedding_blob,
    ])?;
    
    // 如果 sqlite-vec 可用，同时插入向量表
    if self.vec_enabled {
        self.insert_vector(chunk_id, &chunk.embedding)?;
    }
    
    Ok(chunk_id)
}
```

#### 批量插入

```rust
pub fn insert_chunks_batch(&self, chunks: &[DocumentChunk]) -> Result<Vec<i64>> {
    let tx = self.conn.transaction()?;
    let mut chunk_ids = Vec::new();
    
    {
        let mut stmt = tx.prepare(r#"
            INSERT INTO document_chunks (
                book_title, book_author, md_file_path, file_order_in_book,
                related_chapter_titles, chunk_text, chunk_order_in_file,
                total_chunks_in_file, global_chunk_index, embedding_blob
            ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10)
        "#)?;
        
        for chunk in chunks {
            let embedding_blob = bincode::serialize(&chunk.embedding)?;
            let chunk_id = stmt.insert(params![
                chunk.book_title,
                chunk.book_author,
                chunk.md_file_path,
                chunk.file_order_in_book,
                chunk.related_chapter_titles,
                chunk.chunk_text,
                chunk.chunk_order_in_file,
                chunk.total_chunks_in_file,
                chunk.global_chunk_index,
                embedding_blob,
            ])?;
            chunk_ids.push(chunk_id);
        }
    }
    
    // 批量插入向量数据
    if self.vec_enabled {
        self.insert_vectors_batch(&tx, &chunk_ids, chunks)?;
    }
    
    tx.commit()?;
    Ok(chunk_ids)
}
```

### 3. 搜索功能 (`database/search.rs`)

#### 向量相似性搜索

```rust
pub fn search_similar(
    &self, 
    query_embedding: &[f32], 
    limit: usize
) -> Result<Vec<SearchResult>> {
    if self.vec_enabled {
        self.search_with_vec(query_embedding, limit)
    } else {
        self.search_with_cosine(query_embedding, limit)
    }
}

// 使用 sqlite-vec 的高性能搜索
fn search_with_vec(
    &self, 
    query_embedding: &[f32], 
    limit: usize
) -> Result<Vec<SearchResult>> {
    let mut stmt = self.conn.prepare(r#"
        SELECT 
            dc.id,
            dc.book_title,
            dc.book_author,
            dc.md_file_path,
            dc.related_chapter_titles,
            dc.chunk_text,
            dc.chunk_order_in_file,
            dc.total_chunks_in_file,
            dc.global_chunk_index,
            distance
        FROM vec_chunks vc
        JOIN document_chunks dc ON vc.id = dc.id
        WHERE vc.embedding MATCH ?1
        ORDER BY distance
        LIMIT ?2
    "#)?;
    
    let embedding_str = format!("[{}]", 
        query_embedding.iter()
            .map(|f| f.to_string())
            .collect::<Vec<_>>()
            .join(",")
    );
    
    let rows = stmt.query_map(params![embedding_str, limit], |row| {
        Ok(SearchResult {
            id: row.get(0)?,
            book_title: row.get(1)?,
            book_author: row.get(2)?,
            md_file_path: row.get(3)?,
            related_chapter_titles: row.get(4)?,
            chunk_text: row.get(5)?,
            chunk_order_in_file: row.get(6)?,
            total_chunks_in_file: row.get(7)?,
            global_chunk_index: row.get(8)?,
            similarity: 1.0 - row.get::<_, f32>(9)?, // 距离转相似度
        })
    })?;
    
    rows.collect()
}

// 降级的余弦相似度搜索
fn search_with_cosine(
    &self, 
    query_embedding: &[f32], 
    limit: usize
) -> Result<Vec<SearchResult>> {
    // 获取所有向量，计算余弦相似度
    let mut stmt = self.conn.prepare(r#"
        SELECT id, book_title, book_author, md_file_path, 
               related_chapter_titles, chunk_text, chunk_order_in_file,
               total_chunks_in_file, global_chunk_index, embedding_blob
        FROM document_chunks
    "#)?;
    
    let mut results = Vec::new();
    let rows = stmt.query_map([], |row| {
        let embedding_blob: Vec<u8> = row.get(9)?;
        let embedding: Vec<f32> = bincode::deserialize(&embedding_blob)
            .map_err(|e| rusqlite::Error::FromSqlConversionFailure(
                9, rusqlite::types::Type::Blob, Box::new(e)
            ))?;
        
        let similarity = cosine_similarity(query_embedding, &embedding);
        
        Ok((similarity, SearchResult {
            id: row.get(0)?,
            book_title: row.get(1)?,
            book_author: row.get(2)?,
            md_file_path: row.get(3)?,
            related_chapter_titles: row.get(4)?,
            chunk_text: row.get(5)?,
            chunk_order_in_file: row.get(6)?,
            total_chunks_in_file: row.get(7)?,
            global_chunk_index: row.get(8)?,
            similarity,
        }))
    })?;
    
    for row in rows {
        let (similarity, result) = row?;
        results.push((similarity, result));
    }
    
    // 按相似度排序并限制结果数
    results.sort_by(|a, b| b.0.partial_cmp(&a.0).unwrap());
    results.truncate(limit);
    
    Ok(results.into_iter().map(|(_, result)| result).collect())
}
```

#### 范围搜索

```rust
pub fn search_by_chapter(
    &self, 
    chapter_title: &str, 
    limit: usize
) -> Result<Vec<SearchResult>> {
    let mut stmt = self.conn.prepare(r#"
        SELECT id, book_title, book_author, md_file_path,
               related_chapter_titles, chunk_text, chunk_order_in_file,
               total_chunks_in_file, global_chunk_index
        FROM document_chunks
        WHERE related_chapter_titles LIKE ?1
        ORDER BY global_chunk_index
        LIMIT ?2
    "#)?;
    
    let pattern = format!("%{}%", chapter_title);
    let rows = stmt.query_map(params![pattern, limit], |row| {
        Ok(SearchResult {
            id: row.get(0)?,
            book_title: row.get(1)?,
            book_author: row.get(2)?,
            md_file_path: row.get(3)?,
            related_chapter_titles: row.get(4)?,
            chunk_text: row.get(5)?,
            chunk_order_in_file: row.get(6)?,
            total_chunks_in_file: row.get(7)?,
            global_chunk_index: row.get(8)?,
            similarity: 1.0, // 精确匹配
        })
    })?;
    
    rows.collect()
}
```

### 4. 数据模型

#### SearchResult

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    pub id: i64,                           // 分片 ID
    pub book_title: String,                // 书籍标题
    pub book_author: String,               // 书籍作者
    pub md_file_path: String,              // MD 文件路径
    pub related_chapter_titles: String,   // 相关章节标题（层级路径）
    pub chunk_text: String,                // 分片文本
    pub chunk_order_in_file: usize,        // 文件内分片顺序
    pub total_chunks_in_file: usize,       // 文件总分片数
    pub global_chunk_index: usize,         // 全局分片索引
    pub similarity: f32,                   // 相似度分数
}
```

## 性能优化

### 1. 索引策略

```sql
-- 基础索引
CREATE INDEX idx_book_title ON document_chunks(book_title);
CREATE INDEX idx_md_file_path ON document_chunks(md_file_path);
CREATE INDEX idx_global_chunk_index ON document_chunks(global_chunk_index);

-- 复合索引
CREATE INDEX idx_book_chapter ON document_chunks(book_title, related_chapter_titles);
CREATE INDEX idx_file_order ON document_chunks(md_file_path, chunk_order_in_file);

-- 全文搜索索引
CREATE VIRTUAL TABLE fts_chunks USING fts5(
    chunk_text,
    content='document_chunks',
    content_rowid='id'
);
```

### 2. 批量操作优化

```rust
// 事务批量插入
pub fn insert_chunks_batch(&self, chunks: &[DocumentChunk]) -> Result<Vec<i64>> {
    const BATCH_SIZE: usize = 1000;
    let mut all_ids = Vec::new();
    
    for chunk_batch in chunks.chunks(BATCH_SIZE) {
        let tx = self.conn.transaction()?;
        let mut batch_ids = Vec::new();
        
        // 批量插入主表
        for chunk in chunk_batch {
            let id = self.insert_chunk_in_tx(&tx, chunk)?;
            batch_ids.push(id);
        }
        
        // 批量插入向量表
        if self.vec_enabled {
            self.insert_vectors_batch(&tx, &batch_ids, chunk_batch)?;
        }
        
        tx.commit()?;
        all_ids.extend(batch_ids);
    }
    
    Ok(all_ids)
}
```

### 3. 连接池管理

```rust
pub struct DatabasePool {
    pool: Arc<Mutex<Vec<VectorDatabase>>>,
    max_connections: usize,
}

impl DatabasePool {
    pub fn new(db_path: &Path, dimension: usize, max_connections: usize) -> Result<Self>;
    
    pub fn get_connection(&self) -> Result<VectorDatabase>;
    
    pub fn return_connection(&self, conn: VectorDatabase);
}
```

## 错误处理

### 错误类型

```rust
#[derive(Debug, thiserror::Error)]
pub enum DatabaseError {
    #[error("SQLite error: {0}")]
    Sqlite(#[from] rusqlite::Error),
    
    #[error("Serialization error: {0}")]
    Serialization(#[from] bincode::Error),
    
    #[error("Vector dimension mismatch: expected {expected}, got {actual}")]
    DimensionMismatch { expected: usize, actual: usize },
    
    #[error("sqlite-vec not available")]
    VecNotAvailable,
    
    #[error("Database connection failed: {0}")]
    ConnectionFailed(String),
}
```

### 容错机制

```rust
// 自动降级
impl VectorDatabase {
    pub fn search_with_fallback(
        &self, 
        query_embedding: &[f32], 
        limit: usize
    ) -> Result<Vec<SearchResult>> {
        match self.search_with_vec(query_embedding, limit) {
            Ok(results) => Ok(results),
            Err(_) => {
                log::warn!("向量搜索失败，降级到余弦相似度搜索");
                self.search_with_cosine(query_embedding, limit)
            }
        }
    }
}

// 事务回滚
pub fn insert_with_retry(&self, chunk: &DocumentChunk) -> Result<i64> {
    const MAX_RETRIES: usize = 3;
    
    for attempt in 0..MAX_RETRIES {
        match self.insert_chunk(chunk) {
            Ok(id) => return Ok(id),
            Err(e) if attempt < MAX_RETRIES - 1 => {
                log::warn!("插入失败，重试 {}/{}: {}", attempt + 1, MAX_RETRIES, e);
                std::thread::sleep(Duration::from_millis(100 * (attempt + 1) as u64));
                continue;
            }
            Err(e) => return Err(e),
        }
    }
    
    unreachable!()
}
```

## 使用示例

### 基本使用

```rust
use crate::database::VectorDatabase;
use crate::models::DocumentChunk;

// 初始化数据库
let mut db = VectorDatabase::new("vectors.sqlite", 1024)?;
db.initialize_vec_table()?;

// 插入数据
let chunk = DocumentChunk {
    book_title: "示例书籍".to_string(),
    book_author: "示例作者".to_string(),
    md_file_path: "chapter1.md".to_string(),
    related_chapter_titles: "第一部分|第一章".to_string(),
    chunk_text: "这是一段示例文本".to_string(),
    embedding: vec![0.1; 1024],
    // ... 其他字段
};

let chunk_id = db.insert_chunk(&chunk)?;
println!("插入成功，ID: {}", chunk_id);

// 搜索
let query_embedding = vec![0.1; 1024];
let results = db.search_similar(&query_embedding, 5)?;

for result in results {
    println!("相似度: {:.3}", result.similarity);
    println!("章节: {}", result.related_chapter_titles);
    println!("内容: {}", &result.chunk_text[..100]);
}
```

### 批量操作

```rust
// 批量插入
let chunks = vec![chunk1, chunk2, chunk3];
let ids = db.insert_chunks_batch(&chunks)?;
println!("批量插入完成，插入 {} 条记录", ids.len());

// 按章节搜索
let chapter_results = db.search_by_chapter("第一章", 10)?;
println!("找到 {} 个相关分片", chapter_results.len());
```

## 监控和维护

### 数据库统计

```rust
pub fn get_database_stats(&self) -> Result<DatabaseStats> {
    let mut stmt = self.conn.prepare(r#"
        SELECT 
            COUNT(*) as total_chunks,
            COUNT(DISTINCT book_title) as total_books,
            COUNT(DISTINCT md_file_path) as total_files,
            AVG(LENGTH(chunk_text)) as avg_chunk_length
        FROM document_chunks
    "#)?;
    
    let stats = stmt.query_row([], |row| {
        Ok(DatabaseStats {
            total_chunks: row.get(0)?,
            total_books: row.get(1)?,
            total_files: row.get(2)?,
            avg_chunk_length: row.get(3)?,
        })
    })?;
    
    Ok(stats)
}
```

### 数据库维护

```rust
// 清理和优化
pub fn vacuum(&self) -> Result<()> {
    self.conn.execute("VACUUM", [])?;
    Ok(())
}

// 重建索引
pub fn reindex(&self) -> Result<()> {
    self.conn.execute("REINDEX", [])?;
    Ok(())
}

// 数据库完整性检查
pub fn integrity_check(&self) -> Result<bool> {
    let mut stmt = self.conn.prepare("PRAGMA integrity_check")?;
    let result: String = stmt.query_row([], |row| row.get(0))?;
    Ok(result == "ok")
}
```
