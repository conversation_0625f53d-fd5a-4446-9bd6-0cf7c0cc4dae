# 架构设计

## 整体架构

`tauri-plugin-epub` 采用分层架构设计，从上到下分为：

```
┌─────────────────────────────────────────┐
│           Tauri Commands Layer          │  <- API 接口层
├─────────────────────────────────────────┤
│          Core Pipeline Layer           │  <- 核心业务层
├─────────────────────────────────────────┤
│    Processing Modules Layer            │  <- 处理模块层
├─────────────────────────────────────────┤
│         Database Layer                 │  <- 数据存储层
└─────────────────────────────────────────┘
```

## 模块架构图

```mermaid
graph TD
    A[Tauri Commands] --> B[Core Pipeline]
    B --> C[EPUB Processing]
    B --> D[Text Processing]
    B --> E[Database Operations]
    
    C --> C1[EPUB Reader]
    C --> C2[TOC Parser]
    
    D --> D1[Text Chunker]
    D --> D2[Text Sanitizer]
    D --> D3[Tokenizer]
    D --> D4[Vectorizer]
    
    E --> E1[Connection Manager]
    E --> E2[Data Operations]
    E --> E3[Search Engine]
    
    E1 --> F[SQLite + sqlite-vec]
```

## 核心组件详解

### 1. Commands Layer (命令层)

**职责**：提供 Tauri 前端调用的 API 接口

**主要组件**：
- `commands.rs` - 所有 Tauri 命令的定义和实现
- 参数验证和错误处理
- 状态管理和会话控制

**关键接口**：
```rust
// 核心命令
pub async fn index_epub() -> Result<IndexResult, String>
pub async fn search_db() -> Result<Vec<SearchItemDto>, String>
pub fn get_chunk_with_context() -> Result<Vec<DocumentChunkDto>, String>
pub async fn convert_to_mdbook() -> Result<ConvertResult, String>
```

### 2. Core Pipeline (核心流水线)

**职责**：协调整个EPUB处理流程

**核心流程**：
```
EPUB File → Parse → MDBook → Chunk → Vectorize → Database
```

**关键特性**：
- 流水线处理：边处理边向量化，避免内存积累
- 错误恢复：单点失败不影响整体流程
- 进度报告：实时报告处理进度

**主要函数**：
```rust
pub async fn process_epub_to_db() -> Result<ProcessReport>
pub async fn search_db() -> Result<Vec<SearchResult>>
```

### 3. EPUB Processing Layer (EPUB 处理层)

#### 3.1 EPUB Reader (`epub/reader.rs`)
**职责**：解析 EPUB 文件结构和内容

**核心功能**：
- 解析 EPUB 压缩包结构
- 提取元数据（metadata.opf）
- 读取章节内容
- 支持 Markdown 文件分片

**实现细节**：
```rust
pub struct EpubReader {
    // EPUB 解析器配置
}

impl EpubReader {
    pub fn read_epub(&self, path: &Path) -> Result<EpubContent>;
    pub fn chunk_md_file(&self, content: &str, min_size: usize, max_size: usize) -> Vec<String>;
}
```

#### 3.2 TOC Parser (`epub/toc_parser.rs`)
**职责**：解析和处理目录结构

**核心功能**：
- 解析 `toc.ncx` 文件
- 构建层级目录结构
- 扁平化目录树为线性列表
- 关联文件路径和章节标题

**数据结构**：
```rust
pub struct TocNode {
    pub id: String,
    pub title: String,
    pub src: String,
    pub depth: u32,
    pub children: Vec<TocNode>,
}

pub struct FlatTocNode {
    pub title: String,
    pub md_src: String,
    pub depth: u32,
    pub play_order: u32,
}
```

### 4. Text Processing Layer (文本处理层)

#### 4.1 Text Chunker (`text/chunker.rs`)
**职责**：智能文本分片

**分片策略**：
1. **Markdown 感知**：基于 Markdown 结构（标题、段落、列表）
2. **语义边界**：保持句子和段落完整性
3. **尺寸控制**：控制分片的最小和最大长度
4. **重叠处理**：避免语义断裂

**核心算法**：
```rust
pub struct MarkdownChunker {
    pub min_chunk_size: usize,
    pub max_chunk_size: usize,
    pub overlap_size: usize,
}

impl MarkdownChunker {
    pub fn chunk_markdown(&self, content: &str) -> Vec<Chunk>;
    pub fn find_semantic_boundaries(&self, text: &str) -> Vec<usize>;
}
```

#### 4.2 Text Sanitizer (`text/sanitizer.rs`)
**职责**：文本清理和标准化

**处理内容**：
- HTML 标签移除
- 特殊字符标准化
- 空白字符处理
- 编码转换

#### 4.3 Vectorizer (`text/vectorizer.rs`)
**职责**：文本向量化

**支持的向量化服务**：
- 本地 llama.cpp 服务
- 外部 API（OpenAI、Anthropic 等）
- 自定义向量化端点

**配置结构**：
```rust
pub struct VectorizerConfig {
    pub base_url: String,
    pub model_name: String,
    pub api_key: Option<String>,
}

pub struct TextVectorizer {
    config: VectorizerConfig,
    client: reqwest::Client,
}
```

### 5. Database Layer (数据库层)

#### 5.1 Connection Manager (`database/connection.rs`)
**职责**：数据库连接和配置管理

**核心特性**：
- 自动注册 sqlite-vec 扩展
- 智能降级到标准 SQLite 存储
- 连接池和事务管理
- 性能优化配置

**实现**：
```rust
pub struct DatabaseConnection {
    conn: Connection,
    embedding_dimension: usize,
}

impl DatabaseConnection {
    pub fn new(db_path: P, embedding_dimension: usize) -> Result<Self>;
    pub fn supports_vector_search(&self) -> bool;
    pub fn begin_transaction(&mut self) -> Result<()>;
}
```

#### 5.2 Data Operations (`database/operations.rs`)
**职责**：数据库 CRUD 操作

**主要功能**：
- 文档分片的批量插入
- 向量数据的存储管理
- 事务控制和错误恢复

**关键方法**：
```rust
pub struct DatabaseOperations<'a> {
    db: &'a mut DatabaseConnection,
}

impl<'a> DatabaseOperations<'a> {
    pub fn insert_chunk(&mut self, chunk: &DocumentChunk) -> Result<i64>;
    pub fn insert_chunks_batch(&mut self, chunks: &[DocumentChunk]) -> Result<Vec<i64>>;
    pub fn insert_embedding(&mut self, chunk_id: i64, embedding: &[f32]) -> Result<()>;
}
```

#### 5.3 Search Engine (`database/search.rs`)
**职责**：各种搜索和检索功能

**搜索类型**：
1. **语义搜索**：基于向量相似度
2. **范围搜索**：基于全局索引范围
3. **上下文搜索**：获取指定分片的前后文
4. **章节搜索**：基于章节标题

**实现架构**：
```rust
pub struct DatabaseSearch<'a> {
    db: &'a DatabaseConnection,
}

impl<'a> DatabaseSearch<'a> {
    pub fn vector_search(&self, query_embedding: &[f32], limit: usize) -> Result<Vec<SearchResult>>;
    pub fn get_related_chunks(&self, book_title: &str, book_author: &str, chunk_id: i64, context_size: usize) -> Result<Vec<DocumentChunk>>;
    pub fn text_search(&self, query: &str, limit: usize) -> Result<Vec<SearchResult>>;
}
```

## 数据流架构

### 1. 向量化数据流

```mermaid
sequenceDiagram
    participant C as Commands
    participant P as Pipeline
    participant E as EPUB Reader
    participant T as Text Processor
    participant V as Vectorizer
    participant D as Database

    C->>P: index_epub()
    P->>E: read_epub()
    E-->>P: EpubContent
    P->>E: convert_to_mdbook()
    E-->>P: MDBook files
    
    loop For each MD file
        P->>T: chunk_markdown()
        T-->>P: text chunks
        
        loop For each chunk
            P->>V: vectorize_text()
            V-->>P: embedding vector
            P->>D: insert_chunk_with_embedding()
        end
    end
    
    P-->>C: ProcessReport
```

### 2. 搜索数据流

```mermaid
sequenceDiagram
    participant C as Commands
    participant V as Vectorizer  
    participant D as Database
    participant S as Search Engine

    C->>V: vectorize_text(query)
    V-->>C: query_embedding
    C->>S: vector_search(embedding, limit)
    S->>D: execute similarity query
    D-->>S: raw results
    S-->>C: SearchResults
```

## 设计原则

### 1. 模块化设计
- **单一职责**：每个模块专注于特定功能
- **松耦合**：模块间通过明确接口交互
- **高内聚**：相关功能聚集在同一模块

### 2. 错误处理策略
- **分层错误处理**：不同层级有不同的错误处理策略
- **优雅降级**：关键功能失败时有备用方案
- **详细日志**：完整的错误追踪和调试信息

### 3. 性能优化
- **流水线处理**：避免全量数据在内存中积累
- **批量操作**：数据库批量插入和查询
- **索引优化**：合理的数据库索引设计

### 4. 可扩展性
- **插件架构**：支持自定义处理器
- **配置驱动**：通过配置控制行为
- **接口抽象**：核心接口支持多种实现

## 技术选型理由

### 1. SQLite + sqlite-vec
- **轻量级**：无需独立数据库服务
- **高性能**：本地文件访问，低延迟
- **向量支持**：原生向量相似度搜索
- **事务支持**：ACID 特性保证数据一致性

### 2. 流水线架构
- **内存效率**：避免大文件全量加载
- **容错性**：单点失败不影响整体
- **实时反馈**：处理进度实时报告

### 3. 模块化设计
- **开发效率**：团队协作和并行开发
- **维护性**：代码组织清晰，易于维护
- **测试性**：每个模块可独立测试

## 扩展点

### 1. 自定义文本处理器
```rust
pub trait TextProcessor {
    fn process(&self, text: &str) -> Result<String>;
}
```

### 2. 自定义向量化服务
```rust
pub trait VectorService {
    async fn vectorize(&self, text: &str) -> Result<Vec<f32>>;
}
```

### 3. 自定义搜索策略
```rust
pub trait SearchStrategy {
    fn search(&self, query: &SearchQuery) -> Result<Vec<SearchResult>>;
}
``` 