# Tauri Plugin EPUB - 技术文档

## 概述

`tauri-plugin-epub` 是一个功能完整的 Tauri 插件，专门用于处理 EPUB 电子书文件的解析、向量化和智能检索。该插件实现了从 EPUB 文件到可搜索向量数据库的完整工作流程，支持语义搜索、上下文检索等高级功能。

## 核心功能

### 📚 EPUB 处理
- **EPUB 解析**：完整解析 EPUB 文件结构，提取元数据、内容和目录
- **格式转换**：将 EPUB 转换为 MDBook 格式，便于后续处理
- **目录解析**：解析和扁平化 TOC（Table of Contents）结构

### 🔍 文本处理与分片
- **智能分片**：支持基于 Markdown 结构的智能文本分片
- **内容清理**：移除HTML标签、标准化文本格式
- **语义边界**：保持语义完整性的分片边界

### 🎯 向量化与检索
- **向量生成**：支持本地和远程向量化服务
- **高效存储**：基于 SQLite + sqlite-vec 的向量存储
- **多种检索**：语义搜索、范围检索、上下文检索

### 🗄️ 数据管理
- **数据库设计**：模块化的数据库架构
- **事务管理**：完整的事务支持和错误恢复
- **性能优化**：索引优化、批量操作

## 技术架构

```
tauri-plugin-epub/
├── commands/          # Tauri 命令接口层
├── pipeline/          # 核心处理流水线
├── models/            # 数据模型定义
├── database/          # 数据库管理层
│   ├── connection     # 连接管理
│   ├── operations     # 数据操作
│   └── search         # 搜索功能
├── epub/              # EPUB 处理层
│   ├── reader         # EPUB 读取
│   └── toc_parser     # 目录解析
└── text/              # 文本处理层
    ├── chunker        # 分片处理
    ├── sanitizer      # 内容清理
    ├── tokenizer      # 分词处理
    └── vectorizer     # 向量化
```

## 文档导航

### 🏗️ 架构与设计
- [**架构设计**](./architecture.md) - 整体架构设计和模块关系
- [**数据模型**](./models.md) - 核心数据结构和类型定义
- [**数据库设计**](./database.md) - 数据库架构和存储策略

### 🔧 核心模块
- [**命令接口**](./commands.md) - Tauri 命令和 API 接口
- [**处理流水线**](./pipeline.md) - 核心业务处理流程
- [**EPUB 处理**](./epub-processing.md) - EPUB 解析和转换
- [**文本处理**](./text-processing.md) - 分片、清理和预处理
- [**向量化系统**](./vectorization.md) - 向量生成和存储
- [**搜索功能**](./search-system.md) - 各种搜索和检索功能

### 📖 开发指南
- [**开发指南**](./development-guide.md) - 开发环境、编译和测试
- [**API 参考**](./api-reference.md) - 完整的 API 文档
- [**性能优化**](./performance.md) - 性能优化策略和最佳实践
- [**故障排除**](./troubleshooting.md) - 常见问题和解决方案

## 快速开始

### 1. 插件注册
```rust
use tauri_plugin_epub;

fn main() {
    tauri::Builder::default()
        .plugin(tauri_plugin_epub::init())
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
```

### 2. 基本使用
```typescript
import { invoke } from "@tauri-apps/api/core";

// 向量化 EPUB 文件
const result = await invoke("plugin:epub|index_epub", {
  bookId: "book-id",
  dimension: 1024,
  baseUrl: "http://localhost:3544",
  model: "local-embed",
  apiKey: null,
});

// 语义搜索
const searchResults = await invoke("plugin:epub|search_db", {
  bookId: "book-id", 
  query: "搜索查询",
  limit: 5,
  dimension: 1024,
  baseUrl: "http://localhost:3544",
  model: "local-embed",
  apiKey: null,
});
```

## 技术特性

### 🚀 性能特性
- **流水线处理**：边分片边向量化，避免内存积累
- **批量操作**：支持批量数据库操作，提高效率
- **增量更新**：支持增量向量化和数据更新

### 🛡️ 可靠性
- **错误恢复**：完整的错误处理和事务回滚
- **容错设计**：单点失败不影响整体流程
- **详细日志**：完整的操作日志和错误追踪

### 🔧 可扩展性
- **模块化设计**：清晰的模块边界，易于扩展
- **插件架构**：支持自定义处理器和扩展
- **配置灵活**：丰富的配置选项和定制能力

## 版本信息

- **当前版本**：v1.0.0
- **Tauri 版本**：2.x
- **Rust 版本**：1.70+
- **依赖关系**：详见 `Cargo.toml`

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。 