# Tauri电子书阅读器 - 架构总览文档

## 项目概述

这是一个基于Tauri框架开发的跨平台电子书阅读器，集成了AI对话功能和向量化搜索能力。支持多种电子书格式，提供现代化的阅读体验和智能内容检索。

## 技术架构

### 整体架构模式
```
┌─────────────────────────────────────────────────────────────┐
│                    Tauri Desktop App                        │
├─────────────────────────────────────────────────────────────┤
│  Frontend (React + TypeScript)                             │
│  ├── UI Components (Radix UI + Tailwind)                   │
│  ├── State Management (Zustand + Jotai)                    │
│  ├── AI Integration (Multiple Provider SDKs)               │
│  └── Book Reader (foliate-js)                              │
├─────────────────────────────────────────────────────────────┤
│  Backend (Rust + Tauri)                                    │
│  ├── Core APIs (Books, Tags, Threads)                      │
│  ├── Plugin System                                         │
│  │   ├── tauri-plugin-epub (EPUB处理 + 向量化)            │
│  │   └── tauri-plugin-llamacpp (本地LLM服务)               │
│  └── Database Layer (SQLite + sqlx)                        │
├─────────────────────────────────────────────────────────────┤
│  Data Layer                                                 │
│  ├── SQLite Database (书籍元数据 + 用户数据)                │
│  ├── Vector Database (sqlite-vec 向量存储)                 │
│  └── File System (书籍文件 + 封面图片)                     │
└─────────────────────────────────────────────────────────────┘
```

## 核心技术栈

### 前端技术栈
| 技术 | 版本 | 用途 |
|------|------|------|
| **React** | 19.1.0 | 前端框架 |
| **TypeScript** | 5.8.3 | 类型系统 |
| **Vite** | 7.0.4 | 构建工具 |
| **Tailwind CSS** | 4.1.11 | 样式框架 |
| **Radix UI** | 最新 | 无障碍UI组件库 |
| **Zustand** | 5.0.6 | 全局状态管理 |
| **Jotai** | 2.13.1 | 原子化状态管理 |
| **React Router** | 7.7.1 | 路由管理 |
| **AI SDK** | 最新 | 多AI提供商集成 |
| **foliate-js** | 定制版 | 电子书渲染引擎 |

### 后端技术栈
| 技术 | 版本 | 用途 |
|------|------|------|
| **Tauri** | 2.x | 跨平台应用框架 |
| **Rust** | 2021 | 后端编程语言 |
| **SQLite** | 最新 | 主数据库 |
| **sqlx** | 0.8.6 | 异步SQL工具库 |
| **sqlite-vec** | 0.1.6 | 向量数据库扩展 |
| **tokio** | 1.x | 异步运行时 |
| **epub** | 2.1.4 | EPUB文件解析 |
| **tiktoken-rs** | 0.7.0 | OpenAI兼容分词器 |

### 支撑服务
| 服务 | 描述 |
|------|------|
| **llama.cpp** | 本地LLM推理服务器 |
| **epub2mdbook** | EPUB到Markdown转换工具 |
| **向量化API** | 文本嵌入向量化服务 |

## 功能模块架构

### 1. 图书管理模块
```
Book Management
├── 文件上传与解析
│   ├── 多格式支持 (EPUB/PDF/MOBI/CBZ/FB2/FBZ)
│   ├── 元数据自动提取
│   └── 封面图片处理
├── 图书库管理
│   ├── 分类标签系统
│   ├── 搜索与筛选
│   └── 阅读状态跟踪
└── 数据持久化
    ├── 文件系统存储
    └── 数据库元数据
```

### 2. 阅读器模块
```
Reading Engine
├── foliate-js 渲染核心
│   ├── 多格式文档渲染
│   ├── 响应式布局
│   └── 交互功能
├── 阅读功能
│   ├── 进度追踪
│   ├── 书签管理
│   ├── 注释系统
│   └── 主题定制
└── 导航系统
    ├── 目录(TOC)导航
    ├── 页面跳转
    └── 全文搜索
```

### 3. AI集成模块
```
AI Integration
├── 多提供商支持
│   ├── OpenAI/GPT系列
│   ├── Anthropic Claude
│   ├── DeepSeek
│   ├── Google Gemini
│   └── 本地LLM (llama.cpp)
├── RAG增强系统
│   ├── 文本向量化
│   ├── 相似度搜索
│   └── 上下文检索
└── 对话管理
    ├── 会话持久化
    ├── 消息历史
    └── 多轮对话
```

### 4. 向量化检索模块
```
Vector Search
├── EPUB处理流水线
│   ├── EPUB → mdBook 转换
│   ├── TOC解析与提取
│   └── 智能文本分片
├── 向量化服务
│   ├── 本地嵌入服务器
│   ├── 批量处理优化
│   └── 维度动态检测
└── 检索引擎
    ├── sqlite-vec 向量存储
    ├── 相似度计算
    └── 结果排序与过滤
```

## 数据流架构

### 书籍导入流程
```
用户上传文件 → 格式检测 → 元数据提取 → 文件存储 → 数据库记录
                     ↓
               EPUB转mdBook → TOC解析 → 文本分片 → 向量化 → 向量库存储
```

### AI对话流程
```
用户提问 → 上下文检索 → 相关内容提取 → AI提供商调用 → 流式响应 → 对话记录
            ↑                                                      ↓
        向量相似度搜索 ←────────────────────── RAG增强 ←──── 会话历史持久化
```

### 阅读流程
```
选择书籍 → 加载文档 → 渲染页面 → 用户交互 → 状态更新 → 进度同步
            ↑                        ↓
        配置加载 ←──────── 设置保存 ←─── 阅读偏好
```

## 架构特点

### 1. 模块化设计
- **插件化架构**: 核心功能通过Tauri插件实现，易于扩展和维护
- **组件化前端**: React组件化开发，职责单一，复用性强
- **分层架构**: 表现层、业务层、数据层清晰分离

### 2. 高性能
- **异步处理**: 后端基于tokio异步运行时，支持高并发
- **向量优化**: 专业向量数据库，毫秒级相似度查询
- **渐进式加载**: 大文件分片处理，内存占用可控
- **缓存策略**: 多层缓存减少重复计算

### 3. 跨平台支持
- **Tauri框架**: 一套代码支持Windows、macOS、Linux
- **Web技术栈**: 前端技术栈确保一致的用户体验
- **原生性能**: Rust后端提供接近原生应用的性能

### 4. 可扩展性
- **AI提供商**: 工厂模式支持任意AI服务商扩展
- **文件格式**: 插件架构支持新文件格式扩展
- **主题系统**: Tailwind CSS + 变量系统支持主题定制
- **本地化**: i18next多语言支持

### 5. 数据安全
- **本地优先**: 所有数据本地存储，隐私保护
- **事务支持**: 数据库操作事务保护，确保数据一致性
- **错误恢复**: 完善的错误处理和数据恢复机制

## 技术决策说明

### 为什么选择Tauri？
1. **跨平台**: 一套代码支持三大桌面平台
2. **性能优异**: Rust后端提供原生级性能
3. **包体积小**: 相比Electron显著减小安装包大小
4. **Web技术**: 前端可使用熟悉的Web技术栈
5. **安全性**: 默认安全，权限控制精细

### 为什么选择React + TypeScript？
1. **生态丰富**: 大量成熟组件库和工具链
2. **类型安全**: TypeScript提供编译时类型检查
3. **开发效率**: 热重载、调试工具完善
4. **团队技能**: 团队对React技术栈熟悉

### 为什么选择Zustand + Jotai？
1. **Zustand**: 轻量级全局状态管理，API简洁
2. **Jotai**: 原子化状态管理，避免不必要的重渲染
3. **性能优化**: 细粒度状态更新，提升应用性能
4. **开发体验**: 类型友好，调试工具完善

### 为什么选择SQLite + sqlite-vec？
1. **零配置**: 嵌入式数据库，无需额外安装
2. **向量支持**: sqlite-vec扩展提供向量存储能力
3. **ACID特性**: 完整的事务支持
4. **跨平台**: 所有平台一致的数据库体验
5. **性能优异**: 本地文件访问，查询速度快

## 部署架构

### 开发环境
```
Developer Machine
├── Node.js 18+ (前端开发)
├── Rust 1.70+ (后端开发)
├── Tauri CLI (应用构建)
└── pnpm (包管理)
```

### 构建产物
```
Release Package
├── 可执行文件 (.exe/.app/.AppImage)
├── 静态资源文件
├── 依赖库文件
└── 安装程序 (可选)
```

### 运行时架构
```
User Machine
├── 应用主进程 (Tauri Core)
├── WebView进程 (前端UI)
├── Rust后端服务
├── SQLite数据库文件
├── 本地文件存储
└── llama.cpp服务进程 (可选)
```

这个架构设计充分体现了现代桌面应用的最佳实践，在性能、用户体验、可维护性之间达到了良好的平衡。 