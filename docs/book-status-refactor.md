# 书籍状态重构文档

## 概述

本次重构将书籍的阅读进度和状态信息从 `books` 表中分离出来，创建了独立的 `book_status` 表。这样做的目的是：

1. **职责分离**：`books` 表专注于书籍基本信息，`book_status` 表专注于阅读状态
2. **查询性能**：便于按状态、进度、阅读时间等维度进行筛选和排序
3. **扩展性**：为未来添加更多阅读统计功能提供基础

## 数据库结构变更

### books 表变更

**删除的字段：**
```sql
-- 删除了以下字段
progress_current INTEGER DEFAULT 0,
progress_total INTEGER DEFAULT 0,
deleted_at INTEGER  -- 移除软删除功能
```

### 新增 book_status 表

```sql
-- 书籍状态表
CREATE TABLE IF NOT EXISTS book_status (
    book_id TEXT PRIMARY KEY NOT NULL,
    status TEXT NOT NULL DEFAULT 'unread',  -- 'unread', 'reading', 'completed'
    progress_current INTEGER DEFAULT 0,
    progress_total INTEGER DEFAULT 0,
    last_reading_position TEXT,     -- CFI 位置
    reading_time_minutes INTEGER DEFAULT 0,  -- 累计阅读时间（分钟）
    last_read_at INTEGER,          -- 最后阅读时间
    started_at INTEGER,            -- 开始阅读时间
    completed_at INTEGER,          -- 完成阅读时间
    metadata TEXT,                 -- JSON 存储其他信息（设置、偏好等）
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);

-- 书籍状态相关索引
CREATE INDEX IF NOT EXISTS idx_book_status_status ON book_status(status);
CREATE INDEX IF NOT EXISTS idx_book_status_progress ON book_status(progress_current, progress_total);
CREATE INDEX IF NOT EXISTS idx_book_status_last_read ON book_status(last_read_at DESC);
CREATE INDEX IF NOT EXISTS idx_book_status_updated_at ON book_status(updated_at DESC);
```

## 类型定义变更

### SimpleBook 接口

**删除的字段：**
```typescript
progressCurrent?: number;
progressTotal?: number;
deletedAt?: number;  // 移除软删除功能
```

### 新增接口

```typescript
export interface BookStatus {
  bookId: string;
  status: 'unread' | 'reading' | 'completed';
  progressCurrent: number;
  progressTotal: number;
  lastReadingPosition?: string;
  readingTimeMinutes: number;
  lastReadAt?: number;
  startedAt?: number;
  completedAt?: number;
  metadata?: any; // JSON 存储其他信息（设置、偏好等）
  createdAt: number;
  updatedAt: number;
}

export interface BookStatusUpdateData {
  status?: 'unread' | 'reading' | 'completed';
  progressCurrent?: number;
  progressTotal?: number;
  lastReadingPosition?: string;
  readingTimeMinutes?: number;
  lastReadAt?: number;
  startedAt?: number;
  completedAt?: number;
  metadata?: any; // JSON 存储其他信息（设置、偏好等）
}

export interface BookWithStatus extends SimpleBook {
  status?: BookStatus;
}
```

## Rust 后端变更

### 模型变更

1. **SimpleBook 模型**：删除了 `progress_current` 和 `progress_total` 字段
2. **新增 BookStatus 模型**：完整的书籍状态信息
3. **新增 BookStatusUpdateData 模型**：用于部分更新书籍状态
4. **新增 BookWithStatus 模型**：包含书籍和状态的组合模型

### 命令变更

**删除的命令：**
- `update_book_progress`
- `permanent_delete_book`（移除软删除功能）
- `restore_book`（移除软删除功能）

**新增的命令：**
- `get_book_status(book_id: String) -> Option<BookStatus>`
- `update_book_status(book_id: String, update_data: BookStatusUpdateData) -> BookStatus`
- `get_books_with_status(options: Option<BookQueryOptions>) -> Vec<BookWithStatus>`

**修改的命令：**
- `save_book`：现在会自动创建对应的 book_status 记录
- `delete_book`：现在直接删除书籍和文件，不再使用软删除

## 前端 API 变更

### 新增函数

```typescript
// 获取书籍状态
export async function getBookStatus(bookId: string): Promise<BookStatus | null>

// 更新书籍状态
export async function updateBookStatus(bookId: string, updateData: BookStatusUpdateData): Promise<BookStatus>

// 获取带状态的书籍列表
export async function getBooksWithStatus(options: BookQueryOptions = {}): Promise<BookWithStatus[]>

// 便捷函数：更新阅读进度
export async function updateBookProgress(bookId: string, current: number, total: number): Promise<BookStatus>
```

## 迁移说明

### 自动迁移

当应用启动时，数据库会自动应用新的 schema：
1. 创建 `book_status` 表
2. 为现有书籍自动创建对应的状态记录（状态为 'unread'）

### 数据一致性

- 每当创建新书籍时，会自动创建对应的 book_status 记录
- book_status 表通过外键约束确保数据一致性
- 删除书籍时会级联删除对应的状态记录
- **移除软删除功能**：删除操作现在直接删除书籍文件和数据库记录，无法恢复

## 使用示例

### 获取带状态的书籍列表

```typescript
import { getBooksWithStatus } from '@/services/bookService';

// 获取所有书籍及其状态
const booksWithStatus = await getBooksWithStatus();

// 只获取正在阅读的书籍
const readingBooks = await getBooksWithStatus({
  // 注意：目前需要在 Rust 后端添加按状态筛选的功能
});
```

### 更新阅读进度

```typescript
import { updateBookProgress, updateBookStatus } from '@/services/bookService';

// 简单更新进度
await updateBookProgress('book-id', 150, 300);

// 详细更新状态
await updateBookStatus('book-id', {
  status: 'reading',
  progressCurrent: 150,
  progressTotal: 300,
  lastReadingPosition: 'epubcfi(/6/14[chapter01]!/4/2/1:25)',
  lastReadAt: Date.now(),
  startedAt: Date.now() - 86400000, // 昨天开始阅读
});
```

### 标记书籍完成

```typescript
await updateBookStatus('book-id', {
  status: 'completed',
  progressCurrent: 300,
  progressTotal: 300,
  completedAt: Date.now(),
});
```

### 使用 metadata 存储个人设置

```typescript
// 存储阅读设置
await updateBookStatus('book-id', {
  metadata: {
    readingSettings: {
      fontSize: 16,
      fontFamily: 'serif',
      theme: 'dark',
      lineHeight: 1.6,
    },
    bookmarks: ['epubcfi(/6/14[chapter01]!/4/2/1:25)'],
    notes: [
      {
        cfi: 'epubcfi(/6/14[chapter01]!/4/2/1:25)',
        text: '这是一个重要的观点',
        createdAt: Date.now(),
      }
    ],
    personalRating: 4.5,
    tags: ['技术', '编程', '必读'],
  }
});

// 获取并使用设置
const status = await getBookStatus('book-id');
if (status?.metadata?.readingSettings) {
  const { fontSize, theme } = status.metadata.readingSettings;
  // 应用设置到阅读器
}
```

## 后续扩展

基于新的 book_status 表结构，可以轻松实现以下功能：

1. **阅读统计**：按状态统计书籍数量
2. **进度排序**：按阅读进度排序书籍列表
3. **最近阅读**：显示最近阅读的书籍
4. **阅读时间统计**：跟踪每本书的阅读时间
5. **阅读目标**：设置和跟踪阅读目标
6. **阅读历史**：记录阅读历史和习惯分析
7. **个性化设置**：通过 metadata 存储每本书的个人阅读设置
8. **书签和笔记**：在 metadata 中存储个人书签和阅读笔记
9. **个人评分和标签**：存储个人对书籍的评分和自定义标签

## 注意事项

1. **向后兼容**：保留了 `updateBookProgress` 函数作为便捷方法，确保现有代码可以正常工作
2. **性能优化**：通过索引优化了常用查询场景
3. **数据完整性**：使用外键约束确保数据一致性
4. **扩展性**：新的表结构为未来功能扩展预留了空间

## 测试建议

1. **数据库迁移测试**：确保现有数据正确迁移
2. **API 功能测试**：测试所有新增的 API 函数
3. **性能测试**：验证查询性能是否符合预期
4. **边界情况测试**：测试删除书籍、更新不存在的状态等边界情况
