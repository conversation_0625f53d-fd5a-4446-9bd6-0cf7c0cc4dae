# Library 组件设计文档
 
本目录包含关于图书馆组件设计的详细文档。

## 文档结构

- **architecture.md** - 图书馆架构设计概览
- **state-management.md** - 状态管理和数据流设计
- **book-management.md** - 书籍管理系统详解
- **tag-system.md** - 标签系统设计分析
- **upload-system.md** - 书籍上传系统详解
- **ai-integration.md** - AI 集成功能详解

## 概述

Library 组件是应用的书籍管理中心，负责书籍的展示、组织、上传和管理。它采用了模块化设计，支持多种视图模式、智能标签系统、AI 功能集成等高级特性。

## 核心特性

- **多视图支持**: 网格视图和列表视图
- **智能标签系统**: 支持手动标签和 AI 标签建议
- **书籍上传**: 支持多种格式的书籍文件上传
- **向量化处理**: 支持书籍内容的向量化和智能搜索
- **批量操作**: 支持批量删除、标签管理等操作
- **元数据编辑**: 支持书籍信息的编辑和封面管理
- **AI 标签生成**: 基于书籍内容自动生成合适的标签

## 技术栈

- **React + TypeScript**: 前端框架
- **Zustand**: 全局状态管理
- **Tauri**: 桌面应用程序接口和文件系统访问
- **AI SDK**: AI 模型集成和标签生成
- **Rust**: 后端书籍处理和数据库操作
- **SQLite**: 本地数据库存储

## 核心组件

- **BookItem**: 单个书籍项展示组件
- **Sidebar**: 导航和标签管理侧边栏
- **Upload**: 书籍上传界面
- **TagList**: 标签列表管理
- **EmbeddingDialog**: 向量化管理对话框
- **ViewMenu**: 视图选项菜单