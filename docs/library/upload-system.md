# Library 书籍上传系统详解

## 上传系统架构

Library 的上传系统支持多种书籍格式，包括 EPUB、PDF、MOBI、CBZ、FB2、FBZ 等。系统采用前端预处理+后端持久化的架构。

## 核心组件

### 1. Upload 组件

```typescript
export default function Upload() {
  const { isDragOver, isUploading, handleDragOver, handleDragLeave, handleDrop, handleFileSelect, triggerFileSelect } = useBookUpload();

  return (
    <div className="flex h-full w-full flex-col items-center justify-center p-8">
      <div
        className={`relative flex w-full max-w-150 cursor-pointer items-center justify-center rounded-2xl border-2 border-dashed transition-all duration-200 ${
          isDragOver ? "border-blue-400 bg-blue-50" : "border-neutral-300 bg-gradient-to-br"
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          type="file"
          multiple
          accept={FILE_ACCEPT_FORMATS}
          className="absolute inset-0 z-10 h-full w-full cursor-pointer opacity-0"
          onChange={handleFileSelect}
        />
        {/* UI 内容 */}
      </div>
    </div>
  );
}
```

### 2. useBookUpload Hook

```typescript
export const useBookUpload = () => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  const handleFileSelect = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      await processFiles(Array.from(files));
    }
  }, []);

  const handleDrop = useCallback(async (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(event.dataTransfer.files);
    await processFiles(files);
  }, []);

  return {
    isDragOver,
    isUploading,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    handleFileSelect,
    triggerFileSelect,
  };
};
```

## 文件处理流程

### 1. 格式验证

```typescript
function getBookFormat(filename: string): string {
  const ext = filename.toLowerCase().split('.').pop();
  const formatMap: { [key: string]: string } = {
    'epub': 'EPUB',
    'pdf': 'PDF',
    'mobi': 'MOBI',
    'azw3': 'MOBI',
    'cbz': 'CBZ',
    'fb2': 'FB2',
    'fbz': 'FBZ',
  };
  
  return formatMap[ext || ''] || 'UNKNOWN';
}
```

### 2. 元数据提取

```typescript
async function extractMetadataOnly(file: File): Promise<any> {
  try {
    if (file.name.toLowerCase().endsWith(".epub")) {
      const arrayBuffer = await file.arrayBuffer();
      const bookDoc = await parseEpubFile(arrayBuffer, file.name);
      return bookDoc.metadata;
    }

    return {
      title: getFileNameWithoutExt(file.name),
      author: "Unknown",
      language: "en",
    };
  } catch (error) {
    console.warn("元数据提取失败，使用默认值:", error);
    return {
      title: getFileNameWithoutExt(file.name),
      author: "Unknown", 
      language: "en",
    };
  }
}
```

### 3. 封面提取

```typescript
// EPUB 格式封面提取
if (format === "EPUB") {
  try {
    const bookDoc = await parseEpubFile(fileData, file.name);
    const coverBlob = await bookDoc.getCover();
    if (coverBlob) {
      const coverTempFileName = `cover_${bookHash}.jpg`;
      const coverTempPath = await join(tempDirPath, coverTempFileName);
      const coverArrayBuffer = await coverBlob.arrayBuffer();
      await writeFile(coverTempPath, new Uint8Array(coverArrayBuffer));
      coverTempFilePath = coverTempPath;
    }
  } catch (e) {
    console.warn("无法提取封面:", e);
  }
}
```

## 错误处理

### 1. 文件验证错误

```typescript
export async function uploadBook(file: File): Promise<SimpleBook> {
  try {
    const format = getBookFormat(file.name);
    if (!["EPUB", "PDF", "MOBI", "CBZ", "FB2", "FBZ"].includes(format)) {
      throw new Error(`不支持的文件格式: ${format}`);
    }
    // 处理逻辑
  } catch (error) {
    console.error("书籍上传失败:", error);
    throw new Error(`上传失败: ${error instanceof Error ? error.message : "未知错误"}`);
  }
}
```

### 2. 重复文件检测

```typescript
// 计算文件哈希
const bookHash = await partialMD5(file);

// 检查是否已存在
const existingBook = await getBookById(bookHash);
if (existingBook) {
  return await updateBook(bookHash, {});
}
```

这个上传系统提供了完整的文件处理流程，支持多种格式，具有良好的错误处理和用户反馈机制。 