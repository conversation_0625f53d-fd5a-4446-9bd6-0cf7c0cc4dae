# Library 标签系统设计分析

## 标签系统架构概览

Library 的标签系统是一个多层次的分类管理系统，支持手动标签创建、AI 自动标签建议、批量标签操作等功能。系统采用数据库存储标签定义，书籍关联标签ID的方式实现灵活的标签管理。

## 核心数据模型

### 1. 标签数据结构

```typescript
// 数据库标签实体
interface Tag {
  id: string;          // 唯一标识
  name: string;        // 标签名称
  color: string;       // 标签颜色
  createdAt: string;   // 创建时间
  updatedAt: string;   // 更新时间
}

// 前端展示用的标签数据(带统计信息)
interface BookTag {
  id: string;          // 标签ID或特殊标识
  name: string;        // 显示名称
  count: number;       // 关联的书籍数量
  color: string;       // 显示颜色
}

// 书籍标签关联(存储在书籍数据中)
interface BookWithStatus {
  // ... 其他字段
  tags?: string[];     // 关联的标签ID数组
}
```

### 2. 特殊标签类型

系统内置了几种特殊标签：

```typescript
// 特殊标签ID定义
const SPECIAL_TAGS = {
  ALL: "all",                    // 全部书籍
  UNCATEGORIZED: "uncategorized" // 未分类书籍
} as const;

// 标签ID格式
// - "all": 全部书籍
// - "uncategorized": 未分类书籍  
// - "tag-{name}": 用户创建的标签
```

## 标签数据生成和管理

### 1. 标签统计生成

```typescript
const generateTagsFromDatabase = async (books: BookWithStatusAndUrls[]): Promise<BookTag[]> => {
  // 统计未分类书籍数量
  const untaggedCount = books.filter((book) => !book.tags || book.tags.length === 0).length;
  
  console.log("Books analysis:", {
    totalBooks: books.length,
    untaggedCount,
    booksWithTags: books.filter((book) => book.tags && book.tags.length > 0).length,
    sampleBooks: books.slice(0, 3).map((book) => ({ title: book.title, tags: book.tags })),
  });

  // 获取数据库中的所有标签定义
  let databaseTags: Tag[] = [];
  try {
    databaseTags = await getTags();
  } catch (error) {
    console.warn("Failed to fetch tags from database:", error);
  }

  // 构建标签列表
  const tags: BookTag[] = [
    { id: "all", name: "全部", count: books.length, color: "#6b7280" }
  ];

  // 如果有未分类的书籍，添加"未分类"标签
  if (untaggedCount > 0) {
    tags.push({
      id: "uncategorized",
      name: "未分类",
      count: untaggedCount,
      color: "#6b7280",
    });
  }

  // 为每个数据库标签计算关联的书籍数量
  const bookTags = databaseTags.map((tag) => {
    const count = books.filter((book) => book.tags?.includes(tag.id)).length;
    return {
      id: `tag-${tag.name}`,  // 前端显示用的ID格式
      name: tag.name,
      count,
      color: tag.color || "#6b7280",
    };
  });

  // 按书籍数量排序，空标签排在最后
  const sortedTags = bookTags.sort((a, b) => {
    if (a.count === 0 && b.count === 0) return a.name.localeCompare(b.name);
    if (a.count === 0) return 1;
    if (b.count === 0) return -1;
    return b.count - a.count;
  });

  tags.push(...sortedTags);
  return tags;
};
```

### 2. 标签过滤逻辑

```typescript
export const useTagsManagement = (booksWithStatus: BookWithStatusAndUrls[]) => {
  const [selectedTag, setSelectedTag] = useState<string>("all");
  const [tags, setTags] = useState<BookTag[]>([]);
  const [databaseTags, setDatabaseTags] = useState<Tag[]>([]);

  // 根据选中的标签过滤书籍
  const filteredBooksByTag = useMemo(() => {
    if (selectedTag === "all") {
      return booksWithStatus;
    }

    if (selectedTag === "uncategorized") {
      return booksWithStatus.filter((book) => !book.tags || book.tags.length === 0);
    }

    if (selectedTag.startsWith("tag-")) {
      const tagName = selectedTag.replace("tag-", "");
      // 找到对应的数据库标签ID
      const tagObj = databaseTags.find((tag) => tag.name === tagName);
      if (tagObj) {
        return booksWithStatus.filter((book) => book.tags?.includes(tagObj.id));
      }
      return [];
    }

    return booksWithStatus;
  }, [booksWithStatus, selectedTag, databaseTags]);

  // 自动切换到"全部"当当前标签无书籍时
  useEffect(() => {
    if (selectedTag !== "all" && filteredBooksByTag.length === 0 && booksWithStatus.length > 0) {
      console.log(`Current tag view "${selectedTag}" has no books, switching to "all"`);
      setSelectedTag("all");
    }
  }, [filteredBooksByTag, booksWithStatus, selectedTag]);

  return {
    selectedTag,
    tags,
    filteredBooksByTag,
    handleTagSelect,
  };
};
```

## 标签UI组件设计

### 1. TagList 组件

```typescript
interface TagListProps {
  tags: BookTag[];
  selectedTag: string;
  selectedTagsForDelete: string[];
  handleTagClick: (tagId: string, event: React.MouseEvent) => void;
  handleTagContextMenu: (e: React.MouseEvent, tag: BookTag) => void;
  handleNewTagClick: () => void;
  books: BookWithStatusAndUrls[];
  onBookUpdate: (bookId: string, updates: { tags?: string[] }) => Promise<boolean>;
  onRefresh: () => Promise<void>;
}

export default function TagList({
  tags,
  selectedTag,
  selectedTagsForDelete,
  handleTagClick,
  handleTagContextMenu,
  handleNewTagClick,
}: TagListProps) {
  // 滚动状态检测
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showTopShadow, setShowTopShadow] = useState(false);
  const [showBottomShadow, setShowBottomShadow] = useState(false);

  const checkScrollState = useCallback(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const { scrollTop, scrollHeight, clientHeight } = container;
    const tolerance = 1;

    setShowTopShadow(scrollTop > tolerance);
    setShowBottomShadow(scrollTop <= tolerance && scrollHeight > clientHeight);
  }, []);

  return (
    <div className="relative">
      {/* 滚动阴影效果 */}
      {showTopShadow && (
        <div className="absolute top-0 left-0 right-0 h-4 bg-gradient-to-b from-neutral-50 to-transparent pointer-events-none z-10" />
      )}
      
      <div 
        ref={scrollContainerRef}
        className="max-h-60 overflow-y-auto px-2"
        onScroll={checkScrollState}
      >
        {tags.map((tag) => (
          <TagItem
            key={tag.id}
            tag={tag}
            isSelected={selectedTag === tag.id}
            isSelectedForDelete={selectedTagsForDelete.includes(tag.id)}
            onClick={(e) => handleTagClick(tag.id, e)}
            onContextMenu={(e) => handleTagContextMenu(e, tag)}
          />
        ))}
        
        {/* 新建标签按钮 */}
        <button
          onClick={handleNewTagClick}
          className="flex w-full items-center gap-2 rounded-sm p-1 text-sm transition-colors hover:bg-neutral-100 dark:hover:bg-neutral-700/50"
        >
          <Plus size={14} className="flex-shrink-0 text-neutral-500" />
          <span className="text-neutral-600 dark:text-neutral-400">新建标签</span>
        </button>
      </div>
      
      {showBottomShadow && (
        <div className="absolute bottom-0 left-0 right-0 h-4 bg-gradient-to-t from-neutral-50 to-transparent pointer-events-none" />
      )}
    </div>
  );
}
```

### 2. TagItem 组件

```typescript
interface TagItemProps {
  tag: BookTag;
  isSelected: boolean;
  isSelectedForDelete: boolean;
  onClick: (e: React.MouseEvent) => void;
  onContextMenu: (e: React.MouseEvent) => void;
}

const TagItem: React.FC<TagItemProps> = ({ 
  tag, 
  isSelected, 
  isSelectedForDelete, 
  onClick, 
  onContextMenu 
}) => {
  return (
    <div
      className={clsx(
        "flex w-full items-center justify-between rounded-sm p-1 cursor-pointer transition-colors group",
        isSelected 
          ? "bg-neutral-200 dark:bg-neutral-600 text-neutral-900 dark:text-neutral-100"
          : "hover:bg-neutral-100 dark:hover:bg-neutral-700/50 text-neutral-700 dark:text-neutral-300",
        isSelectedForDelete && "ring-2 ring-red-300 bg-red-50"
      )}
      onClick={onClick}
      onContextMenu={onContextMenu}
    >
      <div className="flex items-center gap-2 min-w-0 flex-1">
        {/* 标签颜色指示器 */}
        <div 
          className="w-2 h-2 rounded-full flex-shrink-0"
          style={{ backgroundColor: tag.color }}
        />
        
        {/* 标签名称 */}
        <span className="text-sm truncate">{tag.name}</span>
      </div>
      
      {/* 书籍数量 */}
      <span 
        className={clsx(
          "text-xs rounded-full px-1.5 py-0.5 min-w-[1.5rem] text-center",
          isSelected 
            ? "bg-neutral-300 text-neutral-700 dark:bg-neutral-500 dark:text-neutral-200"
            : "bg-neutral-200 text-neutral-600 dark:bg-neutral-600 dark:text-neutral-300"
        )}
      >
        {tag.count}
      </span>
    </div>
  );
};
```

## 标签操作系统

### 1. 标签创建

```typescript
// CreateTagDialog 组件
interface CreateTagDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateTag: (name: string, color: string) => Promise<void>;
}

export default function CreateTagDialog({ isOpen, onClose, onCreateTag }: CreateTagDialogProps) {
  const [tagName, setTagName] = useState("");
  const [tagColor, setTagColor] = useState("#6b7280");
  const [isCreating, setIsCreating] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!tagName.trim()) return;

    try {
      setIsCreating(true);
      await onCreateTag(tagName.trim(), tagColor);
      toast.success("标签创建成功");
      onClose();
    } catch (error) {
      toast.error("标签创建失败");
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>创建新标签</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="text-sm font-medium">标签名称</label>
            <Input
              value={tagName}
              onChange={(e) => setTagName(e.target.value)}
              placeholder="输入标签名称"
              maxLength={20}
            />
          </div>
          
          <div>
            <label className="text-sm font-medium">标签颜色</label>
            <ColorPicker
              value={tagColor}
              onChange={setTagColor}
            />
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              取消
            </Button>
            <Button type="submit" disabled={!tagName.trim() || isCreating}>
              {isCreating ? "创建中..." : "创建"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
```

### 2. 标签删除操作

```typescript
export const useTagsOperations = ({
  booksWithStatus,
  handleBookUpdate,
  refreshBooks,
  selectedTag,
  handleTagSelect,
  selectedTagsForDelete = [],
  tags,
  clearSelectedTags,
}: UseTagsOperationsProps) => {
  
  // 单个标签删除
  const handleDeleteTag = useCallback(
    async (tag: BookTag) => {
      if (tag.id === "all" || tag.id === "uncategorized") {
        return; // 不允许删除特殊标签
      }

      const tagName = tag.id.startsWith("tag-") ? tag.id.replace("tag-", "") : tag.name;

      try {
        const confirmed = await ask(
          `确定要删除标签"${tagName}"吗？\n\n这将从所有书籍中移除此标签，并删除独立存储的标签。`,
          {
            title: "确认删除标签",
            kind: "warning",
          },
        );

        if (confirmed) {
          // 获取要删除的标签的真实ID
          const tagToDelete = await getTagByName(tagName);
          const tagId = tagToDelete?.id;

          if (tagId) {
            // 从所有关联书籍中移除该标签
            const booksToUpdate = booksWithStatus.filter((book) => book.tags?.includes(tagId));

            for (const book of booksToUpdate) {
              const newTags = book.tags!.filter((t) => t !== tagId);
              await handleBookUpdate(book.id, { tags: newTags });
            }
          }

          // 删除标签数据库记录
          try {
            const independentTag = await getTagByName(tagName);
            if (independentTag) {
              await deleteTag(independentTag.id);
            }
          } catch (error) {
            console.warn("Failed to delete independent tag:", error);
          }

          // 刷新数据
          await refreshBooks();

          // 如果当前选中的是被删除的标签，切换到"全部"
          if (selectedTag === tag.id) {
            handleTagSelect("all");
          }
        }
      } catch (error) {
        console.error("Failed to delete tag:", error);
      }
    },
    [booksWithStatus, handleBookUpdate, refreshBooks, selectedTag, handleTagSelect],
  );

  // 批量删除标签
  const handleBatchDeleteTags = useCallback(async () => {
    if (selectedTagsForDelete.length === 0) return;

    try {
      const tagNames = selectedTagsForDelete.map((tagId) => {
        const tag = tags.find((t) => t.id === tagId);
        return tag ? tag.name : tagId;
      });

      const confirmed = await ask(
        `确定要删除以下 ${selectedTagsForDelete.length} 个标签吗？\n\n${tagNames.join(", ")}\n\n这将从所有书籍中移除这些标签，并删除独立存储的标签。`,
        {
          title: "确认批量删除标签",
          kind: "warning",
        },
      );

      if (confirmed) {
        for (const tagId of selectedTagsForDelete) {
          try {
            const tag = tags.find((t) => t.id === tagId);
            if (!tag) continue;

            const tagName = tag.id.startsWith("tag-") ? tag.id.replace("tag-", "") : tag.name;
            const tagToDelete = await getTagByName(tagName);
            const realTagId = tagToDelete?.id;

            if (realTagId) {
              // 从书籍中移除标签
              const booksToUpdate = booksWithStatus.filter((book) => book.tags?.includes(realTagId));
              for (const book of booksToUpdate) {
                const newTags = book.tags!.filter((t) => t !== realTagId);
                await handleBookUpdate(book.id, { tags: newTags });
              }
            }

            // 删除标签记录
            const independentTag = await getTagByName(tagName);
            if (independentTag) {
              await deleteTag(independentTag.id);
            }
          } catch (error) {
            console.error(`Failed to delete tag ${tagId}:`, error);
          }
        }

        await refreshBooks();

        if (selectedTagsForDelete.includes(selectedTag)) {
          handleTagSelect("all");
        }

        if (clearSelectedTags) {
          clearSelectedTags();
        }
      }
    } catch (error) {
      console.error("Failed to batch delete tags:", error);
    }
  }, [selectedTagsForDelete, tags, booksWithStatus, handleBookUpdate, refreshBooks, selectedTag, handleTagSelect, clearSelectedTags]);

  return {
    handleDeleteTag,
    handleBatchDeleteTags,
    // ...其他方法
  };
};
```

### 3. 右键菜单系统

```typescript
const handleTagContextMenu = useCallback(
  async (e: React.MouseEvent, tag: BookTag) => {
    e.preventDefault();
    e.stopPropagation();

    // 如果有多选标签，显示批量删除菜单
    if (selectedTagsForDelete.length > 0) {
      const canDeleteAll = selectedTagsForDelete.every((tagId) => 
        tagId !== "all" && tagId !== "uncategorized"
      );

      if (!canDeleteAll) return;

      try {
        const menu = await Menu.new({
          items: [
            {
              id: "batch-delete-tags",
              text: `删除 ${selectedTagsForDelete.length} 个标签`,
              action: () => {
                if (handleBatchDeleteTags) {
                  handleBatchDeleteTags();
                }
              },
            },
          ],
        });

        await menu.popup(new LogicalPosition(e.clientX, e.clientY));
      } catch (error) {
        console.error("Failed to show batch delete menu:", error);
      }
      return;
    }

    // 单个标签的右键菜单
    if (tag.id === "all" || tag.id === "uncategorized") {
      return; // 特殊标签不显示菜单
    }

    try {
      const menu = await Menu.new({
        items: [
          {
            id: "edit-tag",
            text: "管理书籍",
            action: () => handleEditTag(tag),
          },
          {
            id: "delete-tag",
            text: "删除标签",
            action: () => handleDeleteTag(tag),
          },
        ],
      });

      await menu.popup(new LogicalPosition(e.clientX, e.clientY));
    } catch (error) {
      console.error("Failed to show tag context menu:", error);
    }
  },
  [handleEditTag, handleDeleteTag, selectedTagsForDelete, handleBatchDeleteTags],
);
```

## 书籍标签关联管理

### 1. 标签分配界面

```typescript
// TagSelector 组件用于书籍编辑时选择标签
interface TagSelectorProps {
  selectedTags: string[];
  availableTags: BookTag[];
  onChange: (tags: string[]) => void;
}

const TagSelector: React.FC<TagSelectorProps> = ({ selectedTags, availableTags, onChange }) => {
  const [searchQuery, setSearchQuery] = useState("");
  
  const filteredTags = useMemo(() => {
    return availableTags.filter(tag =>
      tag.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
      tag.id !== "all" && tag.id !== "uncategorized"
    );
  }, [availableTags, searchQuery]);

  const toggleTag = (tagId: string) => {
    const realTagId = tagId.startsWith("tag-") ? tagId.replace("tag-", "") : tagId;
    
    if (selectedTags.includes(realTagId)) {
      onChange(selectedTags.filter(id => id !== realTagId));
    } else {
      onChange([...selectedTags, realTagId]);
    }
  };

  return (
    <div className="space-y-3">
      <div>
        <label className="text-sm font-medium">标签</label>
        <Input
          placeholder="搜索标签..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>
      
      <div className="max-h-40 overflow-y-auto space-y-1">
        {filteredTags.map((tag) => {
          const realTagId = tag.id.startsWith("tag-") ? tag.id.replace("tag-", "") : tag.id;
          const isSelected = selectedTags.includes(realTagId);
          
          return (
            <div
              key={tag.id}
              className={clsx(
                "flex items-center justify-between p-2 rounded cursor-pointer",
                isSelected ? "bg-blue-100 dark:bg-blue-900" : "hover:bg-neutral-100 dark:hover:bg-neutral-700"
              )}
              onClick={() => toggleTag(tag.id)}
            >
              <div className="flex items-center gap-2">
                <div 
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: tag.color }}
                />
                <span className="text-sm">{tag.name}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <span className="text-xs text-neutral-500">({tag.count})</span>
                {isSelected && <Check size={16} className="text-blue-600" />}
              </div>
            </div>
          );
        })}
      </div>
      
      {/* 显示已选标签 */}
      {selectedTags.length > 0 && (
        <div className="space-y-2">
          <label className="text-sm font-medium">已选标签:</label>
          <div className="flex flex-wrap gap-1">
            {selectedTags.map((tagId) => {
              const tag = availableTags.find(t => 
                t.id === `tag-${tagId}` || t.id === tagId
              );
              
              return (
                <Badge 
                  key={tagId}
                  variant="secondary"
                  className="text-xs"
                >
                  {tag?.name || tagId}
                  <button
                    onClick={() => onChange(selectedTags.filter(id => id !== tagId))}
                    className="ml-1 hover:text-red-600"
                  >
                    ×
                  </button>
                </Badge>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};
```

### 2. 批量标签操作

```typescript
// 批量为书籍添加标签
const handleBatchAddTags = async (bookIds: string[], tagIds: string[]) => {
  try {
    for (const bookId of bookIds) {
      const book = booksWithStatus.find(b => b.id === bookId);
      if (book) {
        const currentTags = book.tags || [];
        const newTags = [...new Set([...currentTags, ...tagIds])]; // 去重
        await handleBookUpdate(bookId, { tags: newTags });
      }
    }
    
    await refreshBooks();
    toast.success(`已为 ${bookIds.length} 本书添加标签`);
  } catch (error) {
    toast.error("批量添加标签失败");
  }
};

// 批量移除书籍标签
const handleBatchRemoveTags = async (bookIds: string[], tagIds: string[]) => {
  try {
    for (const bookId of bookIds) {
      const book = booksWithStatus.find(b => b.id === bookId);
      if (book) {
        const currentTags = book.tags || [];
        const newTags = currentTags.filter(tagId => !tagIds.includes(tagId));
        await handleBookUpdate(bookId, { tags: newTags });
      }
    }
    
    await refreshBooks();
    toast.success(`已从 ${bookIds.length} 本书移除标签`);
  } catch (error) {
    toast.error("批量移除标签失败");
  }
};
```

这个标签系统提供了完整的标签生命周期管理，从创建到删除、从单个操作到批量操作，都有详细的处理逻辑和用户反馈机制，确保了标签系统的易用性和数据一致性。 