# Library AI 集成功能详解

## AI 功能概述

Library 组件集成了多种 AI 功能，主要包括智能标签生成、书籍内容向量化和智能搜索等功能。

## 核心 AI 功能

### 1. AI 标签生成

#### AITagService

```typescript
export interface AITagSuggestion {
  name: string;
  reason: string;
  isExisting: boolean;
  existingTagId?: string;
}

export async function generateTagsWithAI(
  book: SimpleBook,
  existingTags: Tag[],
  selectedModel?: { providerId: string; modelId: string },
): Promise<AITagResponse> {
  // 获取模型配置
  let modelConfig = selectedModel;
  if (!modelConfig) {
    const { selectedModel: storeModel } = useModelProviderStore.getState();
    modelConfig = {
      providerId: storeModel.providerId,
      modelId: storeModel.modelId,
    };
  }

  // 创建模型实例
  const modelInstance = createModelInstance(modelConfig.providerId, modelConfig.modelId);

  // 构建提示词
  const prompt = `作为图书标签分类专家，请为以下书籍生成合适的标签建议：
  
书籍信息：
- 标题：${book.title}
- 作者：${book.author}
- 格式：${book.format}

现有标签：
${existingTags.map(tag => `- ${tag.name}`).join('\n')}

请生成2-3个合适的标签，优先使用现有标签，避免重复创建相似标签。`;

  // 调用 AI 模型
  const { text } = await generateText({
    model: modelInstance,
    prompt,
  });

  return parseAIResponse(text, existingTags);
}
```

#### AI 标签确认对话框

```typescript
export default function AITagConfirmDialog({ suggestions, onConfirm, onClose }: Props) {
  const [selectedSuggestions, setSelectedSuggestions] = useState<string[]>([]);

  const handleConfirm = async () => {
    const confirmedTags = suggestions
      .filter(s => selectedSuggestions.includes(s.name))
      .map(s => ({
        name: s.name,
        isExisting: s.isExisting,
        existingTagId: s.existingTagId,
      }));

    await onConfirm(confirmedTags);
    onClose();
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>AI 标签建议</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-3">
          {suggestions.map((suggestion) => (
            <div key={suggestion.name} className="flex items-start space-x-3">
              <Checkbox
                checked={selectedSuggestions.includes(suggestion.name)}
                onCheckedChange={(checked) => {
                  if (checked) {
                    setSelectedSuggestions([...selectedSuggestions, suggestion.name]);
                  } else {
                    setSelectedSuggestions(selectedSuggestions.filter(s => s !== suggestion.name));
                  }
                }}
              />
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium">{suggestion.name}</span>
                  {suggestion.isExisting && (
                    <Badge variant="secondary">现有标签</Badge>
                  )}
                </div>
                <p className="text-sm text-muted-foreground">{suggestion.reason}</p>
              </div>
            </div>
          ))}
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>取消</Button>
          <Button onClick={handleConfirm}>确认添加</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
```

### 2. 书籍向量化系统

#### EmbeddingDialog 组件

```typescript
export default function EmbeddingDialog({ isOpen, onClose, bookId }: Props) {
  const [loading, setLoading] = useState(false);
  const [query, setQuery] = useState("");
  const [searchResults, setSearchResults] = useState<SearchItem[]>([]);

  // 向量搜索
  const doVectorSearch = useCallback(async () => {
    if (!query.trim()) return;
    setLoading(true);
    
    try {
      const results = await invoke<SearchItem[]>("vector_search", {
        bookId,
        query: query.trim(),
        limit: 10,
      });
      setSearchResults(results);
    } catch (error) {
      console.error("Vector search failed:", error);
    } finally {
      setLoading(false);
    }
  }, [bookId, query]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl">
        <Tabs defaultValue="search">
          <TabsList>
            <TabsTrigger value="search">向量搜索</TabsTrigger>
            <TabsTrigger value="context">上下文检索</TabsTrigger>
            <TabsTrigger value="toc">章节内容</TabsTrigger>
            <TabsTrigger value="range">范围检索</TabsTrigger>
          </TabsList>
          
          <TabsContent value="search">
            <div className="space-y-4">
              <div className="flex gap-2">
                <Input
                  placeholder="输入搜索关键词..."
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                />
                <Button onClick={doVectorSearch} disabled={loading}>
                  {loading ? "搜索中..." : "搜索"}
                </Button>
              </div>
              
              <div className="space-y-2">
                {searchResults.map((result, index) => (
                  <div key={index} className="p-3 border rounded">
                    <div className="font-medium">{result.chapter_title}</div>
                    <p className="text-sm text-muted-foreground">{result.content}</p>
                    <div className="text-xs text-muted-foreground">
                      相似度: {(result.similarity * 100).toFixed(1)}%
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
```

### 3. 向量化进度监听

```typescript
// 在 BookItem 组件中监听向量化进度
useEffect(() => {
  let unlisten: (() => void) | null = null;
  
  (async () => {
    const off = await listen<{
      book_id: string;
      current: number;
      total: number;
      percent: number;
      chapter_title: string;
      chunk_index: number;
    }>("epub://index-progress", (e) => {
      const p = e.payload;
      if (p && p.book_id === book.id) {
        setVectorizeProgress(Math.max(0, Math.min(100, Math.round(p.percent))));
      }
    });
    unlisten = off;
  })();
  
  return () => {
    if (unlisten) unlisten();
  };
}, [book.id]);
```

## AI 模型管理

### 1. 模型选择器

```typescript
export const useModelSelector = () => {
  const { selectedModel, providers } = useModelProviderStore();
  
  const selectModel = useCallback((providerId: string, modelId: string) => {
    useModelProviderStore.getState().setSelectedModel({
      providerId,
      modelId,
    });
  }, []);

  return {
    selectedModel,
    providers,
    selectModel,
  };
};
```

### 2. AI 提示词优化

```typescript
const buildTagGenerationPrompt = (book: SimpleBook, existingTags: Tag[]) => {
  const existingTagsText = existingTags.length > 0 
    ? existingTags.map(tag => `- ${tag.name}`).join('\n') 
    : "无现有标签";

  return `作为一个图书标签分类专家，请为以下书籍生成合适的标签建议：

书籍信息：
- 标题：${book.title || "未知标题"}
- 作者：${book.author || "未知作者"}
- 格式：${book.format || "未知格式"}
- 语言：${book.language || "未知语言"}

现有标签：
${existingTagsText}

标签生成原则：
1. 优先使用现有标签
2. 避免碎片化
3. 多样化分类
4. 标签名称简洁
5. 实用性优先

请按格式回复：标签名称|选择此标签的原因`;
};
```

这些 AI 功能为 Library 组件提供了智能化的书籍管理能力，大大提升了用户体验和管理效率。 