# Library 组件架构设计

## 整体架构概览

Library 组件采用分层模块化架构，主要分为以下层次：

```
index.tsx (主页面)
├── Sidebar (导航侧边栏)
│   ├── SearchToggle (搜索切换)
│   └── TagList (标签列表)
├── Upload (书籍上传界面)
├── BookItem (书籍项组件)
│   ├── EmbeddingDialog (向量化对话框)
│   ├── EditInfo (信息编辑)
│   └── AITagConfirmDialog (AI标签确认)
└── ViewMenu (视图菜单)
```

## 核心组件详解

### 1. 主页面 - index.tsx

**职责**：
- 应用初始化和设置加载
- 协调各个子组件
- 处理全局状态和事件

**核心功能**：
```typescript
export default function NewLibraryPage() {
  const { searchQuery, booksWithStatus, isLoading, refreshBooks, setSearchQuery } = useLibraryStore();
  const { selectedTag, tags, filteredBooksByTag, handleTagSelect } = useTagsManagement(booksWithStatus);
  const { filteredBooks } = useBooksFilter(filteredBooksByTag, searchQuery);
  const { handleBookDelete, handleBookUpdate } = useBooksOperations(refreshBooks);

  // 初始化逻辑
  useEffect(() => {
    const initLibrary = async () => {
      try {
        const appService = await envConfig.getAppService();
        const settings = await appService.loadSettings();
        setSettings(settings);
        await refreshBooks();
        setLibraryLoaded(true);
      } catch (error) {
        console.error("Error initializing library:", error);
      }
    };
    initLibrary();
  }, []);

  return (
    <div className="flex h-screen bg-neutral-50 dark:bg-neutral-900">
      <Sidebar {...sidebarProps} />
      <main className="flex-1">
        {filteredBooks.length === 0 ? <Upload /> : <BookGrid />}
      </main>
    </div>
  );
}
```

**关键特性**：
- 使用多个自定义 Hooks 分离关注点
- 条件渲染：无书籍时显示上传界面
- 响应式设计适配不同屏幕尺寸

### 2. 状态管理架构

#### Hook 组合模式

Library 页面采用 Hook 组合模式，将复杂的状态逻辑分解为独立的自定义 Hooks：

**useBooksFilter**: 书籍过滤逻辑
```typescript
export const useBooksFilter = (filteredBooksByTag: BookWithStatusAndUrls[], searchQuery: string) => {
  const filteredBooks = useMemo(() => {
    if (!searchQuery.trim()) return filteredBooksByTag;
    
    const query = searchQuery.toLowerCase().trim();
    return filteredBooksByTag.filter((book: BookWithStatusAndUrls) => {
      if (book.title.toLowerCase().includes(query)) return true;
      if (book.author.toLowerCase().includes(query)) return true;
      if (book.tags?.some((tag) => tag.toLowerCase().includes(query))) return true;
      return false;
    });
  }, [filteredBooksByTag, searchQuery]);

  return { filteredBooks };
};
```

**useTagsManagement**: 标签管理逻辑
```typescript
export const useTagsManagement = (booksWithStatus: BookWithStatusAndUrls[]) => {
  const [selectedTag, setSelectedTag] = useState<string>("all");
  const [tags, setTags] = useState<BookTag[]>([]);

  // 从数据库获取标签并计算书籍数量
  useEffect(() => {
    const loadTags = async () => {
      const dbTags = await getTags();
      const generatedTags = await generateTagsFromDatabase(booksWithStatus);
      setTags(generatedTags);
    };
    loadTags();
  }, [booksWithStatus]);

  // 根据选中标签过滤书籍
  const filteredBooksByTag = useMemo(() => {
    if (selectedTag === "all") return booksWithStatus;
    if (selectedTag === "uncategorized") {
      return booksWithStatus.filter(book => !book.tags || book.tags.length === 0);
    }
    // 处理具体标签过滤逻辑
  }, [booksWithStatus, selectedTag]);

  return { selectedTag, tags, filteredBooksByTag, handleTagSelect };
};
```

**useBooksOperations**: 书籍操作逻辑
```typescript
export const useBooksOperations = (refreshBooks: () => Promise<void>) => {
  const handleBookDelete = useCallback(async (book: BookWithStatusAndUrls) => {
    try {
      const { deleteBook } = await import("@/services/bookService");
      await deleteBook(book.id);
      await refreshBooks();
      return true;
    } catch (error) {
      console.error("Failed to delete book:", error);
      return false;
    }
  }, [refreshBooks]);

  const handleBookUpdate = useCallback(async (bookId: string, updateData) => {
    try {
      const { updateBook } = await import("@/services/bookService");
      await updateBook(bookId, updateData);
      await refreshBooks();
      return true;
    } catch (error) {
      console.error("Failed to update book:", error);
      return false;
    }
  }, [refreshBooks]);

  return { handleBookDelete, handleBookUpdate };
};
```

### 3. 数据流架构

#### 数据流向图

```
Tauri Backend (Rust)
         ↓
   bookService.ts
         ↓
   libraryStore.ts (Zustand)
         ↓
   useTagsManagement Hook
         ↓
   useBooksFilter Hook
         ↓
   UI Components
```

#### 关键数据转换

**书籍数据处理流程**：
```typescript
// 1. 从后端获取原始数据
const libraryBooks = await getBooksWithStatus();

// 2. 转换文件路径为可访问的URL
async function convertBookWithStatusUrls(book: BookWithStatus): Promise<BookWithStatusAndUrls> {
  const appDataDirPath = await appDataDir();
  const absoluteFilePath = book.filePath.startsWith("/") 
    ? book.filePath 
    : `${appDataDirPath}/${book.filePath}`;

  const fileUrl = convertFileSrc(absoluteFilePath);
  const coverUrl = book.coverPath ? convertFileSrc(absoluteCoverPath) : undefined;

  return { ...book, fileUrl, coverUrl };
}

// 3. 批量转换并存储到状态
const booksWithUrls = await Promise.all(libraryBooks.map(convertBookWithStatusUrls));
set({ booksWithStatus: booksWithUrls });
```

### 4. 组件通信模式

#### Props 传递模式

Library 采用明确的 Props 传递模式，避免过深的组件层次：

```typescript
interface SidebarProps {
  searchQuery: string;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  tags: BookTag[];
  selectedTag: string;
  handleTagClick: (tagId: string, event: React.MouseEvent) => void;
  handleTagContextMenu: (e: React.MouseEvent, tag: BookTag) => void;
  // 其他 props...
}

// 主页面中的 props 组装
const sidebarProps: SidebarProps = {
  searchQuery,
  onSearchChange: (e) => setSearchQuery(e.target.value),
  tags,
  selectedTag,
  selectedTagsForDelete,
  isLibraryExpanded,
  toggleLibraryExpanded,
  handleTagClick,
  handleTagContextMenu,
  handleNewTagClick,
  triggerFileSelect,
  toggleSettingsDialog,
  books: booksWithStatus,
  onBookUpdate: handleBookUpdate,
  onRefresh: refreshBooks,
};
```

#### 事件处理模式

使用回调函数模式处理用户交互：

```typescript
// 标签点击处理
const handleTagClick = useCallback((tagId: string, event: React.MouseEvent) => {
  // 支持多选模式
  if (event.ctrlKey || event.metaKey) {
    handleMultiSelect(tagId);
  } else {
    handleTagSelect(tagId, () => setSearchQuery(""));
  }
}, [handleTagSelect, setSearchQuery]);

// 右键菜单处理
const handleTagContextMenu = useCallback(async (e: React.MouseEvent, tag: BookTag) => {
  e.preventDefault();
  e.stopPropagation();
  
  const menu = await Menu.new({
    items: [
      {
        id: "edit-tag",
        text: "管理书籍",
        action: () => handleEditTag(tag),
      },
      {
        id: "delete-tag", 
        text: "删除标签",
        action: () => handleDeleteTag(tag),
      },
    ],
  });
  
  await menu.popup(new LogicalPosition(e.clientX, e.clientY));
}, [handleEditTag, handleDeleteTag]);
```

### 5. 性能优化架构

#### 惰性加载策略

```typescript
// 动态导入减少初始包大小
const handleBookDelete = useCallback(async (book: BookWithStatusAndUrls) => {
  const { deleteBook } = await import("@/services/bookService");
  await deleteBook(book.id);
}, []);

// 条件组件渲染
{showEditDialog && (
  <EditInfo
    book={book}
    availableTags={availableTags}
    onClose={() => setShowEditDialog(false)}
    onUpdate={onUpdate}
  />
)}
```

#### 记忆化优化

```typescript
// useMemo 优化计算密集型操作
const filteredBooks = useMemo(() => {
  // 复杂的过滤逻辑
}, [filteredBooksByTag, searchQuery]);

// useCallback 优化事件处理函数
const handleClick = useCallback(() => {
  openReader([book.id], book.title);
}, [book.id, book.title, openReader]);
```

#### 虚拟化支持

对于大量书籍的情况，组件支持虚拟化渲染：

```typescript
// 条件渲染优化
{libraryLoaded && (
  <div className="books-container">
    {filteredBooks.map((book, index) => (
      <BookItem
        key={book.id}
        book={book}
        viewMode={viewMode}
        onDelete={handleBookDelete}
        onUpdate={handleBookUpdate}
        onRefresh={refreshBooks}
      />
    ))}
  </div>
)}
```

### 6. 错误处理架构

#### 渐进式错误处理

```typescript
// 服务层错误处理
export async function uploadBook(file: File): Promise<SimpleBook> {
  try {
    // 书籍上传逻辑
    const result = await invoke<SimpleBook>("save_book", { data: uploadData });
    return result;
  } catch (error) {
    console.error("书籍上传失败:", error);
    throw new Error(`上传失败: ${error instanceof Error ? error.message : "未知错误"}`);
  }
}

// 组件层错误处理
const handleBookUpload = useCallback(async (files: FileList) => {
  try {
    setIsUploading(true);
    for (const file of Array.from(files)) {
      await uploadBook(file);
    }
    await refreshBooks();
  } catch (error) {
    toast.error(`上传失败: ${error.message}`);
  } finally {
    setIsUploading(false);
  }
}, [refreshBooks]);
```

#### 优雅降级

```typescript
// 功能降级策略
const [libraryLoaded, setLibraryLoaded] = useState(false);

useEffect(() => {
  const initLibrary = async () => {
    try {
      await refreshBooks();
      setLibraryLoaded(true);
    } catch (error) {
      console.error("Error initializing library:", error);
      setLibraryLoaded(true); // 即使出错也设置为已加载，显示空状态
    }
  };
  initLibrary();
}, []);
```

这种架构设计确保了 Library 组件的可维护性、性能和用户体验，同时为未来的功能扩展提供了良好的基础。 