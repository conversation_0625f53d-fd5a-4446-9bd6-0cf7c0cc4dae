# Library 书籍管理系统详解

## 书籍管理系统概述

Library 的书籍管理系统负责书籍的完整生命周期管理，包括上传、存储、元数据管理、状态跟踪和删除等功能。系统采用前后端分离架构，前端负责UI交互，后端(Rust)负责文件处理和数据持久化。

## 核心数据模型

### 1. 书籍数据类型定义

```typescript
// 基础书籍信息
interface SimpleBook {
  id: string;
  title: string;
  author: string;
  format: string;
  fileSize: number;
  language: string;
  filePath: string;
  coverPath?: string;
  metadata?: any;
  createdAt: string;
  updatedAt: string;
}

// 带状态的书籍信息
interface BookWithStatus extends SimpleBook {
  tags?: string[];
  status: BookStatus;
  vectorizationMeta?: BookVectorizationMeta;
}

// 带URL的书籍信息(前端使用)
interface BookWithStatusAndUrls extends BookWithStatus {
  fileUrl: string;
  coverUrl?: string;
}

// 书籍状态枚举
enum BookStatus {
  Pending = "pending",       // 待处理
  Processing = "processing", // 处理中
  Completed = "completed",   // 完成
  Failed = "failed",        // 失败
}

// 向量化元数据
interface BookVectorizationMeta {
  totalChunks: number;
  processedChunks: number;
  lastProcessedAt?: string;
  status: "pending" | "processing" | "completed" | "failed";
}
```

### 2. 数据流转换

#### 文件路径到URL转换

前端需要将后端返回的文件系统路径转换为可访问的URL：

```typescript
async function convertBookWithStatusUrls(book: BookWithStatus): Promise<BookWithStatusAndUrls> {
  try {
    const appDataDirPath = await appDataDir();
    
    // 处理书籍文件路径
    const absoluteFilePath = book.filePath.startsWith("/") 
      ? book.filePath 
      : `${appDataDirPath}/${book.filePath}`;

    // 处理封面文件路径
    const absoluteCoverPath = book.coverPath
      ? book.coverPath.startsWith("/")
        ? book.coverPath
        : `${appDataDirPath}/${book.coverPath}`
      : undefined;

    // 转换为Tauri可访问的URL
    const fileUrl = convertFileSrc(absoluteFilePath);
    const coverUrl = absoluteCoverPath ? convertFileSrc(absoluteCoverPath) : undefined;

    return {
      ...book,
      fileUrl,
      coverUrl,
    };
  } catch (error) {
    console.error("Error converting book URLs for:", book.title, error);
    throw error;
  }
}
```

## 书籍上传系统

### 1. 上传流程架构

```typescript
export async function uploadBook(file: File): Promise<SimpleBook> {
  try {
    // 1. 验证文件格式
    const format = getBookFormat(file.name);
    if (!["EPUB", "PDF", "MOBI", "CBZ", "FB2", "FBZ"].includes(format)) {
      throw new Error(`不支持的文件格式: ${format}`);
    }

    // 2. 计算文件哈希(防重复)
    const bookHash = await partialMD5(file);

    // 3. 检查是否已存在
    const existingBook = await getBookById(bookHash);
    if (existingBook) {
      return await updateBook(bookHash, {});
    }

    // 4. 创建临时文件
    const tempDirPath = await tempDir();
    const tempFileName = `temp_${bookHash}.${format.toLowerCase()}`;
    const tempFilePath = await join(tempDirPath, tempFileName);
    const fileData = await file.arrayBuffer();
    await writeFile(tempFilePath, new Uint8Array(fileData));

    // 5. 提取元数据
    const metadata = await extractMetadataOnly(file);

    // 6. 处理封面(EPUB格式)
    let coverTempFilePath: string | undefined;
    if (format === "EPUB") {
      try {
        const bookDoc = await parseEpubFile(fileData, file.name);
        const coverBlob = await bookDoc.getCover();
        if (coverBlob) {
          const coverTempFileName = `cover_${bookHash}.jpg`;
          const coverTempPath = await join(tempDirPath, coverTempFileName);
          const coverArrayBuffer = await coverBlob.arrayBuffer();
          await writeFile(coverTempPath, new Uint8Array(coverArrayBuffer));
          coverTempFilePath = coverTempPath;
        }
      } catch (e) {
        console.warn("无法提取封面:", e);
      }
    }

    // 7. 构建上传数据
    const uploadData: BookUploadData = {
      id: bookHash,
      title: formatTitle(metadata.title) || getFileNameWithoutExt(file.name),
      author: formatAuthors(metadata.author) || "Unknown",
      format,
      fileSize: file.size,
      language: getPrimaryLanguage(metadata.language) || "en",
      tempFilePath: tempFilePath,
      coverTempFilePath,
      metadata: metadata,
    };

    // 8. 调用后端保存
    const result = await invoke<SimpleBook>("save_book", { data: uploadData });
    return result;
  } catch (error) {
    console.error("书籍上传失败:", error);
    throw new Error(`上传失败: ${error instanceof Error ? error.message : "未知错误"}`);
  }
}
```

### 2. 元数据提取

```typescript
async function extractMetadataOnly(file: File): Promise<any> {
  try {
    if (file.name.toLowerCase().endsWith(".epub")) {
      const arrayBuffer = await file.arrayBuffer();
      const bookDoc = await parseEpubFile(arrayBuffer, file.name);
      return bookDoc.metadata;
    }

    // 其他格式的基本元数据
    return {
      title: getFileNameWithoutExt(file.name),
      author: "Unknown",
      language: "en",
    };
  } catch (error) {
    console.warn("元数据提取失败，使用默认值:", error);
    return {
      title: getFileNameWithoutExt(file.name),
      author: "Unknown", 
      language: "en",
    };
  }
}
```

### 3. 文件格式处理

```typescript
function getBookFormat(filename: string): string {
  const ext = filename.toLowerCase().split('.').pop();
  const formatMap: { [key: string]: string } = {
    'epub': 'EPUB',
    'pdf': 'PDF',
    'mobi': 'MOBI',
    'azw3': 'MOBI',
    'cbz': 'CBZ',
    'fb2': 'FB2',
    'fbz': 'FBZ',
  };
  
  return formatMap[ext || ''] || 'UNKNOWN';
}

function getFileNameWithoutExt(filename: string): string {
  return filename.replace(/\.[^/.]+$/, "");
}
```

## 书籍信息管理

### 1. BookItem 组件架构

BookItem 是单个书籍的展示和管理组件：

```typescript
interface BookItemProps {
  book: BookWithStatusAndUrls;
  viewMode?: "grid" | "list";
  availableTags?: BookTag[];
  onDelete?: (book: BookWithStatusAndUrls) => Promise<boolean>;
  onUpdate?: (bookId: string, updates: BookUpdateData) => Promise<boolean>;
  onRefresh?: () => Promise<void>;
}

export default function BookItem({ book, availableTags = [], onDelete, onUpdate, onRefresh }: BookItemProps) {
  // 状态管理
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showAITagDialog, setShowAITagDialog] = useState(false);
  const [showEmbeddingDialog, setShowEmbeddingDialog] = useState(false);
  const [vectorizeProgress, setVectorizeProgress] = useState<number | null>(null);

  // 事件处理
  const handleClick = useCallback(() => {
    openReader([book.id], book.title);
  }, [book.id, book.title, openReader]);

  const handleContextMenu = useCallback(async (e: React.MouseEvent) => {
    e.preventDefault();
    await showBookContextMenu(book, { 
      onEdit: () => setShowEditDialog(true),
      onDelete: () => handleDeleteBook(),
      onAITag: () => handleAITagGeneration(),
      onVectorize: () => setShowEmbeddingDialog(true),
    });
  }, [book]);

  return (
    <div 
      className="book-item"
      onClick={handleClick}
      onContextMenu={handleContextMenu}
    >
      <BookCover book={book} />
      <BookInfo book={book} />
      <BookActions book={book} />
      
      {/* 模态框 */}
      {showEditDialog && (
        <EditInfo
          book={book}
          availableTags={availableTags}
          onClose={() => setShowEditDialog(false)}
          onUpdate={onUpdate}
        />
      )}
      
      {showAITagDialog && (
        <AITagConfirmDialog
          suggestions={aiTagSuggestions}
          onConfirm={handleConfirmAITags}
          onClose={() => setShowAITagDialog(false)}
        />
      )}
      
      {showEmbeddingDialog && (
        <EmbeddingDialog
          isOpen={showEmbeddingDialog}
          onClose={() => setShowEmbeddingDialog(false)}
          bookId={book.id}
        />
      )}
    </div>
  );
}
```

### 2. 右键菜单系统

```typescript
async function showBookContextMenu(
  book: BookWithStatusAndUrls,
  actions: {
    onEdit: () => void;
    onDelete: () => void;
    onAITag: () => void;
    onVectorize: () => void;
  }
) {
  try {
    const menu = await Menu.new({
      items: [
        {
          id: "open-book",
          text: "打开阅读",
          action: () => actions.onEdit(),
        },
        { id: "separator1", item: { Separator: {} } },
        {
          id: "edit-info",
          text: "编辑信息",
          action: () => actions.onEdit(),
        },
        {
          id: "ai-tag",
          text: "AI标签建议",
          action: () => actions.onAITag(),
        },
        {
          id: "vectorize",
          text: "向量化管理",
          action: () => actions.onVectorize(),
        },
        { id: "separator2", item: { Separator: {} } },
        {
          id: "delete-book",
          text: "删除书籍",
          action: () => actions.onDelete(),
        },
      ],
    });

    // 在鼠标位置显示菜单
    await menu.popup(new LogicalPosition(e.clientX, e.clientY));
  } catch (error) {
    console.error("Failed to show context menu:", error);
  }
}
```

### 3. 书籍信息编辑

#### EditInfo 组件

```typescript
interface EditInfoProps {
  book: BookWithStatusAndUrls;
  availableTags: BookTag[];
  onClose: () => void;
  onUpdate?: (bookId: string, updates: BookUpdateData) => Promise<boolean>;
}

export default function EditInfo({ book, availableTags, onClose, onUpdate }: EditInfoProps) {
  const [formData, setFormData] = useState({
    title: book.title,
    author: book.author,
    tags: book.tags || [],
    coverPath: book.coverPath,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (onUpdate) {
      const success = await onUpdate(book.id, formData);
      if (success) {
        toast.success("书籍信息更新成功");
        onClose();
      } else {
        toast.error("更新失败");
      }
    }
  };

  const handleCoverUpload = async (file: File) => {
    try {
      // 处理封面上传逻辑
      const coverUrl = await uploadCover(book.id, file);
      setFormData(prev => ({ ...prev, coverPath: coverUrl }));
    } catch (error) {
      toast.error("封面上传失败");
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>编辑书籍信息</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <FormField
            label="标题"
            value={formData.title}
            onChange={(value) => setFormData(prev => ({ ...prev, title: value }))}
          />
          
          <FormField
            label="作者"
            value={formData.author}
            onChange={(value) => setFormData(prev => ({ ...prev, author: value }))}
          />
          
          <TagSelector
            selectedTags={formData.tags}
            availableTags={availableTags}
            onChange={(tags) => setFormData(prev => ({ ...prev, tags }))}
          />
          
          <CoverUploader
            currentCover={formData.coverPath}
            onUpload={handleCoverUpload}
          />
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              取消
            </Button>
            <Button type="submit">
              保存
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
```

## 书籍状态管理

### 1. 状态跟踪系统

```typescript
// 书籍状态更新
export async function updateBookStatus(
  bookId: string, 
  status: BookStatus,
  additionalData?: Partial<BookStatusUpdateData>
): Promise<void> {
  try {
    await invoke("update_book_status", {
      bookId,
      status,
      ...additionalData,
    });
  } catch (error) {
    console.error("Failed to update book status:", error);
    throw error;
  }
}

// 向量化状态管理
export async function updateBookVectorizationMeta(
  bookId: string,
  meta: BookVectorizationMeta
): Promise<void> {
  try {
    await invoke("update_book_vectorization_meta", {
      bookId,
      meta,
    });
  } catch (error) {
    console.error("Failed to update vectorization meta:", error);
    throw error;
  }
}
```

### 2. 进度监听系统

```typescript
// 在BookItem组件中监听向量化进度
useEffect(() => {
  let unlisten: (() => void) | null = null;
  
  (async () => {
    const off = await listen<{
      book_id: string;
      current: number;
      total: number;
      percent: number;
      chapter_title: string;
      chunk_index: number;
    }>("epub://index-progress", (e) => {
      const p = e.payload;
      if (p && p.book_id === book.id) {
        // 更新进度显示
        setVectorizeProgress(Math.max(0, Math.min(100, Math.round(p.percent))));
      }
    });
    unlisten = off;
  })();
  
  return () => {
    if (unlisten) unlisten();
  };
}, [book.id]);
```

## 书籍删除系统

### 1. 删除确认机制

```typescript
const handleDeleteBook = async (book: BookWithStatusAndUrls) => {
  try {
    // 显示确认对话框
    const confirmed = await ask(
      `确定要删除《${book.title}》吗？\n\n这将永久删除书籍文件和所有相关数据。`,
      {
        title: "确认删除书籍",
        kind: "warning",
      }
    );

    if (confirmed) {
      // 执行删除操作
      const success = await onDelete?.(book);
      if (success) {
        toast.success("书籍删除成功");
      } else {
        toast.error("删除失败");
      }
    }
  } catch (error) {
    console.error("Delete operation failed:", error);
    toast.error("删除操作失败");
  }
};
```

### 2. 后端删除逻辑

```typescript
export async function deleteBook(bookId: string): Promise<void> {
  try {
    await invoke("delete_book", { bookId });
  } catch (error) {
    console.error("Failed to delete book:", error);
    throw error;
  }
}
```

## 书籍搜索和过滤

### 1. 多字段搜索

```typescript
export const useBooksFilter = (books: BookWithStatusAndUrls[], searchQuery: string) => {
  const filteredBooks = useMemo(() => {
    if (!searchQuery.trim()) {
      return books;
    }

    const query = searchQuery.toLowerCase().trim();
    return books.filter((book) => {
      // 标题匹配
      if (book.title.toLowerCase().includes(query)) return true;
      
      // 作者匹配
      if (book.author.toLowerCase().includes(query)) return true;
      
      // 标签匹配
      if (book.tags?.some((tag) => tag.toLowerCase().includes(query))) return true;
      
      // 格式匹配
      if (book.format.toLowerCase().includes(query)) return true;
      
      return false;
    });
  }, [books, searchQuery]);

  return { filteredBooks };
};
```

### 2. 高级筛选

```typescript
// 按状态筛选
const filterByStatus = (books: BookWithStatusAndUrls[], status?: BookStatus) => {
  if (!status) return books;
  return books.filter(book => book.status === status);
};

// 按格式筛选
const filterByFormat = (books: BookWithStatusAndUrls[], format?: string) => {
  if (!format) return books;
  return books.filter(book => book.format === format);
};

// 按语言筛选
const filterByLanguage = (books: BookWithStatusAndUrls[], language?: string) => {
  if (!language) return books;
  return books.filter(book => book.language === language);
};
```

这个书籍管理系统提供了完整的书籍生命周期管理功能，从上传到删除的每个环节都有详细的处理逻辑和错误处理机制，确保了系统的稳定性和用户体验。 