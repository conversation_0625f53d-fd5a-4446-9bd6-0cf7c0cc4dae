# Library 状态管理和数据流设计

## 状态管理架构概览

Library 组件采用 **Zustand + Custom Hooks** 的状态管理模式，实现了清晰的数据流和状态分离：

- **Zustand Store**: 管理全局书籍数据和持久化状态
- **Custom Hooks**: 封装业务逻辑和本地状态
- **服务层**: 处理数据转换和外部API交互

## 核心状态管理器

### 1. libraryStore (全局状态)

**职责**：管理所有书籍数据和全局搜索状态

```typescript
interface LibraryState {
  library: Book[];                    // 原始书籍列表
  currentBookshelf: (Book | BooksGroup)[]; // 当前书架状态
  searchQuery: string;                // 搜索查询
  booksWithStatus: BookWithStatusAndUrls[]; // 带状态和URL的书籍数据
  isLoading: boolean;                // 加载状态

  // 状态操作方法
  getVisibleLibrary: () => Book[];
  setLibrary: (books: Book[]) => void;
  updateBook: (envConfig: EnvConfigType, book: Book) => void;
  setCurrentBookshelf: (bookshelf: (Book | BooksGroup)[]) => void;
  setSearchQuery: (query: string) => void;
  refreshBooks: () => Promise<void>;
  setBooksWithStatus: (books: BookWithStatusAndUrls[]) => void;
  setIsLoading: (loading: boolean) => void;
}
```

**关键实现**：

```typescript
export const useLibraryStore = create<LibraryState>((set, get) => ({
  library: [],
  currentBookshelf: [],
  searchQuery: "",
  booksWithStatus: [],
  isLoading: false,

  // 获取未删除的书籍
  getVisibleLibrary: () => get().library.filter((book) => !book.deletedAt),

  // 刷新书籍数据 - 核心方法
  refreshBooks: async () => {
    try {
      set({ isLoading: true });
      
      // 从后端获取书籍状态数据
      const libraryBooks = await getBooksWithStatus();
      
      // 转换文件路径为可访问的URL
      const booksWithUrls = await Promise.all(
        libraryBooks.map(convertBookWithStatusUrls)
      );
      
      set({ booksWithStatus: booksWithUrls });
    } catch (error) {
      console.error("Error refreshing books:", error);
    } finally {
      set({ isLoading: false });
    }
  },

  // 更新单个书籍
  updateBook: async (envConfig: EnvConfigType, book: Book) => {
    const appService = await envConfig.getAppService();
    const { library } = get();
    const bookIndex = library.findIndex((b) => b.hash === book.hash);
    
    if (bookIndex !== -1) {
      library[bookIndex] = book;
      set({ library: [...library] });
      appService.saveLibraryBooks(library);
    }
  },
}));
```

### 2. 数据转换层

#### URL 转换处理

Library 需要将后端返回的文件路径转换为前端可访问的URL：

```typescript
async function convertBookWithStatusUrls(book: BookWithStatus): Promise<BookWithStatusAndUrls> {
  try {
    const appDataDirPath = await appDataDir();
    
    // 处理文件路径
    const absoluteFilePath = book.filePath.startsWith("/") 
      ? book.filePath 
      : `${appDataDirPath}/${book.filePath}`;

    // 处理封面路径
    const absoluteCoverPath = book.coverPath
      ? book.coverPath.startsWith("/")
        ? book.coverPath
        : `${appDataDirPath}/${book.coverPath}`
      : undefined;

    // 转换为Tauri可访问的URL
    const fileUrl = convertFileSrc(absoluteFilePath);
    const coverUrl = absoluteCoverPath ? convertFileSrc(absoluteCoverPath) : undefined;

    return {
      ...book,
      fileUrl,    // 添加可访问的文件URL
      coverUrl,   // 添加可访问的封面URL
    };
  } catch (error) {
    console.error("Error converting book URLs for:", book.title, error);
    throw error;
  }
}
```

## 自定义 Hooks 状态管理

### 1. useTagsManagement - 标签状态管理

**职责**：处理标签系统的所有状态逻辑

```typescript
export const useTagsManagement = (booksWithStatus: BookWithStatusAndUrls[]) => {
  const [selectedTag, setSelectedTag] = useState<string>("all");
  const [tags, setTags] = useState<BookTag[]>([]);
  const [databaseTags, setDatabaseTags] = useState<Tag[]>([]);

  // 从数据库加载标签并计算统计数据
  useEffect(() => {
    const loadTags = async () => {
      const dbTags = await getTags();
      setDatabaseTags(dbTags);
      
      const generatedTags = await generateTagsFromDatabase(booksWithStatus);
      setTags(generatedTags);
    };
    loadTags();
  }, [booksWithStatus]);

  // 根据选中标签过滤书籍
  const filteredBooksByTag = useMemo(() => {
    if (selectedTag === "all") {
      return booksWithStatus;
    }

    if (selectedTag === "uncategorized") {
      return booksWithStatus.filter((book) => !book.tags || book.tags.length === 0);
    }

    if (selectedTag.startsWith("tag-")) {
      const tagName = selectedTag.replace("tag-", "");
      const tagObj = databaseTags.find((tag) => tag.name === tagName);
      if (tagObj) {
        return booksWithStatus.filter((book) => book.tags?.includes(tagObj.id));
      }
      return [];
    }

    return booksWithStatus;
  }, [booksWithStatus, selectedTag, databaseTags]);

  return {
    selectedTag,
    tags,
    filteredBooksByTag,
    handleTagSelect,
  };
};
```

**标签数据生成逻辑**：

```typescript
const generateTagsFromDatabase = async (books: BookWithStatusAndUrls[]): Promise<BookTag[]> => {
  const untaggedCount = books.filter((book) => !book.tags || book.tags.length === 0).length;
  
  // 获取数据库中的所有标签
  const databaseTags = await getTags();
  
  const tags: BookTag[] = [
    { id: "all", name: "全部", count: books.length, color: "#6b7280" }
  ];

  // 添加未分类标签
  if (untaggedCount > 0) {
    tags.push({
      id: "uncategorized",
      name: "未分类", 
      count: untaggedCount,
      color: "#6b7280",
    });
  }

  // 为每个数据库标签计算书籍数量
  const bookTags = databaseTags.map((tag) => {
    const count = books.filter((book) => book.tags?.includes(tag.id)).length;
    return {
      id: `tag-${tag.name}`,
      name: tag.name,
      count,
      color: tag.color || "#6b7280",
    };
  });

  // 按书籍数量排序
  const sortedTags = bookTags.sort((a, b) => {
    if (a.count === 0 && b.count === 0) return a.name.localeCompare(b.name);
    if (a.count === 0) return 1;
    if (b.count === 0) return -1;
    return b.count - a.count;
  });

  tags.push(...sortedTags);
  return tags;
};
```

### 2. useBooksFilter - 搜索过滤状态

**职责**：处理书籍搜索和过滤逻辑

```typescript
export const useBooksFilter = (filteredBooksByTag: BookWithStatusAndUrls[], searchQuery: string) => {
  const filteredBooks = useMemo(() => {
    const books = filteredBooksByTag;

    if (!searchQuery.trim()) {
      return books;
    }

    const query = searchQuery.toLowerCase().trim();
    return books.filter((book: BookWithStatusAndUrls) => {
      // 多字段搜索
      if (book.title.toLowerCase().includes(query)) return true;
      if (book.author.toLowerCase().includes(query)) return true;
      if (book.tags?.some((tag) => tag.toLowerCase().includes(query))) return true;
      return false;
    });
  }, [filteredBooksByTag, searchQuery]);

  return { filteredBooks };
};
```

### 3. useBooksOperations - 书籍操作状态

**职责**：处理书籍的增删改操作

```typescript
export const useBooksOperations = (refreshBooks: () => Promise<void>) => {
  const handleBookDelete = useCallback(
    async (book: BookWithStatusAndUrls) => {
      try {
        // 动态导入减少包大小
        const { deleteBook } = await import("@/services/bookService");
        await deleteBook(book.id);

        // 刷新数据
        await refreshBooks();
        return true;
      } catch (error) {
        console.error("Failed to delete book:", error);
        return false;
      }
    },
    [refreshBooks],
  );

  const handleBookUpdate = useCallback(
    async (bookId: string, updateData: BookUpdateData) => {
      try {
        const { updateBook } = await import("@/services/bookService");
        await updateBook(bookId, updateData);

        await refreshBooks();
        return true;
      } catch (error) {
        console.error("Failed to update book:", error);
        return false;
      }
    },
    [refreshBooks],
  );

  return {
    handleBookDelete,
    handleBookUpdate,
  };
};
```

### 4. useLibraryUI - UI 状态管理

**职责**：管理UI相关的本地状态

```typescript
export const useLibraryUI = () => {
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [isLibraryExpanded, setIsLibraryExpanded] = useState(true);
  const [showNewTagDialog, setShowNewTagDialog] = useState(false);

  const toggleLibraryExpanded = useCallback(() => {
    setIsLibraryExpanded((prev) => !prev);
  }, []);

  const handleNewTagClick = useCallback(() => {
    setShowNewTagDialog(true);
  }, []);

  const handleCloseNewTagDialog = useCallback(() => {
    setShowNewTagDialog(false);
  }, []);

  return {
    viewMode,
    setViewMode,
    isLibraryExpanded,
    showNewTagDialog,
    toggleLibraryExpanded,
    handleNewTagClick,
    handleCloseNewTagDialog,
  };
};
```

## 数据流架构

### 1. 完整数据流图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Tauri Backend │────│  bookService.ts │────│ libraryStore.ts │
│     (Rust)      │    │   (Data Layer)  │    │ (Global State)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                               ┌────────────────────────┴────────────────────────┐
                               │                                                 │
                    ┌─────────────────┐                              ┌─────────────────┐
                    │useTagsManagement│                              │ useBooksFilter  │
                    │     (Hook)      │                              │     (Hook)      │
                    └─────────────────┘                              └─────────────────┘
                               │                                                 │
                               └────────────────┬────────────────────────────────┘
                                               │
                                    ┌─────────────────┐
                                    │ UI Components   │
                                    │ (BookItem, etc) │
                                    └─────────────────┘
```

### 2. 状态更新流程

#### 书籍数据加载流程

```typescript
// 1. 组件初始化触发数据加载
useEffect(() => {
  const initLibrary = async () => {
    try {
      // 2. 调用全局store的刷新方法
      await refreshBooks();
      setLibraryLoaded(true);
    } catch (error) {
      console.error("Error initializing library:", error);
    }
  };
  initLibrary();
}, []);

// 3. refreshBooks 执行数据获取和转换
refreshBooks: async () => {
  try {
    set({ isLoading: true });
    
    // 4. 从后端服务获取数据
    const libraryBooks = await getBooksWithStatus();
    
    // 5. 转换数据格式
    const booksWithUrls = await Promise.all(
      libraryBooks.map(convertBookWithStatusUrls)
    );
    
    // 6. 更新全局状态
    set({ booksWithStatus: booksWithUrls });
  } catch (error) {
    console.error("Error refreshing books:", error);
  } finally {
    set({ isLoading: false });
  }
},
```

#### 标签筛选流程

```typescript
// 1. 用户选择标签
const handleTagSelect = useCallback((tagId: string, onSearchClear?: () => void) => {
  // 2. 更新选中状态
  setSelectedTag(tagId);
  
  // 3. 清空搜索（可选）
  if (onSearchClear) {
    onSearchClear();
  }
}, []);

// 4. useMemo 自动重新计算过滤结果
const filteredBooksByTag = useMemo(() => {
  if (selectedTag === "all") {
    return booksWithStatus;
  }
  
  if (selectedTag === "uncategorized") {
    return booksWithStatus.filter((book) => !book.tags || book.tags.length === 0);
  }
  
  // 处理具体标签过滤
  if (selectedTag.startsWith("tag-")) {
    const tagName = selectedTag.replace("tag-", "");
    const tagObj = databaseTags.find((tag) => tag.name === tagName);
    if (tagObj) {
      return booksWithStatus.filter((book) => book.tags?.includes(tagObj.id));
    }
  }
  
  return booksWithStatus;
}, [booksWithStatus, selectedTag, databaseTags]);
```

### 3. 状态同步机制

#### 跨组件状态同步

```typescript
// 主页面作为状态协调器
const NewLibraryPage = () => {
  // 从全局store获取数据
  const { booksWithStatus, refreshBooks, setSearchQuery, searchQuery } = useLibraryStore();
  
  // 传递给标签管理hook
  const { selectedTag, tags, filteredBooksByTag, handleTagSelect } = useTagsManagement(booksWithStatus);
  
  // 传递给过滤hook
  const { filteredBooks } = useBooksFilter(filteredBooksByTag, searchQuery);
  
  // 传递给操作hook
  const { handleBookDelete, handleBookUpdate } = useBooksOperations(refreshBooks);

  // 将状态和方法传递给子组件
  return (
    <div>
      <Sidebar
        searchQuery={searchQuery}
        onSearchChange={(e) => setSearchQuery(e.target.value)}
        tags={tags}
        selectedTag={selectedTag}
        handleTagClick={handleTagSelect}
        // ...其他props
      />
      <main>
        {filteredBooks.map(book => (
          <BookItem
            key={book.id}
            book={book}
            onDelete={handleBookDelete}
            onUpdate={handleBookUpdate}
            onRefresh={refreshBooks}
          />
        ))}
      </main>
    </div>
  );
};
```

## 性能优化策略

### 1. 记忆化优化

```typescript
// 使用 useMemo 缓存计算结果
const filteredBooks = useMemo(() => {
  // 昂贵的过滤计算
}, [filteredBooksByTag, searchQuery]);

// 使用 useCallback 缓存函数引用
const handleTagSelect = useCallback((tagId: string, onSearchClear?: () => void) => {
  setSelectedTag(tagId);
  if (onSearchClear) onSearchClear();
}, []);
```

### 2. 延迟状态更新

```typescript
// 防抖搜索输入
const [searchQuery, setSearchQuery] = useState("");
const debouncedSearch = useMemo(
  () => debounce((query: string) => {
    // 执行实际搜索
    performSearch(query);
  }, 300),
  []
);

useEffect(() => {
  debouncedSearch(searchQuery);
}, [searchQuery, debouncedSearch]);
```

### 3. 选择性重渲染

```typescript
// 使用 React.memo 优化组件重渲染
const BookItem = React.memo<BookItemProps>(({ book, onDelete, onUpdate }) => {
  // 组件逻辑
}, (prevProps, nextProps) => {
  // 自定义比较逻辑
  return prevProps.book.id === nextProps.book.id &&
         prevProps.book.updatedAt === nextProps.book.updatedAt;
});
```

这种状态管理架构确保了数据流的清晰性和组件间的松耦合，同时提供了良好的性能和可维护性。 