# MD文件向量化与智能上下文检索系统

## 📖 系统概述

本系统实现了基于文档结构的智能分片和两级检索架构，相比传统RAG系统有显著优势。

### 🎯 核心创新
- **结构化分片**：基于EPUB → MDBook → TOC结构的智能分片
- **位置感知**：每个分片都有完整的"GPS定位"信息
- **两级检索**：精准搜索 + 智能扩展的架构
- **上下文感知**：支持前后文、章节、范围等多种检索方式
- **鲁棒性强**：智能文件查找和特殊字符处理

## 🏗️ 系统架构

### 数据流程
```
EPUB → MDBook转换 → 智能TOC查找 → TOC解析 → 结构化分片 → 向量化 → 智能检索
```

### 核心组件
- **后端**: Rust + Tauri插件架构
- **向量数据库**: SQLite + sqlite-vec扩展
- **前端工具**: TypeScript + AI Tools集成
- **文档转换**: epub2mdbook + 自定义TOC解析
- **文件处理**: 递归搜索 + URL解码 + 多格式支持

## 📊 数据结构设计

### DocumentChunk 增强结构
```rust
pub struct DocumentChunk {
    // 基础信息
    id: Option<i64>,
    book_title: String,
    book_author: String,
    chapter_title: String,
    chunk_text: String,
    
    // 🆕 TOC导航信息
    md_src: String,              // MD文件路径 "text/part001.md"
    toc_depth: u32,              // TOC层级深度 0,1,2...
    toc_id: String,              // TOC节点ID "num_1"
    toc_index: usize,            // 在扁平化TOC数组中的索引位置
    
    // 🆕 分块位置信息
    chunk_index_in_toc: usize,   // 在当前TOC节点中的第几个分块
    total_chunks_in_toc: usize,  // 当前TOC节点的总分块数
    
    // 🆕 全局位置信息
    global_chunk_index: usize,   // 在整本书中的全局分块序号
}
```

### FlatTocNode 扁平化TOC
```rust
pub struct FlatTocNode {
    id: String,                  // TOC节点ID
    play_order: u32,             // 播放顺序
    title: String,               // 章节标题
    md_src: String,              // 转换后的MD文件路径（无锚点）
    depth: u32,                  // 节点深度，从0开始
}
```

## 🔄 分片流程详解

### 1. EPUB到MDBook转换
```rust
// packages/app/src-tauri/plugins/tauri-plugin-epub/src/pipeline.rs
convert_epub_to_mdbook(&epub_path, &mdbook_dir, true)?;
```

**优势**：
- 保持文档原始结构
- 生成标准化的Markdown文件
- 自动处理图片和链接

### 2. 智能TOC文件查找 🆕
```rust
// 递归搜索整个 mdbook_dir 查找 toc.ncx
let toc_path = find_toc_ncx_in_mdbook(&mdbook_dir)
    .context("TOC file (toc.ncx) not found in MDBook directory")?;
```

**创新特性**：
- **递归搜索**：不依赖固定目录结构，适应各种EPUB格式
- **智能定位**：在整个mdbook目录树中查找toc.ncx文件
- **错误处理**：完善的错误处理，避免权限问题

### 3. TOC解析与扁平化 🆕
```rust
let toc_nodes = parse_toc_file(&toc_path)?;
let flat_toc = flatten_toc(&toc_nodes);
```

**核心函数**：
- `find_toc_ncx_in_mdbook()`: 递归搜索TOC文件
- `parse_toc_file()`: 解析XML格式的TOC文件，支持URL解码
- `flatten_toc()`: 将层级结构转换为扁平数组
- `html_to_md_src()`: 智能路径转换，支持多种格式

### 4. 智能路径处理 🆕
```rust
pub fn html_to_md_src(src: &str) -> String {
    // 移除锚点 + URL解码 + 多格式转换
    let file_part = extract_file_part(src);
    let decoded_path = url_decode(file_part);  // 🆕 URL解码
    html_to_md_filename(&decoded_path)         // 🆕 多格式支持
}
```

**支持的文档格式**：
- `.html` → `.md`
- `.xhtml` → `.md` (EPUB常用)
- `.htm` → `.md`
- `.xml` → `.md`
- `.xht` → `.md`

**特殊字符处理**：
- `Si_Kao_%2cKuai_Yu_Man_split_121.html` → `Si_Kao_,Kuai_Yu_Man_split_121.md`
- `file%20with%20spaces.xhtml` → `file with spaces.md`
- `special%21chars%40test.htm` → `special!<EMAIL>`

### 5. 基于结构的智能分片
```rust
// 按TOC结构遍历MD文件
for (toc_index, toc_node) in flat_toc.iter().enumerate() {
    let md_content = fs::read_to_string(&md_file_path)?;
    let chunks = reader.chunk_md_file(&md_content, 256, 50);
    
    // 记录完整位置信息
    for (chunk_idx_in_toc, chunk_content) in chunks.into_iter().enumerate() {
        all_chunks.push((
            toc_node.clone(),
            toc_index,
            chunk_idx_in_toc,
            total_chunks_in_toc,
            chunk_content,
        ));
    }
}
```

### 6. Markdown感知分块算法
```rust
pub fn chunk_md_file(&self, md_content: &str, min_tokens: usize, max_tokens: usize) -> Vec<String> {
    // 1. 尝试按Markdown结构分块
    if let Some(structured_chunks) = self.chunk_by_markdown_structure(md_content, min_tokens, max_tokens) {
        return structured_chunks;
    }
    
    // 2. 回退到标准文本分块
    self.chunk_text_by_tokens(md_content, min_tokens, max_tokens)
}
```

**智能特性**：
- 识别Markdown标题边界
- 保持代码块完整性
- 考虑段落和列表结构
- 智能重叠处理

## 🗄️ 数据库设计

### 表结构
```sql
CREATE TABLE document_chunks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_title TEXT NOT NULL,
    book_author TEXT NOT NULL,
    chapter_title TEXT NOT NULL,
    chunk_text TEXT NOT NULL,
    
    -- TOC导航字段
    md_src TEXT NOT NULL DEFAULT '',
    toc_depth INTEGER NOT NULL DEFAULT 0,
    toc_id TEXT NOT NULL DEFAULT '',
    toc_index INTEGER NOT NULL DEFAULT 0,
    
    -- 分块位置字段
    chunk_index_in_toc INTEGER NOT NULL DEFAULT 0,
    total_chunks_in_toc INTEGER NOT NULL DEFAULT 0,
    
    -- 全局位置字段
    global_chunk_index INTEGER NOT NULL DEFAULT 0
);
```

### 关键索引
```sql
-- TOC相关索引，提升上下文检索性能
CREATE INDEX idx_toc_index ON document_chunks (toc_index);
CREATE INDEX idx_toc_chunk_position ON document_chunks (toc_index, chunk_index_in_toc);
CREATE INDEX idx_global_chunk_index ON document_chunks (global_chunk_index);
CREATE INDEX idx_toc_id ON document_chunks (toc_id);
```

## 🔍 四大检索工具

### 1. ragSearch - 精准向量搜索
```typescript
const results = await ragSearch({
  question: "什么是机器学习？",
  limit: 5,
  format: true
});
```

**返回增强结构**：
```typescript
{
  results: [{
    rank: 1,
    chapter_title: "第3章 机器学习基础",
    similarity: 87.5,
    content: "...",
    position: {
      chunk_id: 42,
      toc_id: "num_3",
      toc_depth: 1,
      global_index: 156,
      toc_position: "3/7",
      md_source: "text/part003.md"
    }
  }],
  formatted: "格式化的上下文文本",
  meta: { total_found: 5, book_id: "...", query: "..." }
}
```

### 2. ragContext - 智能上下文检索
```typescript
const context = await ragContext({
  chunk_id: 42,
  prev_count: 2,
  next_count: 2
});
```

**特性**：
- 🎯 清楚标识目标分块
- 📄 获取前后文分块
- 📊 提供相对位置信息

### 3. ragToc - 完整章节检索
```typescript
const chapter = await ragToc({
  toc_id: "num_3"
});
```

**特性**：
- 📖 获取完整章节内容
- 📌 标识章节开始和结束
- 📊 提供章节统计信息

### 4. ragRange - 精确范围检索
```typescript
const range = await ragRange({
  start_index: 100,
  end_index: 120,
  max_chunks: 20
});
```

**特性**：
- 🌐 支持跨章节检索
- 📚 智能显示章节变化
- 📊 提供范围统计信息

## 🧠 智能检索策略

### 两级检索架构
```
第一级：ragSearch(精准定位) 
    ↓
分析top1结果是否充分
    ↓
第二级：按需扩展
├─ 需要上下文 → ragContext
├─ 讨论章节 → ragToc
└─ 特定范围 → ragRange
```

### AI决策逻辑
```typescript
// 1. 精准搜索
const searchResults = await ragSearch({ question, limit: 5 });

// 2. 分析top1结果
const topResult = searchResults.results[0];

// 3. 智能判断扩展需求
if (contentIncomplete(topResult)) {
    // 获取上下文
    const context = await ragContext({
        chunk_id: topResult.position.chunk_id,
        prev_count: 2,
        next_count: 2
    });
} else if (userAsksAboutChapter()) {
    // 获取完整章节
    const chapter = await ragToc({
        toc_id: topResult.position.toc_id
    });
}
```

## 🎯 与传统RAG的优势对比

| 维度 | 传统RAG | 我们的系统 |
|------|---------|------------|
| **分片质量** | 机械式切分，破坏语义 | 基于文档结构，保持完整性 |
| **位置感知** | 无位置信息 | 完整GPS定位（章节/层级/位置） |
| **上下文获取** | 单一片段，缺乏前后文 | 智能获取前后文/章节/范围 |
| **检索策略** | 单一相似度搜索 | 两级检索：定位+扩展 |
| **AI决策** | 直接基于片段回答 | 智能判断是否需要更多信息 |
| **文件处理** | 固定路径假设 | 智能搜索+特殊字符处理 |
| **格式支持** | 单一HTML格式 | 多种文档格式支持 |
| **结果质量** | 可能不完整或片面 | 完整准确，支持深度理解 |

## 💻 关键技术实现

### 后端核心文件
```
plugins/tauri-plugin-epub/src/
├── pipeline.rs        # 主流程控制
├── database.rs        # 数据库和上下文检索API
├── parse_toc.rs       # TOC解析、搜索和路径处理 🆕
├── epub_reader.rs     # Markdown感知分块
├── commands.rs        # Tauri命令接口
└── permissions/       # 权限配置文件
```

### 前端工具集
```
ai/tools/
├── ragSearch.ts       # 增强向量搜索
├── ragContext.ts      # 上下文检索
├── ragToc.ts         # 章节检索
└── ragRange.ts       # 范围检索
```

### 配置文件
```
constants/prompt.ts    # AI使用规则和策略指导
```

## 🚀 使用示例

### 典型对话流程
```
用户: "书中关于三种对错观的内容是什么？"
    ↓
AI: ragSearch("三种对错观") 
    ↓ 
AI: 分析top1结果，发现需要更多上下文
    ↓
AI: ragContext(chunk_id: 42, prev: 2, next: 2)
    ↓
AI: 基于完整上下文给出详细回答
```

## 🔧 部署和配置

### 依赖项
- **后端**: `epub2mdbook`, `sqlite-vec`, `anyhow`, `tauri`
- **前端**: `@tauri-apps/api`, `ai` SDK, `zod`

### 配置要点
- 本地嵌入服务端口：`http://127.0.0.1:3544`
- 向量维度：1024
- 分块参数：min_tokens=50, max_tokens=400
- 默认上下文窗口：前后各2个分块

## 📈 性能优化

### 数据库优化
- 智能初始化检查，避免重复日志
- 关键字段索引，提升查询性能
- 批量插入，提升写入效率

### 检索优化
- 两级架构避免不必要的多工具调用
- 智能决策减少无效检索
- 位置信息缓存提升扩展速度

### 文件处理优化 🆕
- 递归搜索避免路径假设错误
- URL解码处理特殊字符
- 多格式支持提升兼容性

## 🐛 关键问题解决记录

### 问题1：TOC文件查找失败 🆕
**症状**：某些EPUB的toc.ncx文件不在预期路径
**根因**：固定路径假设 `mdbook/book/src/toc.ncx`
**解决方案**：实现 `find_toc_ncx_in_mdbook()` 递归搜索整个目录

### 问题2：特殊字符文件名解析失败 🆕
**症状**：包含逗号的文件名无法找到 (`Si_Kao_%2cKuai_Yu_Man_split_121.md`)
**根因**：TOC中的URL编码字符未解码
**解决方案**：实现 `url_decode()` 函数处理URL编码

### 问题3：文件扩展名转换错误 🆕
**症状**：.xhtml文件被转换为 `.xhtml.md` 而不是 `.md`
**根因**：只支持 `.html` 扩展名转换
**解决方案**：支持多种文档格式 (`.html`/`.xhtml`/`.htm`/`.xml`/`.xht`)

## 🧪 测试验证

### 单元测试覆盖
```rust
#[test]
fn test_html_to_md_src() {
    // 基础格式测试
    assert_eq!(html_to_md_src("chapter.html"), "chapter.md");
    assert_eq!(html_to_md_src("chapter.xhtml"), "chapter.md");
    
    // URL编码测试
    assert_eq!(html_to_md_src("Si_Kao_%2cKuai_Yu_Man_split_121.html"), 
               "Si_Kao_,Kuai_Yu_Man_split_121.md");
    
    // 复合场景测试
    assert_eq!(html_to_md_src("file%20with%20spaces.xhtml#anchor"), 
               "file with spaces.md");
}
```

### 测试运行方法
```bash
# 运行所有测试
cargo test

# 运行特定测试
cargo test test_html_to_md_src
cargo test test_url_decode

# 显示测试输出
cargo test -- --nocapture
```

## 🎉 成果总结

这套系统实现了：
- ✅ **结构化分片**: 基于文档真实结构，保持语义完整性
- ✅ **位置感知**: 每个分片都有完整的定位信息
- ✅ **智能检索**: 两级架构支持精准定位和灵活扩展
- ✅ **上下文理解**: 支持前后文、章节、范围等多维度检索
- ✅ **简化决策**: AI使用简单高效的两步策略
- ✅ **高质量回答**: 基于完整上下文的准确回答
- ✅ **鲁棒性强**: 智能文件查找，处理各种EPUB结构
- ✅ **特殊字符支持**: URL解码，支持复杂文件名
- ✅ **多格式兼容**: 支持HTML/XHTML/XML等多种格式

相比传统RAG系统，在准确性、完整性、鲁棒性和智能程度上都有显著提升，真正实现了"让AI有全局视野和智能判断"的目标。

## 🔮 未来优化方向

### 技术改进
- 考虑使用 `percent-encoding` crate 替代手写URL解码器
- 增加更多文档格式支持 (如`.epub`直接解析)
- 优化大文件的分块算法

### 功能扩展
- 支持图片和表格的结构化处理
- 增加多语言文档的智能处理
- 实现更精细的语义分片算法 