# 分片系统实施技术指南

## 🚀 快速开始

### P0任务：立即实施

#### 1. 精度升级 - 基于真实偏移

**核心修改**：
```rust
// 新增数据结构
pub struct ChunkWithOffset {
    pub content: String,
    pub start_offset: usize,
    pub end_offset: usize,
    pub token_count: usize,
}

// 新增接口
pub fn chunk_md_file_with_offsets(
    &self, 
    md_content: &str, 
    min_tokens: usize, 
    max_tokens: usize
) -> Vec<ChunkWithOffset> {
    // 在分片过程中记录精确的文档偏移量
}
```

**Pipeline集成**：
```rust
// 精确的TOC归属计算
fn assign_chunks_to_toc_precise(
    chunks: &[ChunkWithOffset],
    toc_intervals: &[(usize, usize, usize)]
) -> Vec<(usize, &ChunkWithOffset)> {
    chunks.iter().map(|chunk| {
        let best_toc = toc_intervals.iter()
            .max_by_key(|(_, start, end)| {
                // 计算重叠区域大小
                chunk.end_offset.min(*end)
                    .saturating_sub(chunk.start_offset.max(*start))
            })
            .map(|(idx, _, _)| *idx)
            .unwrap_or(0);
        (best_toc, chunk)
    }).collect()
}
```

#### 2. 测试用例建设

**基础测试框架**：
```rust
#[cfg(test)]
mod chunking_tests {
    #[test]
    fn test_offset_accuracy() {
        let md_content = "# Chapter 1\nContent here.\n# Chapter 2\nMore content.";
        let chunks = chunk_md_file_with_offsets(md_content, 50, 400);
        
        for chunk in chunks {
            // 验证偏移量的准确性
            assert_eq!(&md_content[chunk.start_offset..chunk.end_offset], chunk.content);
        }
    }
    
    #[test]
    fn test_duplicate_titles() {
        // 测试重复标题的处理
    }
    
    #[test]
    fn test_no_duplicate_chunks() {
        // 验证无重复分片
    }
}
```

#### 3. 指标监控系统

**指标收集**：
```rust
#[derive(Serialize)]
pub struct ChunkingMetrics {
    pub title_hit_rate: f32,
    pub total_chunks: usize,
    pub processing_time_ms: u64,
    pub title_mismatches: Vec<String>,
}

pub struct MetricsCollector {
    start_time: Instant,
    title_hits: usize,
    title_attempts: usize,
    mismatches: Vec<String>,
}

impl MetricsCollector {
    pub fn record_title_match(&mut self, title: &str, found: bool) {
        self.title_attempts += 1;
        if found {
            self.title_hits += 1;
        } else {
            self.mismatches.push(title.to_string());
        }
    }
}
```

**日志集成**：
```rust
// 在pipeline.rs中
let mut metrics = MetricsCollector::new();
// ... 处理过程 ...
let final_metrics = metrics.finalize();
log::info!("📊 分片完成: {}", serde_json::to_string_pretty(&final_metrics)?);
```

### P1任务：中期实施

#### 1. 锚点优先策略

**锚点解析**：
```rust
fn parse_markdown_anchors(md_content: &str) -> HashMap<String, usize> {
    let mut anchors = HashMap::new();
    let anchor_regex = regex::Regex::new(r"(?:^#+.*\{#([^}]+)\}|<[^>]*id=[\'\"]([^\'\"]+)[\'\"])").unwrap();
    
    for (offset, line) in md_content.lines().scan(0, |pos, line| {
        let current = *pos;
        *pos += line.len() + 1;
        Some((current, line))
    }) {
        if let Some(caps) = anchor_regex.captures(line) {
            if let Some(anchor) = caps.get(1).or_else(|| caps.get(2)) {
                anchors.insert(anchor.as_str().to_string(), offset);
            }
        }
    }
    
    anchors
}
```

#### 2. 标题匹配鲁棒化

**标准化函数**：
```rust
fn normalize_title(title: &str) -> String {
    title.chars()
        .map(|c| match c {
            '０'..='９' => char::from_u32(c as u32 - '０' as u32 + '0' as u32).unwrap(),
            'Ａ'..='Ｚ' => char::from_u32(c as u32 - 'Ａ' as u32 + 'A' as u32).unwrap(),
            'ａ'..='ｚ' => char::from_u32(c as u32 - 'ａ' as u32 + 'a' as u32).unwrap(),
            '，' => ',', '。' => '.', '！' => '!', '？' => '?',
            _ => c,
        })
        .collect::<String>()
        .trim()
        .to_lowercase()
        .split_whitespace()
        .collect::<Vec<_>>()
        .join(" ")
}
```

**模糊匹配**：
```rust
fn fuzzy_match_title(md_content: &str, target: &str, threshold: f32) -> Option<usize> {
    use edit_distance::edit_distance;
    
    let normalized_target = normalize_title(target);
    let mut best_match = None;
    let mut best_score = threshold;
    
    for (offset, line) in scan_lines_with_offset(md_content) {
        if line.starts_with('#') {
            let line_title = normalize_title(&line[1..].trim());
            let max_len = normalized_target.len().max(line_title.len());
            if max_len > 0 {
                let distance = edit_distance(&normalized_target, &line_title);
                let similarity = 1.0 - (distance as f32 / max_len as f32);
                if similarity > best_score {
                    best_score = similarity;
                    best_match = Some(offset);
                }
            }
        }
    }
    
    best_match
}
```

### P2任务：后期优化

#### 1. 自适应重叠策略

```rust
#[derive(Debug, Clone)]
pub enum ContentType {
    Technical,  // 技术文档，需要更多上下文
    Narrative,  // 叙事文本，可以减少重叠
    Reference,  // 参考资料，需要更多重叠
    Code,       // 代码文档，重叠较少
}

impl ContentType {
    pub fn detect(md_content: &str) -> Self {
        let lines: Vec<&str> = md_content.lines().collect();
        let total = lines.len();
        
        let code_lines = lines.iter().filter(|line| {
            line.starts_with("```") || line.starts_with("    ")
        }).count();
        
        if code_lines as f32 / total as f32 > 0.3 {
            Self::Code
        } else if has_technical_keywords(md_content) {
            Self::Technical
        } else {
            Self::Narrative
        }
    }
    
    pub fn overlap_ratio(&self) -> f32 {
        match self {
            Self::Technical => 0.25,
            Self::Narrative => 0.15,
            Self::Reference => 0.30,
            Self::Code => 0.10,
        }
    }
}
```

#### 2. 配置统一化

```rust
#[derive(Clone)]
pub struct ChunkingConfig {
    pub max_tokens: usize,
    pub overlap_ratio: f32,
    pub fuzzy_threshold: f32,
    pub content_type: ContentType,
}

impl ChunkingConfig {
    pub fn adaptive(md_content: &str) -> Self {
        let content_type = ContentType::detect(md_content);
        Self {
            max_tokens: 480,  // 与向量器一致
            overlap_ratio: content_type.overlap_ratio(),
            fuzzy_threshold: 0.8,
            content_type,
        }
    }
}
```

## 📋 实施检查表

### 第一周
- [ ] 实现`ChunkWithOffset`结构体
- [ ] 修改`chunk_md_file_with_offsets`函数
- [ ] 编写基础单元测试
- [ ] 集成指标收集系统

### 第二-三周
- [ ] 精确TOC归属算法
- [ ] 完善测试覆盖
- [ ] 性能基准测试
- [ ] 日志和监控集成

### 第四-六周
- [ ] 锚点解析器实现
- [ ] 标题标准化匹配
- [ ] 模糊匹配集成
- [ ] 重复标题处理

### 第七-八周
- [ ] 自适应配置系统
- [ ] 配置参数统一
- [ ] 性能优化
- [ ] 文档完善

## ⚠️ 关键注意事项

### 向后兼容
- 保持原有函数接口不变
- 新功能作为可选参数添加
- 确保现有调用方不受影响

### 性能考虑
- offset计算的额外开销最小化
- 模糊匹配只在标准匹配失败时使用
- 大文档的流式处理

### 测试策略
- 每个功能都要有对应的测试用例
- 边界情况和异常情况全覆盖
- 性能回归测试必备

### 错误处理
- 优雅降级，确保系统不崩溃
- 详细的错误日志和指标记录
- 多层回退策略

这个实施指南提供了具体的代码实现和检查清单，确保改进工作按计划有序进行。 