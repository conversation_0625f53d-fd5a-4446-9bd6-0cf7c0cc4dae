# Tauri 前后端交互和事件机制详解
 
## 概述

本项目采用 Tauri 框架构建桌面应用，实现了 TypeScript 前端与 Rust 后端的高效交互。系统通过命令调用机制和事件系统实现双向通信，同时利用插件架构扩展核心功能。

## 架构概览

```
┌─────────────────────────────────┐
│       Frontend (TypeScript)     │
│  - React Components             │
│  - Service Layer                │
│  - State Management             │
└─────────────────────────────────┘
              │ ↑
              │ │ invoke() / listen()
              │ │ Commands / Events  
              ↓ │
┌─────────────────────────────────┐
│       Tauri Runtime             │
│  - Command Router               │
│  - Event System                 │
│  - Plugin Management            │
└─────────────────────────────────┘
              │ ↑
              │ │ Function Calls / Emitter
              │ │ 
              ↓ │
┌─────────────────────────────────┐
│       Backend (Rust)            │
│  - Core Commands                │
│  - Database Operations          │
│  - Plugin Commands              │
│  - Event Emission               │
└─────────────────────────────────┘
```

## 命令调用机制 (Commands)

### 1. 后端命令定义

#### 核心命令注册

在 `src-tauri/src/lib.rs` 中注册所有命令：

```rust
.invoke_handler(tauri::generate_handler![
    // 线程管理
    create_thread,
    edit_thread,
    get_latest_thread_by_book_key,
    
    // 书籍管理  
    save_book,
    get_books,
    get_book_by_id,
    update_book,
    delete_book,
    get_book_status,
    update_book_status,
    get_books_with_status,
    
    // 标签管理
    create_tag,
    get_tags,
    get_tag_by_id,
    get_tag_by_name,
    update_tag,
    delete_tag,
    
    // LLama 相关
    greet,
    get_app_data_dir,
    get_llamacpp_backend_path,
    ensure_llamacpp_directories,
    download_llama_server,
    llama_server_binary_name_cmd,
])
```

#### 命令实现示例

```rust
// 书籍保存命令
#[tauri::command]
pub async fn save_book(app_handle: AppHandle, data: BookUploadData) -> Result<SimpleBook, String> {
    let db_pool = get_db_pool(&app_handle).await?;

    // 检查是否已存在
    let existing_book = get_book_by_id(app_handle.clone(), data.id.clone()).await?;
    if let Some(book) = existing_book {
        return Err(format!("书籍已存在: {} (ID: {})", book.title, book.id));
    }

    // 创建目录和文件处理
    let app_data_dir = app_handle.path().app_data_dir()
        .map_err(|e| format!("获取应用目录失败: {}", e))?;

    let books_dir = app_data_dir.join("books");
    let book_dir = books_dir.join(&data.id);
    fs::create_dir_all(&book_dir).map_err(|e| format!("创建目录失败: {}", e))?;

    // 数据库操作
    let mut tx = db_pool.begin().await
        .map_err(|e| format!("开启事务失败: {}", e))?;

    sqlx::query(/* SQL 插入语句 */)
        .bind(&data.id)
        .bind(&data.title)
        // ...其他参数
        .execute(&mut *tx)
        .await
        .map_err(|e| format!("数据库插入失败: {}", e))?;

    tx.commit().await.map_err(|e| format!("事务提交失败: {}", e))?;

    Ok(SimpleBook {
        id: data.id,
        title: data.title,
        // ...其他字段
    })
}
```

### 2. 前端命令调用

#### 服务层封装

在 `bookService.ts` 中封装命令调用：

```typescript
import { invoke } from "@tauri-apps/api/core";

export async function uploadBook(file: File): Promise<SimpleBook> {
  try {
    // 预处理文件数据
    const bookHash = await partialMD5(file);
    const metadata = await extractMetadataOnly(file);
    
    // 构建上传数据
    const uploadData: BookUploadData = {
      id: bookHash,
      title: formatTitle(metadata.title) || getFileNameWithoutExt(file.name),
      author: formatAuthors(metadata.author) || "Unknown",
      format: getBookFormat(file.name),
      fileSize: file.size,
      language: getPrimaryLanguage(metadata.language) || "en",
      tempFilePath: tempFilePath,
      coverTempFilePath,
      metadata: metadata,
    };

    // 调用后端命令
    const result = await invoke<SimpleBook>("save_book", { data: uploadData });
    return result;
  } catch (error) {
    console.error("书籍上传失败:", error);
    throw new Error(`上传失败: ${error instanceof Error ? error.message : "未知错误"}`);
  }
}

export async function getBooksWithStatus(options?: BookQueryOptions): Promise<BookWithStatus[]> {
  try {
    const result = await invoke<BookWithStatus[]>("get_books_with_status", { options });
    return result;
  } catch (error) {
    console.error("获取书籍状态失败:", error);
    throw error;
  }
}

export async function deleteBook(id: string): Promise<void> {
  try {
    await invoke("delete_book", { id });
  } catch (error) {
    console.error("删除书籍失败:", error);
    throw error;
  }
}
```

#### 标签管理服务

```typescript
// tagService.ts
export async function createTag(data: CreateTagData): Promise<Tag> {
  try {
    const result = await invoke<Tag>("create_tag", { data });
    return result;
  } catch (error) {
    console.error("创建标签失败:", error);
    throw error;
  }
}

export async function getTags(): Promise<Tag[]> {
  try {
    const result = await invoke<Tag[]>("get_tags");
    return result;
  } catch (error) {
    console.error("获取标签失败:", error);
    throw error;
  }
}

export async function deleteTag(id: string): Promise<void> {
  try {
    await invoke("delete_tag", { id });
  } catch (error) {
    console.error("删除标签失败:", error);
    throw error;
  }
}
```

## 事件系统机制 (Events)

### 1. 后端事件发送

#### 事件发送示例

在 `tauri-plugin-epub/src/commands.rs` 中：

```rust
use tauri::{AppHandle, Emitter, Manager, Runtime, State};

#[tauri::command]
pub async fn index_epub<R: Runtime>(
    app: AppHandle<R>,
    state: State<'_, EpubState>,
    book_id: String,
    opts: ProcessOptions,
) -> Result<IndexResult, String> {
    // 克隆 app handle 用于事件发送
    let app_for_emit = app.clone();
    let book_id_for_emit = book_id.clone();

    // 进度回调函数
    let progress_callback = move |update: ProgressUpdate| {
        // 构造事件负载
        let payload = serde_json::json!({
            "book_id": book_id_for_emit.clone(),
            "current": update.current,
            "total": update.total,
            "percent": update.percent,
            "chapter_title": update.chapter_title,
            "chunk_index": update.chunk_index,
        });

        // 发送事件到前端
        let _ = app_for_emit.emit("epub://index-progress", payload)
            .map_err(|e| log::error!("发送进度事件失败: {}", e));
    };

    // 执行处理流程，传入进度回调
    match process_epub_to_db(&book_dir, opts, Some(progress_callback)).await {
        Ok(report) => {
            Ok(IndexResult {
                success: true,
                message: "索引完成".to_string(),
                report: Some(report.into()),
            })
        }
        Err(e) => {
            Err(format!("索引失败: {}", e))
        }
    }
}
```

#### 处理流水线中的进度发送

在 `pipeline.rs` 中：

```rust
pub async fn process_epub_to_db<P: AsRef<Path>, F>(
    book_dir: P,
    opts: ProcessOptions,
    mut on_progress: Option<F>,
) -> Result<ProcessReport>
where
    F: FnMut(ProgressUpdate),
{
    // ... 初始化代码

    for (chapter_index, chapter) in content.chapters.iter().enumerate() {
        let chunks = chunk_text(&chapter.content, chunk_size);
        
        for (chunk_index, chunk_text) in chunks.iter().enumerate() {
            // 计算当前进度
            let current = processed_chunks + 1;
            let percent = (current as f32 / total_chunks as f32) * 100.0;

            // 发送进度更新
            if let Some(ref mut callback) = on_progress {
                callback(ProgressUpdate {
                    current,
                    total: total_chunks,
                    percent,
                    chapter_title: chapter.title.clone(),
                    chunk_index,
                });
            }

            // 处理文本块向量化
            let vector = vectorizer.vectorize_text(chunk_text).await?;
            
            // 保存到数据库
            database.insert_chunk(&chunk).await?;
            
            processed_chunks += 1;
        }
    }

    Ok(ProcessReport {
        db_path: db_path.to_path_buf(),
        book_title: content.title,
        book_author: content.author,
        total_chunks: processed_chunks,
        vector_dimension: opts.dimension,
    })
}
```

### 2. 前端事件监听

#### 监听向量化进度

在 `BookItem.tsx` 中：

```typescript
import { listen } from "@tauri-apps/api/event";

export default function BookItem({ book }: BookItemProps) {
  const [vectorizeProgress, setVectorizeProgress] = useState<number | null>(null);

  // 监听向量化进度事件
  useEffect(() => {
    let unlisten: (() => void) | null = null;
    
    (async () => {
      const off = await listen<{
        book_id: string;
        current: number;
        total: number;
        percent: number;
        chapter_title: string;
        chunk_index: number;
      }>("epub://index-progress", (e) => {
        const p = e.payload;
        // 只处理当前书籍的进度
        if (p && p.book_id === book.id) {
          setVectorizeProgress(Math.max(0, Math.min(100, Math.round(p.percent))));
        }
      });
      unlisten = off;
    })();
    
    // 清理监听器
    return () => {
      if (unlisten) unlisten();
    };
  }, [book.id]);

  return (
    <div className="book-item">
      {/* 书籍信息显示 */}
      
      {/* 进度条显示 */}
      {vectorizeProgress !== null && (
        <div className="progress-bar">
          <div 
            className="progress-fill" 
            style={{ width: `${vectorizeProgress}%` }}
          />
          <span>{vectorizeProgress}%</span>
        </div>
      )}
    </div>
  );
}
```

#### 事件类型定义

```typescript
// 定义事件负载类型
interface IndexProgressPayload {
  book_id: string;
  current: number;
  total: number;
  percent: number;
  chapter_title: string;
  chunk_index: number;
}

// 类型安全的事件监听
const listenToIndexProgress = async (callback: (payload: IndexProgressPayload) => void) => {
  return await listen<IndexProgressPayload>("epub://index-progress", (event) => {
    callback(event.payload);
  });
};
```

## 插件架构系统

### 1. EPUB 插件

#### 插件结构

```
tauri-plugin-epub/
├── src/
│   ├── commands.rs        # 插件命令定义
│   ├── database.rs        # 向量数据库操作
│   ├── epub_reader.rs     # EPUB文件解析
│   ├── pipeline.rs        # 处理流水线
│   ├── vectorizer.rs      # 文本向量化
│   ├── parse_toc.rs       # 目录解析
│   └── lib.rs            # 插件入口
├── Cargo.toml
└── permissions/          # 权限配置
```

#### 插件命令注册

```rust
// tauri-plugin-epub/src/lib.rs
use tauri::{
    plugin::{Builder, TauriPlugin},
    Runtime,
};

pub use commands::*;
mod commands;
mod database;
mod epub_reader;
mod pipeline;
mod state;
mod vectorizer;
mod parse_toc;

pub fn init<R: Runtime>() -> TauriPlugin<R> {
    Builder::new("epub")
        .invoke_handler(tauri::generate_handler![
            parse_epub,
            index_epub,
            convert_to_mdbook,
            parse_toc,
            search_db,
            get_chunk_with_context,
            get_toc_chunks,
            get_chunks_by_range,
        ])
        .setup(|app, api| {
            // 插件初始化逻辑
            Ok(())
        })
        .build()
}
```

#### 插件命令实现

```rust
// commands.rs
#[tauri::command]
pub async fn search_db<R: Runtime>(
    app: AppHandle<R>,
    book_id: String,
    question: String,
    limit: Option<usize>,
) -> Result<Vec<DocumentChunk>, String> {
    let app_data_dir = app.path().app_data_dir().map_err(|e| e.to_string())?;
    let db_path = app_data_dir.join("books").join(&book_id).join("vector.db");
    
    let database = VectorDatabase::new(&db_path).await
        .map_err(|e| format!("打开数据库失败: {}", e))?;
    
    let results = database.search(&question, limit.unwrap_or(5)).await
        .map_err(|e| format!("向量搜索失败: {}", e))?;
    
    Ok(results)
}

#[tauri::command] 
pub async fn get_chunk_with_context<R: Runtime>(
    app: AppHandle<R>,
    book_id: String,
    chunk_id: i64,
    prev_count: Option<usize>,
    next_count: Option<usize>,
) -> Result<Vec<DocumentChunk>, String> {
    let app_data_dir = app.path().app_data_dir().map_err(|e| e.to_string())?;
    let db_path = app_data_dir.join("books").join(&book_id).join("vector.db");
    
    let database = VectorDatabase::new(&db_path).await
        .map_err(|e| format!("打开数据库失败: {}", e))?;
    
    let results = database.get_chunk_with_context(
        chunk_id, 
        prev_count.unwrap_or(2), 
        next_count.unwrap_or(2)
    ).await
        .map_err(|e| format!("获取上下文失败: {}", e))?;
    
    Ok(results)
}
```

### 2. LlamaCPP 插件

#### 插件功能

- LLM 模型管理 
- 进程生命周期管理
- API 服务器启动/停止
- 设备信息获取

#### 命令定义

```rust
// tauri-plugin-llamacpp/src/commands.rs
#[tauri::command]
pub async fn load_llama_model<R: Runtime>(
    app_handle: tauri::AppHandle<R>,
    backend_path: &str,
    library_path: Option<&str>,
    mut args: Vec<String>,
) -> ServerResult<SessionInfo> {
    let state: State<LlamacppState> = app_handle.state();
    let mut process_map = state.llama_server_process.lock().await;

    // 验证路径
    validate_binary_path(backend_path)?;
    
    // 配置命令
    let mut command = Command::new(backend_path);
    command.args(args);
    setup_library_path(library_path, &mut command);
    
    // 启动进程
    let mut child = command.spawn().map_err(ServerError::Io)?;
    
    // 监控进程状态
    let (ready_tx, mut ready_rx) = mpsc::channel::<bool>(1);
    
    // 等待服务器就绪
    tokio::select! {
        _ = tokio::time::sleep(Duration::from_secs(30)) => {
            return Err(ServerError::Timeout);
        }
        result = ready_rx.recv() => {
            match result {
                Some(true) => {
                    // 服务器启动成功
                    let session_info = SessionInfo {
                        pid: child.id().unwrap(),
                        port,
                        model_path: model_path_pb.to_string_lossy().to_string(),
                        // ...其他信息
                    };
                    
                    process_map.insert(session_info.pid, child);
                    Ok(session_info)
                }
                _ => Err(ServerError::StartupFailed),
            }
        }
    }
}
```

### 3. 前端插件调用

#### RAG 工具集成

```typescript
// ragSearch.ts
import { invoke } from "@tauri-apps/api/core";

export const ragSearch = tool({
  description: "在当前图书中进行向量检索",
  parameters: z.object({
    reasoning: z.string().min(1).describe("调用此工具的原因和目的"),
    question: z.string().min(1).describe("用户的问题"),
    limit: z.number().int().min(1).max(20).default(5).describe("返回的片段数量"),
    format: z.boolean().default(true).describe("是否返回格式化的上下文文本"),
  }),
  execute: async ({ reasoning, question, limit, format }) => {
    const activeBookId = useActiveBookStore.getState().activeBookId;
    if (!activeBookId) {
      return {
        type: "text" as const,
        text: "错误：没有活动的书籍",
      };
    }

    try {
      // 调用插件命令进行向量搜索
      const results = (await invoke("plugin:epub|search_db", {
        bookId: activeBookId,
        question,
        limit,
      })) as SearchResult[];

      if (format) {
        const lines: string[] = [];
        lines.push(`[RAG检索结果] 找到 ${results.length} 个相关片段：`);
        lines.push(`💭 调用原因：${reasoning}\n`);
        
        results.forEach((result, index) => {
          lines.push(`## 片段 ${index + 1} (相似度: ${(result.similarity * 100).toFixed(1)}%)`);
          lines.push(`📖 章节: ${result.chapter_title}`);
          lines.push(`📍 位置: 全局索引 ${result.global_chunk_index}`);
          lines.push(`💬 内容:\n${result.content}\n`);
        });

        return {
          type: "text" as const,
          text: lines.join("\n"),
        };
      }

      return {
        type: "text" as const,
        text: JSON.stringify(results, null, 2),
        meta: {
          reasoning,
          total_found: results.length,
          book_id: activeBookId,
          query: question,
        },
      };
    } catch (error) {
      return {
        type: "text" as const,
        text: `搜索失败: ${error}`,
      };
    }
  },
});
```

## 窗口生命周期管理

### 1. 窗口事件处理

```rust
// lib.rs
.on_window_event(|window, event| {
    if let tauri::WindowEvent::CloseRequested { .. } = event {
        let app_handle = window.app_handle().clone();
        tauri::async_runtime::spawn(async move {
            // 清理 llamacpp 进程
            if let Err(e) = tauri_plugin_llamacpp::cleanup_llama_processes(app_handle).await {
                log::error!("清理 llamacpp 进程失败: {}", e);
            }
        });
    }
})
```

### 2. 应用状态管理

```rust
// state.rs
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::Mutex;

#[derive(Default)]
pub struct AppState {
    pub db_pool: Arc<Mutex<Option<sqlx::SqlitePool>>>,
    pub active_books: Arc<Mutex<HashMap<String, BookState>>>,
}

pub struct BookState {
    pub processing: bool,
    pub last_accessed: chrono::DateTime<chrono::Utc>,
}
```

## 错误处理机制

### 1. 后端错误处理

```rust
// 自定义错误类型
#[derive(Debug, thiserror::Error)]
pub enum AppError {
    #[error("数据库错误: {0}")]
    Database(#[from] sqlx::Error),
    
    #[error("文件系统错误: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("序列化错误: {0}")]
    Serialization(#[from] serde_json::Error),
    
    #[error("业务逻辑错误: {message}")]
    Business { message: String },
}

// 命令错误处理
#[tauri::command]
pub async fn example_command(data: String) -> Result<String, String> {
    match process_data(data).await {
        Ok(result) => Ok(result),
        Err(AppError::Database(e)) => {
            log::error!("数据库操作失败: {}", e);
            Err("数据库操作失败".to_string())
        }
        Err(AppError::Business { message }) => {
            Err(message)
        }
        Err(e) => {
            log::error!("未知错误: {}", e);
            Err("内部服务器错误".to_string())
        }
    }
}
```

### 2. 前端错误处理

```typescript
// 统一错误处理
export async function safeInvoke<T>(
  command: string, 
  args?: Record<string, any>
): Promise<T> {
  try {
    return await invoke<T>(command, args);
  } catch (error) {
    console.error(`命令调用失败: ${command}`, error);
    
    // 根据错误类型进行处理
    if (typeof error === 'string') {
      if (error.includes('数据库')) {
        toast.error('数据库操作失败，请稍后重试');
      } else if (error.includes('网络')) {
        toast.error('网络连接错误，请检查网络设置');
      } else {
        toast.error(error);
      }
    } else {
      toast.error('未知错误，请联系技术支持');
    }
    
    throw error;
  }
}

// 在服务层使用
export async function createBook(data: BookData): Promise<Book> {
  return await safeInvoke<Book>('create_book', { data });
}
```

## 性能优化策略

### 1. 命令调用优化

```typescript
// 批量操作
export async function batchUpdateBooks(updates: BookUpdate[]): Promise<void> {
  // 避免逐个调用，使用批量操作
  await invoke('batch_update_books', { updates });
}

// 缓存机制
const commandCache = new Map<string, { data: any; timestamp: number }>();

export async function cachedInvoke<T>(
  command: string, 
  args?: Record<string, any>,
  cacheTimeout = 5000
): Promise<T> {
  const cacheKey = `${command}:${JSON.stringify(args)}`;
  const cached = commandCache.get(cacheKey);
  
  if (cached && Date.now() - cached.timestamp < cacheTimeout) {
    return cached.data;
  }
  
  const result = await invoke<T>(command, args);
  commandCache.set(cacheKey, { data: result, timestamp: Date.now() });
  
  return result;
}
```

### 2. 事件处理优化

```typescript
// 事件防抖
const debouncedProgressHandler = debounce((payload: ProgressPayload) => {
  setProgress(payload.percent);
}, 100);

// 事件监听管理
class EventManager {
  private listeners = new Map<string, () => void>();
  
  async listen<T>(event: string, handler: (payload: T) => void): Promise<void> {
    if (this.listeners.has(event)) {
      this.listeners.get(event)!(); // 清理旧监听器
    }
    
    const unlisten = await listen<T>(event, ({ payload }) => {
      handler(payload);
    });
    
    this.listeners.set(event, unlisten);
  }
  
  cleanup(): void {
    for (const unlisten of this.listeners.values()) {
      unlisten();
    }
    this.listeners.clear();
  }
}
```

这种 Tauri 集成架构提供了高效的前后端通信机制，支持复杂的桌面应用需求，同时保持了良好的性能和可维护性。