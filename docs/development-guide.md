# Tauri电子书阅读器 - 开发指南

## 快速开始

### 环境要求

#### 必需环境
- **Node.js**: 18.x 或更高版本
- **Rust**: 1.70 或更高版本
- **pnpm**: 8.x 或更高版本
- **Tauri CLI**: 2.x 版本

#### 平台特定要求

**Windows:**
- Visual Studio Build Tools 2019/2022
- Windows SDK

**macOS:**
- Xcode Command Line Tools
- macOS 10.15+ 

**Linux (Ubuntu/Debian):**
```bash
sudo apt update
sudo apt install libwebkit2gtk-4.0-dev build-essential curl wget libssl-dev libgtk-3-dev libayatana-appindicator3-dev librsvg2-dev
```

### 项目安装

1. **克隆项目**
```bash
git clone <repository-url>
cd tauri-app
```

2. **安装依赖**
```bash
# 安装根目录依赖
pnpm install

# 安装前端依赖
cd packages/app
pnpm install

# 返回根目录
cd ../..
```

3. **Rust环境检查**
```bash
# 检查Rust版本
rustc --version
cargo --version

# 安装Tauri CLI（如未安装）
cargo install tauri-cli@^2.0.0
```

4. **首次构建**
```bash
# 开发模式运行
pnpm dev

# 或者
cd packages/app
pnpm tauri dev
```

## 项目结构详解

```
tauri-app/
├── packages/
│   ├── app/                    # 主应用程序
│   │   ├── src/               # 前端源码
│   │   │   ├── components/    # React组件
│   │   │   ├── pages/         # 页面组件
│   │   │   ├── store/         # Zustand状态管理
│   │   │   ├── hooks/         # 自定义Hooks
│   │   │   ├── services/      # 业务逻辑服务
│   │   │   ├── ai/            # AI集成模块
│   │   │   └── utils/         # 工具函数
│   │   ├── src-tauri/         # Rust后端
│   │   │   ├── src/           # Rust源码
│   │   │   │   ├── core/      # 核心业务逻辑
│   │   │   │   │   ├── books/ # 书籍管理
│   │   │   │   │   ├── tags/  # 标签管理
│   │   │   │   │   └── threads/ # 对话管理
│   │   │   │   └── lib.rs     # 主入口
│   │   │   ├── plugins/       # 自定义插件
│   │   │   │   ├── tauri-plugin-epub/     # EPUB处理
│   │   │   │   └── tauri-plugin-llamacpp/ # LLM集成
│   │   │   ├── Cargo.toml     # Rust依赖配置
│   │   │   └── tauri.conf.json # Tauri配置
│   │   ├── package.json       # 前端依赖配置
│   │   └── vite.config.ts     # Vite构建配置
│   ├── app-tabs/              # 标签组件库
│   └── foliate-js/            # 电子书渲染引擎
├── docs/                      # 项目文档
├── pnpm-workspace.yaml        # Monorepo配置
└── package.json               # 根配置文件
```

## 开发流程

### 开发模式启动

1. **启动开发服务器**
```bash
# 方式1：从根目录启动
pnpm dev

# 方式2：从app目录启动
cd packages/app
pnpm tauri dev
```

2. **开发服务器特性**
- **热重载**: 前端代码修改自动刷新
- **Rust重编译**: 后端代码修改自动重编译重启
- **调试端口**: 前端运行在 http://localhost:1420
- **开发者工具**: 右键检查元素或 `Ctrl+Shift+I`

### 代码结构规范

#### 前端代码规范

1. **组件命名**
```typescript
// 页面组件：PascalCase + Page后缀
export default function LibraryPage() {}

// 普通组件：PascalCase
export function BookItem({ book }: { book: Book }) {}

// Hook：camelCase + use前缀
export function useBookUpload() {}
```

2. **文件组织**
```typescript
// 页面组件目录结构
pages/library/
├── index.tsx              # 页面主入口
├── components/            # 页面专用组件
│   ├── BookItem.tsx
│   └── Sidebar.tsx
└── hooks/                 # 页面专用Hooks
    ├── useBooksFilter.ts
    └── useBooksOperations.ts
```

3. **状态管理模式**
```typescript
// Zustand全局状态（跨页面共享）
export const useLibraryStore = create<LibraryState>((set, get) => ({
  books: [],
  isLoading: false,
  
  // Actions
  refreshBooks: async () => {
    set({ isLoading: true });
    try {
      const books = await getBooksWithStatus();
      set({ books });
    } finally {
      set({ isLoading: false });
    }
  },
}));

// Jotai原子状态（页面内精细化管理）
export const selectedBookAtom = atom<Book | null>(null);
export const bookSettingsAtom = atom<BookSettings>({});
```

#### 后端代码规范

1. **模块组织**
```rust
// 每个业务模块包含三个文件
src/core/books/
├── mod.rs        # 模块声明和导出
├── models.rs     # 数据模型定义
└── commands.rs   # Tauri命令实现
```

2. **错误处理模式**
```rust
// 统一的Result类型
type CommandResult<T> = Result<T, String>;

#[tauri::command]
pub async fn save_book(data: BookUploadData) -> CommandResult<SimpleBook> {
    // 业务逻辑...
    Ok(book)
}

// 错误转换工具
fn db_error(err: sqlx::Error) -> String {
    format!("数据库操作失败: {}", err)
}
```

3. **数据库操作模式**
```rust
// 使用事务确保数据一致性
let mut tx = pool.begin().await.map_err(db_error)?;

sqlx::query("INSERT INTO books (...) VALUES (...)")
    .bind(...)
    .execute(&mut *tx)
    .await
    .map_err(db_error)?;

sqlx::query("INSERT INTO book_status (...) VALUES (...)")
    .bind(...)
    .execute(&mut *tx)
    .await
    .map_err(db_error)?;

tx.commit().await.map_err(db_error)?;
```

### 调试方法

#### 前端调试

1. **浏览器开发者工具**
```typescript
// 在代码中添加断点
debugger;

// 控制台日志
console.log('状态变化:', { oldState, newState });
console.error('请求失败:', error);

// 性能分析
console.time('书籍加载');
await loadBooks();
console.timeEnd('书籍加载');
```

2. **React DevTools**
- 安装浏览器扩展
- 查看组件层级和状态
- 性能分析和重渲染检测

3. **Zustand DevTools**
```typescript
import { devtools } from 'zustand/middleware';

export const useLibraryStore = create<LibraryState>()(
  devtools(
    (set, get) => ({
      // store implementation
    }),
    { name: 'library-store' }
  )
);
```

#### 后端调试

1. **日志调试**
```rust
// 在Cargo.toml中启用日志
[dependencies]
log = "0.4"
env_logger = "0.10"

// 代码中使用
log::info!("处理书籍上传: {}", book_title);
log::warn!("文件大小超出限制: {} bytes", file_size);
log::error!("数据库连接失败: {}", error);
```

2. **Rust调试器**
```bash
# 使用rust-gdb（Linux）
rust-gdb target/debug/tauri-app

# 使用lldb（macOS）
rust-lldb target/debug/tauri-app

# VSCode调试配置（.vscode/launch.json）
{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug Tauri App",
      "cargo": {
        "args": ["build", "--bin", "tauri-app"],
        "filter": {
          "name": "tauri-app",
          "kind": "bin"
        }
      }
    }
  ]
}
```

3. **数据库调试**
```bash
# 直接查看SQLite数据库
sqlite3 ~/.local/share/tauri-app/database/app.db

# 查看表结构
.schema

# 查询数据
SELECT * FROM books LIMIT 5;
SELECT * FROM book_status WHERE status = 'reading';
```

#### 插件调试

1. **EPUB插件调试**
```rust
// 在插件中添加详细日志
log::debug!("开始解析EPUB: {:?}", epub_path);
log::info!("提取到 {} 个章节", chapters.len());

// 测试向量化功能
#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_vectorize_text() {
        let vectorizer = TextVectorizer::new(test_config()).await.unwrap();
        let result = vectorizer.vectorize_text("测试文本").await;
        assert!(result.is_ok());
    }
}
```

2. **LLama插件调试**
```rust
// 监控进程启动
log::info!("启动LLama服务器: {:?}", command);

// 捕获stdout/stderr
tokio::spawn(async move {
    let mut reader = BufReader::new(stdout);
    let mut line = String::new();
    while let Ok(_) = reader.read_line(&mut line).await {
        log::debug!("[llamacpp stdout] {}", line.trim());
        line.clear();
    }
});
```

## 构建和发布

### 开发构建

```bash
# 前端构建
cd packages/app
pnpm build

# Rust构建
pnpm tauri build --debug
```

### 生产构建

```bash
# 完整生产构建
pnpm build

# 或分步构建
cd packages/app
pnpm build
pnpm tauri build
```

### 构建配置

**Tauri配置 (tauri.conf.json)**
```json
{
  "productName": "TauriReader",
  "version": "0.1.0",
  "identifier": "com.example.taurireader",
  "build": {
    "beforeDevCommand": "pnpm dev",
    "beforeBuildCommand": "pnpm build",
    "devUrl": "http://localhost:1420",
    "frontendDist": "../dist"
  },
  "bundle": {
    "active": true,
    "targets": ["deb", "appimage", "nsis", "dmg", "app"],
    "icon": [
      "icons/32x32.png",
      "icons/128x128.png",
      "icons/icon.icns",
      "icons/icon.ico"
    ]
  }
}
```

**Cargo.toml优化配置**
```toml
[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true
```

## 常见问题解决

### 环境问题

1. **Rust版本过低**
```bash
# 更新Rust
rustup update

# 检查版本
rustc --version
```

2. **Node.js版本问题**
```bash
# 使用nvm管理Node版本
nvm install 18
nvm use 18
```

3. **权限问题（Linux/macOS）**
```bash
# 给予执行权限
chmod +x src-tauri/target/debug/tauri-app
```

### 构建问题

1. **WebKit依赖缺失（Linux）**
```bash
# Ubuntu/Debian
sudo apt install libwebkit2gtk-4.0-dev

# Arch Linux
sudo pacman -S webkit2gtk

# CentOS/RHEL
sudo yum install webkit2gtk3-devel
```

2. **Tauri构建失败**
```bash
# 清理构建缓存
cd packages/app
rm -rf src-tauri/target
pnpm tauri build
```

3. **前端构建错误**
```bash
# 清理node_modules
rm -rf node_modules
rm -rf packages/app/node_modules
pnpm install
```

### 运行时问题

1. **数据库连接失败**
- 检查应用数据目录权限
- 确认SQLite扩展加载
- 查看日志输出

2. **AI功能不工作**
- 检查API密钥配置
- 验证网络连接
- 查看控制台错误信息

3. **EPUB解析失败**
- 确认文件格式正确
- 检查文件完整性
- 查看插件日志输出

### 性能问题

1. **启动缓慢**
- 检查是否为debug模式
- 优化数据库查询
- 减少初始化时的数据加载

2. **内存占用高**
- 检查是否存在内存泄漏
- 优化大文件处理逻辑
- 使用分页和懒加载

3. **向量化速度慢**
- 调整批处理大小
- 检查API响应时间
- 考虑使用本地模型

## 贡献指南

### 代码提交规范

1. **提交信息格式**
```bash
type(scope): 简短描述

详细描述（可选）

# 类型说明
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式
refactor: 重构
perf: 性能优化
test: 测试相关
chore: 构建配置等
```

2. **分支命名规范**
```bash
feature/书籍管理优化
bugfix/EPUB解析错误
hotfix/紧急安全修复
```

3. **代码审查要点**
- 代码风格一致性
- 错误处理完整性
- 性能影响评估
- 测试覆盖率

### 开发最佳实践

1. **前端开发**
- 组件单一职责
- 避免props drilling
- 合理使用状态管理
- 性能优化考虑

2. **后端开发**
- 错误处理完整
- 数据库事务使用
- 日志记录规范
- 安全性考虑

3. **测试编写**
- 单元测试覆盖
- 集成测试重点
- 边界条件测试
- 性能基准测试 