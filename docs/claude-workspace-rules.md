# Claude工作区规则配置文档

## 概述

本文档记录了用户为Claude配置的所有工作区规则，这些规则会影响Claude在Cursor环境中的行为模式。

## 始终应用的工作区规则 (always_applied_workspace_rules)

### MCP Interactive Feedback 规则

**规则内容**:
```
注意根据mcp-feedback-termianl工具的要求在任务完成前进行工具调用。在任务完成前一定要调用interactive_feedback 工具来提醒我检查问题
```

**影响**:
- 强制Claude在每个步骤完成后调用feedback工具
- 创建暂停确认的协作节奏
- 确保用户能及时审查和指导工作进程

### 命令执行限制规则

**禁止执行的命令**:
- `pnpm dev` / `npm run dev`
- `pnpm start` / `npm start`
- `pnpm test` / `npm test`
- 任何启动开发服务器或测试的命令

**其他限制**:
- 不要验证修改是否正确，用户会自己验证
- 不要主动新建分支
- 不要主动使用git提交代码
- 不要修复css类排序的问题

**原理**: 用户会自己执行这些命令来验证结果

### Tailwind CSS 要求

**规则**: 使用neutral族的颜色，包括背景色、字体颜色

**影响**: 在涉及CSS样式时，优先使用neutral色系

## 用户规则 (user_rules)

### 1. 行动导向规则
```
Never ask: "Would you like me to make this change for you?". Just do it.
```
**影响**: Claude会直接执行操作而不询问确认

### 2. 包管理器规则
```
Always use pnpm as package manager
```
**影响**: 所有包管理操作都使用pnpm而不是npm或yarn

### 3. Git分支规则
```
create a new branch with git new (not git checkout/switch)
```
**影响**: 创建分支时使用`git new`命令

### 4. 包安装规则
```
when installing packages with pnpm always use pnpm add <packagename>@latest
```
**影响**: 安装包时总是使用最新版本

### 5. 提交信息规则
```
Commit message style: Use concise English format with conventional commit prefixes (feat/fix/refactor/docs/style/test/chore). Start with brief description, followed by 2-4 bullet points highlighting key changes. Avoid verbose explanations and keep total message under 5 lines.
```
**格式要求**:
- 使用conventional commit前缀
- 简洁的英文格式
- 简短描述 + 2-4个要点
- 避免冗长解释
- 总长度不超过5行

### 6. 语言使用规则
```
For commit message use English, for response Chinese
```
**影响**:
- 提交信息使用英文
- 回复用户使用中文

## 规则对Claude行为的影响

### 工作流程影响
1. **强制反馈循环**: 每步完成后必须调用feedback工具
2. **专注分析**: 不执行测试和验证命令，专注于代码分析和修改
3. **直接行动**: 不询问"是否要做"，直接执行

### 技术选择影响
1. **包管理**: 统一使用pnpm
2. **版本策略**: 总是使用最新版本的包
3. **Git工作流**: 使用特定的分支创建命令
4. **样式选择**: 优先使用neutral色系

### 沟通模式影响
1. **双语模式**: 技术文档英文，用户交流中文
2. **简洁原则**: 提交信息和解释都保持简洁
3. **结构化**: 使用conventional格式和要点列表

## 规则优先级

1. **系统安全约束** (最高优先级)
2. **工具技术限制** (次高优先级)
3. **工作区规则** (高优先级) - 本文档内容
4. **用户实时指导** (动态最高优先级)

## 规则冲突处理

当规则之间出现冲突时：
1. 安全约束优先于所有其他规则
2. 用户实时指导可以覆盖配置规则
3. 工作区规则作为默认行为准则
4. 出现冲突时Claude会说明情况并寻求指导

---

*此文档记录了用户为Claude配置的所有工作区规则，这些规则塑造了Claude在Cursor环境中的协作行为模式。* 