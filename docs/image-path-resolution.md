# 图片相对路径解析功能

## 功能概述

实现了完整的图片相对路径解析功能，让 AI 模型可以在对话中引用书籍内的图片，并在前端正确显示。支持处理如 `![2-1 猫科动物分类图](../images/00010.jpeg)` 这样的相对路径图片引用。

## 功能链路

整个功能包含四个主要环节：

```
AI 生成 → 后端存储 → 状态管理 → 前端渲染
```

### 1. AI 生成阶段
- **文件**：`packages/app/src/constants/prompt.ts`
- **作用**：指导 AI 模型使用正确的 Markdown 图片语法

### 2. 后端存储阶段  
- **文件**：`packages/app/src-tauri/plugins/tauri-plugin-epub/src/pipeline.rs`
- **作用**：在 EPUB 向量化时保存图片基础目录路径

### 3. 状态管理阶段
- **文件**：`packages/app/src/store/activeBookStore.ts`
- **作用**：自动加载书籍 metadata，提供图片路径解析所需的基础目录

### 4. 前端渲染阶段
- **文件**：`packages/app/src/components/prompt-kit/markdown.tsx`
- **作用**：解析相对路径，转换为可访问的文件 URL，正确渲染图片

## 详细实现

### 1. AI 提示词增强 (prompt.ts)

在 `READING_PROMPT_BASE` 中添加了新的"图片引用"章节：

```typescript
### 图片引用
当回答中需要引用书籍中的图片、图表、插图时：
- **使用标准 Markdown 图片语法**：`![图片描述](相对路径)`
- **相对路径格式**：使用书籍内的相对路径，如 `../images/图片文件名.jpg` 或 `./images/图片文件名.png`
- **描述性 alt 文本**：提供有意义的图片描述，帮助用户理解图片内容
- **举例**：`![图2-1 猫科动物分类图](../images/00010.jpeg)`

这样可以让用户直接看到书中的相关图片，增强理解效果。
```

### 2. 后端 metadata 存储 (pipeline.rs)

#### 2.1 扩展数据结构

```rust
#[derive(Debug, Clone, Deserialize, Serialize, Default)]
struct BookMetadataFile {
    // ... 其他字段 ...
    #[serde(default)]
    base_dir: Option<String>,
}

#[derive(Debug, Clone, Deserialize, Serialize, Default)]
struct MetadataPerson {
    // 添加了 Serialize trait
}

#[derive(Debug, Clone, Deserialize, Serialize)]
enum AuthorField {
    // 添加了 Serialize trait
}
```

#### 2.2 保存 base_dir 逻辑

```rust
fn write_metadata_markdown(
    book_dir: &Path, 
    epub_content: &EpubContent, 
    flat_toc: &[FlatTocNode], 
    toc_base_dir: &Path  // 新增参数
) -> Result<()> {
    // ... 生成 metadata.md ...
    
    // 更新 metadata.json，保存 base_dir
    let updated_metadata = BookMetadataFile {
        title: Some(title),
        // ... 其他字段 ...
        base_dir: Some(toc_base_dir.to_string_lossy().to_string()),
    };
    
    let metadata_json = serde_json::to_string_pretty(&updated_metadata)?;
    fs::write(&metadata_path, metadata_json)?;
    
    Ok(())
}
```

### 3. 状态管理增强 (activeBookStore.ts)

#### 3.1 类型定义

```typescript
export interface BookMetadata {
  title?: string;
  language?: string;
  published?: string;
  publisher?: string;
  author?: string | { name?: string }[] | { name?: string };
  base_dir?: string; // 关键字段：图片基础目录
}

type ActiveBookState = {
  activeBookId: string | null;
  activeBookMetadata: BookMetadata | null; // 新增状态
  setActiveBookId: (id: string | null) => void;
};
```

#### 3.2 自动加载逻辑

```typescript
async function loadBookMetadata(bookId: string): Promise<BookMetadata | null> {
  try {
    const appDataDirPath = await appDataDir();
    const metadataPath = `${appDataDirPath}/books/${bookId}/metadata.json`;
    
    const metadataContent = await readTextFile(metadataPath);
    const metadata = JSON.parse(metadataContent) as BookMetadata;
    
    return metadata;
  } catch (error) {
    console.warn(`Failed to load metadata for book ${bookId}:`, error);
    return null;
  }
}

// 在 setActiveBookId 中自动加载 metadata
setActiveBookId: async (id: string | null) => {
  set({ activeBookId: id });
  
  if (id) {
    const metadata = await loadBookMetadata(id);
    if (get().activeBookId === id) {
      set({ activeBookMetadata: metadata });
    }
  } else {
    set({ activeBookMetadata: null });
  }
}
```

### 4. 前端图片渲染 (markdown.tsx)

#### 4.1 路径解析函数

```typescript
// 检测是否为相对路径
function isRelativePath(src: string): boolean {
  return src.startsWith("../") || src.startsWith("./");
}

// 解析相对路径，拼接 baseDir
function resolveImagePath(src: string, baseDir?: string): string {
  if (!isRelativePath(src) || !baseDir) {
    return src;
  }
  
  let resolvedPath = baseDir;
  const parts = src.split("/");
  
  for (const part of parts) {
    if (part === "..") {
      const lastSlashIndex = resolvedPath.lastIndexOf("/");
      if (lastSlashIndex > 0) {
        resolvedPath = resolvedPath.substring(0, lastSlashIndex);
      }
    } else if (part && part !== ".") {
      resolvedPath += "/" + part;
    }
  }
  
  return resolvedPath;
}
```

#### 4.2 自定义 img 组件

```typescript
function MarkdownComponent({ children, id, className, components }: MarkdownProps) {
  const { activeBookMetadata } = useActiveBookStore();

  const finalComponents = useMemo(() => {
    const imgComponent = function ImgComponent({
      src,
      alt,
      ...props
    }: { src?: string; alt?: string; [key: string]: any }) {
      if (!src) {
        return <img alt={alt} {...props} />;
      }

      let resolvedSrc = src;
      if (isRelativePath(src) && activeBookMetadata?.base_dir) {
        try {
          const fullPath = resolveImagePath(src, activeBookMetadata.base_dir);
          resolvedSrc = convertFileSrc(fullPath);
          console.log(`Resolved image path: ${src} -> ${fullPath} -> ${resolvedSrc}`);
        } catch (error) {
          console.warn(`Failed to resolve image path: ${src}`, error);
          resolvedSrc = src; // fallback
        }
      }

      return <img src={resolvedSrc} alt={alt} {...props} />;
    };

    return {
      ...INITIAL_COMPONENTS,
      img: imgComponent,
      ...components,
    };
  }, [activeBookMetadata?.base_dir, components]);

  // ... 渲染逻辑 ...
}
```

## 使用示例

### AI 对话中的图片引用

当用户询问书中的图表时，AI 会这样回答：

```markdown
书中第2章详细介绍了猫科动物的分类，让我们看看这张分类图：

![图2-1 猫科动物分类图](../images/00010.jpeg)

从这张图中我们可以看到...
```

### 路径解析过程

1. **检测相对路径**：`../images/00010.jpeg` ✅
2. **获取 base_dir**：`/Users/<USER>/books/bookid/mdbook/book/src/chapter01`
3. **路径解析**：
   - 起始路径：`/Users/<USER>/books/bookid/mdbook/book/src/chapter01`
   - 处理 `..`：向上到 `/Users/<USER>/books/bookid/mdbook/book/src`
   - 添加 `images/00010.jpeg`：`/Users/<USER>/books/bookid/mdbook/book/src/images/00010.jpeg`
4. **convertFileSrc 转换**：转换为可访问的文件 URL
5. **渲染显示**：用户看到图片

## 技术特点

### 1. 完整的错误处理
- metadata 加载失败时的 fallback
- 路径解析失败时的原始路径回退
- 异步加载的竞态条件处理

### 2. 性能优化
- 使用 `useMemo` 缓存 components 对象
- 异步加载 metadata，不阻塞 UI
- 只在必要时进行路径解析

### 3. 类型安全
- 完整的 TypeScript 类型定义
- Rust 端的强类型约束
- 序列化/反序列化的类型安全

## 测试场景

### 正常情况
- ✅ AI 生成相对路径图片引用
- ✅ metadata.json 包含正确的 base_dir
- ✅ 前端正确解析并显示图片

### 异常情况  
- ✅ metadata.json 不存在或格式错误
- ✅ base_dir 为空或无效
- ✅ 图片文件不存在
- ✅ 路径解析失败

### 边界情况
- ✅ 复杂的相对路径（多层 `../`）
- ✅ 混合的绝对路径和相对路径
- ✅ 特殊字符和 Unicode 文件名

## 后续优化

### 可能的改进方向
1. **缓存机制**：缓存已解析的图片路径，避免重复计算
2. **图片预加载**：提前加载可能用到的图片，提升用户体验  
3. **图片优化**：支持不同分辨率的图片，根据设备选择合适尺寸
4. **错误恢复**：更智能的图片路径猜测和修复机制

### 扩展功能
1. **图片缩放**：支持点击图片放大查看
2. **图片标注**：支持在图片上添加注释和高亮
3. **图片搜索**：根据图片内容或描述搜索相关图片
4. **图片导出**：支持将图片保存到本地或分享

---

*最后更新：2024年12月* 