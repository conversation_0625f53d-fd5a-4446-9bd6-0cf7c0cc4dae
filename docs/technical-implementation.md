# Tauri电子书阅读器 - 技术实现详解

## 目录
- [后端实现详解](#后端实现详解)
- [前端实现详解](#前端实现详解)
- [数据库设计](#数据库设计)
- [插件系统](#插件系统)
- [AI集成实现](#ai集成实现)
- [向量化系统](#向量化系统)
- [性能优化](#性能优化)

## 后端实现详解

### Tauri应用架构

#### 主入口点
```rust
// src-tauri/src/main.rs
fn main() {
    tauri_app_lib::run()
}

// src-tauri/src/lib.rs
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_global_shortcut::Builder::new().build())
        .manage(AppState::default())
        .plugin(tauri_plugin_sql::Builder::new().build())
        .plugin(tauri_plugin_epub::init())
        .plugin(tauri_plugin_llamacpp::init())
        .setup(|app| {
            // 异步数据库初始化
            let app_handle = app.handle().clone();
            tauri::async_runtime::spawn(async move {
                let pool = database::initialize(&app_handle).await
                    .expect("Failed to initialize database");
                let state = app_handle.state::<AppState>();
                let mut db_pool_guard = state.db_pool.lock().await;
                *db_pool_guard = Some(pool);
            });
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            // 书籍管理命令
            save_book, get_books, update_book, delete_book,
            // 阅读状态命令
            get_book_status, update_book_status,
            // 标签管理命令
            create_tag, get_tags, update_tag, delete_tag,
            // 对话管理命令
            create_thread, edit_thread, get_latest_thread_by_book_key,
            // LLama管理命令
            get_llamacpp_backend_path, download_llama_server,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
```

#### 应用状态管理
```rust
// src/core/state.rs
use sqlx::SqlitePool;
use std::sync::Arc;
use tokio::sync::Mutex;

#[derive(Default)]
pub struct AppState {
    pub db_pool: Arc<Mutex<Option<SqlitePool>>>,
}
```

### 核心API实现

#### 书籍管理API
```rust
// src/core/books/commands.rs

#[tauri::command]
pub async fn save_book(
    app_handle: AppHandle, 
    data: BookUploadData
) -> Result<SimpleBook, String> {
    let db_pool = get_db_pool(&app_handle).await?;
    
    // 检查书籍是否已存在
    if let Some(existing) = get_book_by_id(app_handle.clone(), data.id.clone()).await? {
        return Err(format!("书籍已存在: {}", existing.title));
    }
    
    // 文件系统操作
    let app_data_dir = app_handle.path().app_data_dir()
        .map_err(|e| format!("获取应用目录失败: {}", e))?;
    let book_dir = app_data_dir.join("books").join(&data.id);
    fs::create_dir_all(&book_dir)
        .map_err(|e| format!("创建目录失败: {}", e))?;
    
    // 移动文件到目标位置
    let epub_path = book_dir.join(format!("book.{}", data.format.to_lowercase()));
    std::fs::rename(&data.temp_file_path, &epub_path)?;
    
    // 处理封面文件
    let cover_path = if let Some(cover_temp_path) = &data.cover_temp_file_path {
        let cover_file = book_dir.join("cover.jpg");
        std::fs::rename(cover_temp_path, &cover_file)?;
        Some(format!("books/{}/cover.jpg", data.id))
    } else { None };
    
    // 数据库事务操作
    let mut tx = db_pool.begin().await?;
    
    // 插入书籍记录
    sqlx::query(r#"
        INSERT INTO books (id, title, author, format, file_path, cover_path,
                          file_size, language, tags, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    "#)
    .bind(&data.id).bind(&data.title).bind(&data.author)
    .bind(&data.format).bind(&file_path).bind(&cover_path)
    .bind(data.file_size).bind(&data.language).bind(None::<String>)
    .bind(now).bind(now)
    .execute(&mut *tx).await?;
    
    // 创建默认阅读状态
    sqlx::query(r#"
        INSERT INTO book_status (book_id, status, progress_current, 
                               progress_total, reading_time_minutes, 
                               created_at, updated_at)
        VALUES (?, 'unread', 0, 0, 0, ?, ?)
    "#)
    .bind(&data.id).bind(now).bind(now)
    .execute(&mut *tx).await?;
    
    tx.commit().await?;
    
    Ok(SimpleBook::new(data.id, data.title, data.author, 
                       data.format, file_path, cover_path, 
                       data.file_size, data.language))
}

#[tauri::command]
pub async fn get_books_with_status(
    app_handle: AppHandle,
    options: Option<BookQueryOptions>,
) -> Result<Vec<BookWithStatus>, String> {
    let db_pool = get_db_pool(&app_handle).await?;
    let opts = options.unwrap_or_default();
    
    // 构建动态SQL查询
    let mut query = String::from(r#"
        SELECT b.*, s.book_id as status_book_id, s.status, s.progress_current, 
               s.progress_total, s.last_reading_position, s.reading_time_minutes,
               s.last_read_at, s.started_at, s.completed_at, s.metadata,
               s.created_at as status_created_at, s.updated_at as status_updated_at
        FROM books b LEFT JOIN book_status s ON b.id = s.book_id
    "#);
    
    let mut conditions = Vec::new();
    
    // 搜索条件
    if let Some(search_query) = &opts.search_query {
        if !search_query.trim().is_empty() {
            conditions.push("(b.title LIKE ? OR b.author LIKE ?)");
        }
    }
    
    // 标签过滤
    if let Some(tags) = &opts.tags {
        if !tags.is_empty() {
            let tag_conditions: Vec<String> = tags.iter()
                .map(|_| "b.tags LIKE ?".to_string()).collect();
            conditions.push(&format!("({})", tag_conditions.join(" OR ")));
        }
    }
    
    if !conditions.is_empty() {
        query.push_str(&format!(" WHERE {}", conditions.join(" AND ")));
    }
    
    // 排序和分页
    if let Some(sort_by) = &opts.sort_by {
        let order = opts.sort_order.as_deref().unwrap_or("asc");
        query.push_str(&format!(" ORDER BY b.{} {}", sort_by, order));
    } else {
        query.push_str(" ORDER BY b.updated_at DESC");
    }
    
    if let Some(limit) = opts.limit {
        query.push_str(&format!(" LIMIT {}", limit));
        if let Some(offset) = opts.offset {
            query.push_str(&format!(" OFFSET {}", offset));
        }
    }
    
    // 执行查询并处理结果
    let rows = sqlx::query(&query).fetch_all(&db_pool).await?;
    let mut results = Vec::new();
    
    for row in rows {
        let book = SimpleBook::from_db_row(&row)?;
        let status = if row.try_get::<Option<String>, _>("status_book_id")?.is_some() {
            Some(BookStatus::from_db_row(&row)?)
        } else { None };
        results.push(BookWithStatus { book, status });
    }
    
    Ok(results)
}
```

#### 数据模型设计
```rust
// src/core/books/models.rs

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct SimpleBook {
    pub id: String,
    pub title: String,
    pub author: String,
    pub format: String,
    #[serde(rename = "filePath")]
    pub file_path: String,
    #[serde(rename = "coverPath")]
    pub cover_path: Option<String>,
    #[serde(rename = "fileSize")]
    pub file_size: i64,
    pub language: String,
    pub tags: Option<Vec<String>>,
    #[serde(rename = "createdAt")]
    pub created_at: i64,
    #[serde(rename = "updatedAt")]
    pub updated_at: i64,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct BookStatus {
    #[serde(rename = "bookId")]
    pub book_id: String,
    pub status: String, // 'unread', 'reading', 'completed'
    #[serde(rename = "progressCurrent")]
    pub progress_current: i64,
    #[serde(rename = "progressTotal")]
    pub progress_total: i64,
    #[serde(rename = "lastReadingPosition")]
    pub last_reading_position: Option<String>,
    #[serde(rename = "readingTimeMinutes")]
    pub reading_time_minutes: i64,
    #[serde(rename = "lastReadAt")]
    pub last_read_at: Option<i64>,
    pub metadata: Option<serde_json::Value>,
    #[serde(rename = "createdAt")]
    pub created_at: i64,
    #[serde(rename = "updatedAt")]
    pub updated_at: i64,
}

impl SimpleBook {
    // 从数据库行构造对象
    pub fn from_db_row(row: &sqlx::sqlite::SqliteRow) -> Result<Self, sqlx::Error> {
        use sqlx::Row;
        
        let tags_str: Option<String> = row.try_get("tags")?;
        let tags = tags_str.and_then(|s| serde_json::from_str(&s).ok());
        
        Ok(Self {
            id: row.try_get("id")?,
            title: row.try_get("title")?,
            author: row.try_get("author")?,
            format: row.try_get("format")?,
            file_path: row.try_get("file_path")?,
            cover_path: row.try_get("cover_path")?,
            file_size: row.try_get("file_size")?,
            language: row.try_get("language")?,
            tags,
            created_at: row.try_get("created_at")?,
            updated_at: row.try_get("updated_at")?,
        })
    }
}
```

### 数据库层实现

#### 数据库初始化
```rust
// src/core/database.rs
use sqlx::{migrate::MigrateDatabase, Sqlite, SqlitePool};

pub async fn initialize(app_handle: &AppHandle) -> Result<SqlitePool, Box<dyn std::error::Error>> {
    let app_data_dir = app_handle.path().app_data_dir()?;
    let db_dir = app_data_dir.join("database");
    fs::create_dir_all(&db_dir)?;
    
    let db_path = db_dir.join("app.db");
    let db_url = format!("sqlite:{}", db_path.to_str().unwrap());
    
    // 创建数据库（如果不存在）
    if !Sqlite::database_exists(&db_url).await.unwrap_or(false) {
        Sqlite::create_database(&db_url).await?;
        println!("Database created at: {}", db_url);
    }
    
    let pool = SqlitePool::connect(&db_url).await?;
    
    // 执行schema初始化
    sqlx::query(include_str!("./schema.sql"))
        .execute(&pool).await?;
        
    println!("Database schema initialized.");
    Ok(pool)
}
```

## 前端实现详解

### React应用架构

#### 状态管理设计

**Zustand全局状态管理**
```typescript
// src/store/libraryStore.ts
import { create } from "zustand";

interface LibraryState {
  library: Book[];
  searchQuery: string;
  booksWithStatus: BookWithStatusAndUrls[];
  isLoading: boolean;
  
  // Actions
  setLibrary: (books: Book[]) => void;
  setSearchQuery: (query: string) => void;
  refreshBooks: () => Promise<void>;
}

export const useLibraryStore = create<LibraryState>((set, get) => ({
  library: [],
  searchQuery: "",
  booksWithStatus: [],
  isLoading: false,
  
  setLibrary: (books) => set({ library: books }),
  setSearchQuery: (query) => set({ searchQuery: query }),
  
  refreshBooks: async () => {
    try {
      set({ isLoading: true });
      const libraryBooks = await getBooksWithStatus();
      
      // URL转换为Tauri可访问路径
      const booksWithUrls = await Promise.all(
        libraryBooks.map(async (book) => {
          const appDataDirPath = await appDataDir();
          const fileUrl = convertFileSrc(`${appDataDirPath}/${book.filePath}`);
          const coverUrl = book.coverPath ? 
            convertFileSrc(`${appDataDirPath}/${book.coverPath}`) : undefined;
          
          return { ...book, fileUrl, coverUrl };
        })
      );
      
      set({ booksWithStatus: booksWithUrls });
    } catch (error) {
      console.error("Error refreshing books:", error);
    } finally {
      set({ isLoading: false });
    }
  },
}));
```

**Jotai原子化状态管理**
```typescript
// src/pages/new-reader/atoms/readerAtoms.ts
import { atom } from "jotai";

export interface BookDataAtomState {
  id: string;
  book: Book | null;
  file: File | null;
  config: BookConfig | null;
  bookDoc: BookDoc | null;
}

// 原子状态定义
export const bookDataAtom = atom<BookDataAtomState | null>(null);
export const viewAtom = atom<FoliateView | null>(null);
export const progressAtom = atom<BookProgress | null>(null);
export const viewSettingsAtom = atom<ViewSettings | null>(null);

// 组合选择器
export const viewStateAtom = atom((get) => ({
  view: get(viewAtom),
  progress: get(progressAtom),
  viewSettings: get(viewSettingsAtom),
}));

// 异步操作原子
export const initBookDataActionAtom = atom(
  null,
  async (get, set, params: { envConfig: EnvConfigType; id: string }) => {
    const { envConfig, id } = params;
    
    try {
      const settings = get(bookSettingsAtom);
      const appService = await envConfig.getAppService();
      
      // 从图书库查找书籍
      const { useLibraryStore } = await import("@/store/libraryStore");
      const { library } = useLibraryStore.getState();
      const book = library.find((b) => b.hash === id);
      
      if (!book) throw new Error("Book not found");
      
      // 加载文件
      const { convertFileSrc } = await import("@tauri-apps/api/core");
      const fileUrl = convertFileSrc(book.filePath!);
      const response = await fetch(fileUrl);
      const arrayBuffer = await response.arrayBuffer();
      const file = new File([arrayBuffer], "book.epub");
      
      // 加载配置和文档
      const config = await appService.loadBookConfig(book, settings);
      const { book: bookDoc } = await new DocumentLoader(file).open();
      
      // 更新原子状态
      set(bookDataAtom, { id, book, file, config, bookDoc });
      set(viewSettingsAtom, { 
        ...settings.globalViewSettings, 
        ...(config.viewSettings || {}) 
      });
    } catch (error) {
      console.error("Failed to initialize book:", error);
      set(bookDataAtom, { id, book: null, file: null, config: null, bookDoc: null });
    }
  }
);
```

#### 自定义Hooks实现

**书籍上传Hook**
```typescript
// src/hooks/useBookUpload.ts
import { useCallback, useState } from "react";
import { uploadBook } from "@/services/bookService";

export function useBookUpload() {
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const { refreshBooks } = useLibraryStore();
  
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    handleDropedFiles(files);
  }, []);
  
  const importBooks = useCallback(async (files: File[]) => {
    setIsUploading(true);
    const successBooks = [];
    const failedFiles = [];
    
    // 批量处理文件
    for (const file of files) {
      try {
        const newBook = await uploadBook(file);
        successBooks.push(newBook);
      } catch (error) {
        failedFiles.push(file.name);
      }
    }
    
    setIsUploading(false);
    
    // 显示处理结果
    if (failedFiles.length > 0) {
      toast.error(`导入失败: ${failedFiles.join(", ")}`);
    }
    
    if (successBooks.length > 0) {
      toast.success(`成功导入 ${successBooks.length} 本书`);
      await refreshBooks(); // 刷新书库
    }
  }, [refreshBooks]);
  
  return {
    isDragOver, isUploading,
    handleDrop, triggerFileSelect: selectFiles,
  };
}
```

#### AI集成实现

**多提供商支持**
```typescript
// src/ai/providers/factory.ts
import { createAnthropic } from "@ai-sdk/anthropic";
import { createDeepSeek } from "@ai-sdk/deepseek";
import { createOpenAI } from "@ai-sdk/openai";

export function createProviderInstance(config: ProviderConfig) {
  const { providerId, apiKey, baseUrl } = config;
  
  switch (providerId) {
    case "deepseek":
      return createDeepSeek({ apiKey, baseURL: baseUrl });
    
    case "openai":
      return createOpenAI({ apiKey, baseURL: baseUrl });
      
    case "anthropic":
      return createAnthropic({ apiKey, baseURL: baseUrl });
      
    case "grok":
      return createOpenAI({
        apiKey,
        baseURL: baseUrl || "https://api.x.ai/v1"
      });
      
    default:
      throw new Error(`Unsupported provider: ${providerId}`);
  }
}

export function createModelInstance(providerId: string, modelId: string) {
  const { modelProviders } = useModelProviderStore.getState();
  const provider = modelProviders.find(p => p.provider === providerId);
  
  if (!provider?.active) {
    throw new Error(`Provider not active: ${providerId}`);
  }
  
  const model = provider.models.find(m => m.id === modelId && m.active);
  if (!model) {
    throw new Error(`Model not found: ${modelId}`);
  }
  
  const providerInstance = createProviderInstance({
    providerId, apiKey: provider.apiKey, baseUrl: provider.baseUrl
  });
  
  return providerInstance(modelId);
}
```

**自定义聊天Hook**
```typescript
// src/ai/hooks/use-chat.ts
import { useChat as useChatSDK } from "@ai-sdk/react";
import { CustomChatTransport } from "../custom-chat-transport";

export function useChat(model: LanguageModel, options?: CustomChatOptions) {
  const transportRef = useRef<CustomChatTransport | null>(null);
  
  // 初始化传输层
  if (!transportRef.current) {
    transportRef.current = new CustomChatTransport(model);
  }
  
  // 动态更新模型
  useEffect(() => {
    if (transportRef.current) {
      transportRef.current.updateModel(model);
    }
  }, [model]);
  
  return useChatSDK({
    transport: transportRef.current,
    ...options,
  });
}
```

**自定义聊天传输**
```typescript
// src/ai/custom-chat-transport.ts
import type { LanguageModel } from "ai";

export class CustomChatTransport {
  constructor(private model: LanguageModel) {}
  
  updateModel(model: LanguageModel) {
    this.model = model;
  }
  
  async send(messages: UIMessage[], options: any) {
    // RAG上下文检索
    const context = await this.retrieveContext(messages);
    
    // 构建增强提示
    const enhancedMessages = this.buildEnhancedMessages(messages, context);
    
    // 调用模型API
    return streamText({
      model: this.model,
      messages: enhancedMessages,
      ...options,
    });
  }
  
  private async retrieveContext(messages: UIMessage[]) {
    const lastMessage = messages[messages.length - 1];
    if (!lastMessage?.content) return null;
    
    try {
      // 调用向量检索API
      const searchResults = await invoke("search_vectors", {
        query: lastMessage.content,
        limit: 5,
        threshold: 0.7,
      });
      
      return searchResults;
    } catch (error) {
      console.warn("Context retrieval failed:", error);
      return null;
    }
  }
}
```

#### 组件架构设计

**聊天消息组件**
```typescript
// src/components/chat/chat-messages.tsx
export function ChatMessages({ 
  messages, status, error, onUpdateMessage 
}: ChatMessagesProps) {
  const { scrollToBottom } = useStickToBottomContext();
  const { elapsedTime } = useReasoningTimer(
    reasoningActive, hasReasoningInLastMessage, 
    lastMessage?.id, onUpdateMessage
  );
  
  const renderMessageParts = (parts: any[], isLastMessage: boolean) => {
    const elements: any[] = [];
    let textBuffer = "";
    
    // 文本内容缓冲
    const flushText = () => {
      if (!textBuffer) return;
      elements.push(
        <MessageContent key={`text-${elements.length}`} markdown>
          {textBuffer}
        </MessageContent>
      );
      textBuffer = "";
    };
    
    // 处理不同类型的消息部分
    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];
      
      if (part.type === "text") {
        textBuffer += part.text ?? "";
      } else if (part.type === "reasoning") {
        flushText();
        elements.push(
          <Reasoning key={`reasoning-${i}`} isStreaming={reasoningActive}>
            <ReasoningTrigger>
              <Brain className="h-4 w-4" />
              <span>
                {reasoningActive ? "Thinking..." : ""}
                {elapsedTime && ` (${elapsedTime}s)`}
              </span>
            </ReasoningTrigger>
            <ReasoningContent markdown>
              {part.text || ""}
            </ReasoningContent>
          </Reasoning>
        );
      } else if (part.type === "tool") {
        flushText();
        elements.push(<Tool key={`tool-${i}`} part={part} />);
      }
    }
    
    flushText(); // 处理剩余文本
    return elements;
  };
  
  return (
    <ChatContainerContent>
      {messages.map((message, index) => (
        <Message key={message.id} role={message.role}>
          {renderMessageParts(message.parts || [], index === messages.length - 1)}
          
          <MessageActions>
            <MessageAction icon={Copy} onClick={() => copyMessage(message)} />
            <MessageAction icon={ThumbsUp} onClick={() => likeMessage(message)} />
            <MessageAction icon={ThumbsDown} onClick={() => dislikeMessage(message)} />
          </MessageActions>
        </Message>
      ))}
      <ChatContainerScrollAnchor />
    </ChatContainerContent>
  );
}
```

## 数据库设计

### 主要表结构

```sql
-- 书籍基本信息表
CREATE TABLE IF NOT EXISTS books (
    id TEXT PRIMARY KEY NOT NULL,           -- 书籍唯一标识（MD5哈希）
    title TEXT NOT NULL,                    -- 书名
    author TEXT NOT NULL,                   -- 作者
    format TEXT NOT NULL,                   -- 文件格式
    file_path TEXT NOT NULL,                -- 文件相对路径
    cover_path TEXT,                        -- 封面相对路径
    file_size INTEGER NOT NULL,             -- 文件大小（字节）
    language TEXT NOT NULL,                 -- 主要语言
    tags TEXT,                             -- JSON格式标签数组
    created_at INTEGER NOT NULL,            -- 创建时间戳
    updated_at INTEGER NOT NULL             -- 更新时间戳
);

-- 阅读状态跟踪表
CREATE TABLE IF NOT EXISTS book_status (
    book_id TEXT PRIMARY KEY NOT NULL,     -- 关联书籍ID
    status TEXT NOT NULL DEFAULT 'unread', -- 阅读状态
    progress_current INTEGER DEFAULT 0,     -- 当前阅读进度
    progress_total INTEGER DEFAULT 0,       -- 总页数/位置数
    last_reading_position TEXT,             -- 最后阅读位置（CFI或页码）
    reading_time_minutes INTEGER DEFAULT 0, -- 累计阅读时间（分钟）
    last_read_at INTEGER,                   -- 最后阅读时间
    started_at INTEGER,                     -- 开始阅读时间
    completed_at INTEGER,                   -- 完成阅读时间
    metadata TEXT,                          -- JSON格式额外元数据
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);

-- AI对话线程表
CREATE TABLE IF NOT EXISTS threads (
    id TEXT PRIMARY KEY NOT NULL,          -- 对话线程ID
    book_key TEXT NOT NULL,                -- 关联书籍ID
    metadata TEXT NOT NULL,                -- 对话元数据（AI模型、设置等）
    title TEXT NOT NULL,                   -- 对话标题
    messages TEXT NOT NULL,                -- JSON格式消息数组
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL
);

-- 标签分类表
CREATE TABLE IF NOT EXISTS tags (
    id TEXT PRIMARY KEY NOT NULL,          -- 标签ID
    name TEXT NOT NULL UNIQUE,             -- 标签名称
    color TEXT,                            -- 标签颜色（十六进制）
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL
);

-- 性能优化索引
CREATE INDEX IF NOT EXISTS idx_books_title ON books(title);
CREATE INDEX IF NOT EXISTS idx_books_author ON books(author);
CREATE INDEX IF NOT EXISTS idx_books_updated_at ON books(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_book_status_status ON book_status(status);
CREATE INDEX IF NOT EXISTS idx_book_status_last_read ON book_status(last_read_at DESC);
CREATE INDEX IF NOT EXISTS idx_threads_book_key ON threads(book_key);
CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name);
```

### 向量数据库设计

```sql
-- 文档分块表（sqlite-vec扩展）
CREATE TABLE IF NOT EXISTS document_chunks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_title TEXT NOT NULL,              -- 书名
    book_author TEXT NOT NULL,             -- 作者
    chapter_title TEXT NOT NULL,           -- 章节标题
    chapter_order INTEGER NOT NULL,        -- 章节顺序
    chunk_text TEXT NOT NULL,              -- 分块文本内容
    chunk_order INTEGER NOT NULL,          -- 分块顺序
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- TOC导航字段
    md_src TEXT NOT NULL DEFAULT '',       -- Markdown源文件路径
    toc_depth INTEGER NOT NULL DEFAULT 0,  -- TOC层级深度
    toc_id TEXT NOT NULL DEFAULT '',       -- TOC节点ID
    toc_index INTEGER NOT NULL DEFAULT 0,  -- TOC索引位置
    
    -- 分块位置字段
    chunk_index_in_toc INTEGER NOT NULL DEFAULT 0,   -- TOC内分块索引
    total_chunks_in_toc INTEGER NOT NULL DEFAULT 0,  -- TOC总分块数
    global_chunk_index INTEGER NOT NULL DEFAULT 0    -- 全局分块索引
);

-- 向量存储表（使用sqlite-vec扩展）
CREATE VIRTUAL TABLE IF NOT EXISTS vec_chunks USING vec0(
    chunk_id INTEGER PRIMARY KEY,          -- 关联document_chunks.id
    embedding FLOAT[1024]                  -- 向量嵌入（维度可配置）
);

-- 向量检索索引
CREATE INDEX IF NOT EXISTS idx_chunks_book_title ON document_chunks(book_title);
CREATE INDEX IF NOT EXISTS idx_chunks_toc_id ON document_chunks(toc_id);
CREATE INDEX IF NOT EXISTS idx_chunks_global_index ON document_chunks(global_chunk_index);
```

## 插件系统

### EPUB处理插件

#### 核心处理流水线
```rust
// plugins/tauri-plugin-epub/src/pipeline.rs

pub async fn process_epub_to_db<P: AsRef<Path>, F>(
    book_dir: P,
    opts: ProcessOptions,
    mut on_progress: Option<F>,
) -> Result<ProcessReport>
where
    F: FnMut(ProgressUpdate) + Send,
{
    let book_dir = book_dir.as_ref();
    let epub_path = book_dir.join("book.epub");
    let db_path = book_dir.join("vectors.sqlite");
    
    // 1. 读取EPUB文件
    let reader = EpubReader::new()?;
    let epub_content = reader.read_epub(&epub_path)?;
    
    // 2. 转换为mdBook格式
    let mdbook_dir = book_dir.join("mdbook");
    convert_epub_to_mdbook(&epub_path, &mdbook_dir)?;
    
    // 3. 解析TOC结构
    let toc_path = find_toc_ncx_in_mdbook(&mdbook_dir);
    let toc_nodes = if let Some(toc_path) = toc_path {
        parse_toc_file(&toc_path)?
    } else {
        Vec::new() // 回退到基于文件的TOC
    };
    
    // 4. 扁平化TOC为线性结构
    let flat_toc = flatten_toc(&toc_nodes);
    
    // 5. 初始化向量数据库
    let mut vector_db = VectorDatabase::new(&db_path, opts.dimension)?;
    let vectorizer = TextVectorizer::new(opts.vectorizer.clone()).await?;
    
    // 6. 处理每个TOC节点
    let mut total_chunks = 0;
    for (toc_index, toc_node) in flat_toc.iter().enumerate() {
        let md_file_path = mdbook_dir.join("text").join(&toc_node.md_src);
        
        if md_file_path.exists() {
            let content = fs::read_to_string(&md_file_path)?;
            
            // 文本分片
            let chunks = chunk_md_file(&content, 500, 50)?; // 500字符块，50字符重叠
            
            // 向量化并存储每个分片
            for (chunk_index, chunk_text) in chunks.iter().enumerate() {
                // 生成向量嵌入
                let embedding = vectorizer.vectorize_text(chunk_text).await?;
                
                // 创建文档分块对象
                let document_chunk = DocumentChunk {
                    id: None,
                    book_title: epub_content.title.clone(),
                    book_author: epub_content.author.clone(),
                    chapter_title: toc_node.title.clone(),
                    chapter_order: toc_node.play_order as usize,
                    chunk_text: chunk_text.clone(),
                    chunk_order: chunk_index,
                    embedding,
                    md_src: toc_node.md_src.clone(),
                    toc_depth: toc_node.depth,
                    toc_id: toc_node.id.clone(),
                    toc_index,
                    chunk_index_in_toc: chunk_index,
                    total_chunks_in_toc: chunks.len(),
                    global_chunk_index: total_chunks,
                };
                
                // 存储到向量数据库
                vector_db.insert_chunk(&document_chunk).await?;
                total_chunks += 1;
                
                // 报告进度
                if let Some(ref mut callback) = on_progress {
                    callback(ProgressUpdate {
                        current: total_chunks,
                        total: estimate_total_chunks(&flat_toc),
                        percent: (total_chunks as f32 / estimate_total_chunks(&flat_toc) as f32) * 100.0,
                        chapter_title: toc_node.title.clone(),
                        chunk_index,
                    });
                }
            }
        }
    }
    
    Ok(ProcessReport {
        db_path,
        book_title: epub_content.title,
        book_author: epub_content.author,
        total_chunks,
        vector_dimension: opts.dimension,
    })
}
```

#### 向量化实现
```rust
// plugins/tauri-plugin-epub/src/vectorizer.rs

pub struct TextVectorizer {
    client: Client,
    api_key: Option<String>,
    model_name: String,
    base_url: String,
    tokenizer: tiktoken_rs::CoreBPE,
}

impl TextVectorizer {
    pub async fn vectorize_text(&self, text: &str) -> Result<Vec<f32>> {
        // Token截断处理
        let max_tokens: usize = 480; // 保守设置，确保兼容性
        let tokens = self.tokenizer.encode_with_special_tokens(text);
        
        let processed_text = if tokens.len() > max_tokens {
            log::warn!("文本过长 ({} tokens)，截断到 {} tokens", tokens.len(), max_tokens);
            let clipped = &tokens[..max_tokens];
            self.tokenizer.decode(clipped.to_vec())
                .unwrap_or_else(|_| text.chars().take(1000).collect::<String>())
        } else {
            text.to_string()
        };
        
        // 构建API请求
        let request = EmbeddingRequest {
            input: vec![processed_text],
            model: self.model_name.clone(),
            encoding_format: "float".to_string(),
        };
        
        // 发送HTTP请求
        let url = format!("{}/v1/embeddings", self.base_url);
        let mut req = self.client
            .post(&url)
            .header("Content-Type", "application/json")
            .json(&request);
            
        if let Some(k) = &self.api_key {
            req = req.header("Authorization", format!("Bearer {}", k));
        }
        
        let response = req.send().await?;
        
        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            anyhow::bail!("嵌入API错误: {}", error_text);
        }
        
        let embedding_response: EmbeddingResponse = response.json().await?;
        
        if embedding_response.data.is_empty() {
            anyhow::bail!("API返回空的嵌入结果");
        }
        
        Ok(embedding_response.data[0].embedding.clone())
    }
}
```

#### 向量数据库实现
```rust
// plugins/tauri-plugin-epub/src/database.rs

impl VectorDatabase {
    pub fn new<P: AsRef<Path>>(db_path: P, embedding_dimension: usize) -> Result<Self> {
        // 注册sqlite-vec扩展
        unsafe {
            sqlite3_auto_extension(Some(std::mem::transmute(sqlite3_vec_init as *const ())));
        }
        
        let conn = Connection::open(db_path)?;
        let mut db = Self { conn, embedding_dimension };
        
        db.initialize_database()?;
        Ok(db)
    }
    
    pub async fn insert_chunk(&mut self, chunk: &DocumentChunk) -> Result<i64> {
        // 插入文档分块记录
        let chunk_id = self.conn.execute(
            r#"INSERT INTO document_chunks 
               (book_title, book_author, chapter_title, chapter_order, chunk_text, 
                chunk_order, md_src, toc_depth, toc_id, toc_index, 
                chunk_index_in_toc, total_chunks_in_toc, global_chunk_index)
               VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13)"#,
            params![
                chunk.book_title, chunk.book_author, chunk.chapter_title,
                chunk.chapter_order as i64, chunk.chunk_text, chunk.chunk_order as i64,
                chunk.md_src, chunk.toc_depth as i64, chunk.toc_id, chunk.toc_index as i64,
                chunk.chunk_index_in_toc as i64, chunk.total_chunks_in_toc as i64,
                chunk.global_chunk_index as i64
            ],
        )?;
        
        let chunk_id = self.conn.last_insert_rowid();
        
        // 插入向量嵌入
        let embedding_blob = self.serialize_vector(&chunk.embedding)?;
        self.conn.execute(
            "INSERT INTO vec_chunks (chunk_id, embedding) VALUES (?1, ?2)",
            params![chunk_id, embedding_blob],
        )?;
        
        Ok(chunk_id)
    }
    
    pub fn search_similar(&self, query_vector: &[f32], limit: usize, threshold: f32) -> Result<Vec<SearchResult>> {
        let query_blob = self.serialize_vector(query_vector)?;
        
        let mut stmt = self.conn.prepare(
            r#"SELECT dc.*, vec_distance_cosine(vc.embedding, ?1) as similarity
               FROM document_chunks dc
               JOIN vec_chunks vc ON dc.id = vc.chunk_id
               WHERE vec_distance_cosine(vc.embedding, ?1) > ?2
               ORDER BY similarity DESC
               LIMIT ?3"#,
        )?;
        
        let chunk_iter = stmt.query_map(params![query_blob, threshold, limit], |row| {
            Ok(SearchResult {
                chunk: DocumentChunk {
                    id: Some(row.get("id")?),
                    book_title: row.get("book_title")?,
                    book_author: row.get("book_author")?,
                    chapter_title: row.get("chapter_title")?,
                    chapter_order: row.get::<_, i64>("chapter_order")? as usize,
                    chunk_text: row.get("chunk_text")?,
                    chunk_order: row.get::<_, i64>("chunk_order")? as usize,
                    embedding: Vec::new(), // 不需要返回原始向量
                    md_src: row.get("md_src")?,
                    toc_depth: row.get::<_, i64>("toc_depth")? as u32,
                    toc_id: row.get("toc_id")?,
                    toc_index: row.get::<_, i64>("toc_index")? as usize,
                    chunk_index_in_toc: row.get::<_, i64>("chunk_index_in_toc")? as usize,
                    total_chunks_in_toc: row.get::<_, i64>("total_chunks_in_toc")? as usize,
                    global_chunk_index: row.get::<_, i64>("global_chunk_index")? as usize,
                },
                similarity: row.get("similarity")?,
            })
        })?;
        
        let mut results = Vec::new();
        for chunk in chunk_iter {
            results.push(chunk?);
        }
        
        Ok(results)
    }
    
    fn serialize_vector(&self, vector: &[f32]) -> Result<Vec<u8>> {
        // 将f32向量序列化为二进制格式
        let mut bytes = Vec::with_capacity(vector.len() * 4);
        for &value in vector {
            bytes.extend_from_slice(&value.to_le_bytes());
        }
        Ok(bytes)
    }
}
```

### LLamaCpp集成插件

#### 进程管理实现
```rust
// plugins/tauri-plugin-llamacpp/src/commands.rs

#[tauri::command]
pub async fn load_llama_model<R: Runtime>(
    app_handle: tauri::AppHandle<R>,
    backend_path: &str,
    library_path: Option<&str>,
    mut args: Vec<String>,
) -> ServerResult<SessionInfo> {
    let state: State<LlamacppState> = app_handle.state();
    let mut process_map = state.llama_server_process.lock().await;
    
    // 验证二进制文件路径
    validate_binary_path(backend_path)?;
    
    // 解析端口和模型路径
    let port = parse_port_from_args(&args);
    let model_path = validate_model_path(&mut args)?;
    let api_key = extract_arg_value(&args, "--api-key");
    let model_id = extract_arg_value(&args, "-a");
    
    // 配置命令
    let mut command = Command::new(backend_path);
    command.args(args);
    
    // 设置环境和管道
    setup_library_path(library_path, &mut command);
    command.stdout(Stdio::piped());
    command.stderr(Stdio::piped());
    
    // 启动子进程
    let mut child = command.spawn().map_err(ServerError::Io)?;
    let stderr = child.stderr.take().expect("stderr was piped");
    let stdout = child.stdout.take().expect("stdout was piped");
    
    // 监控输出流
    let (ready_tx, mut ready_rx) = mpsc::channel::<bool>(1);
    
    // 监控stdout判断服务器就绪状态
    let _stdout_task = tokio::spawn(async move {
        let mut reader = BufReader::new(stdout);
        let mut buffer = Vec::new();
        
        loop {
            buffer.clear();
            match reader.read_until(b'\n', &mut buffer).await {
                Ok(0) => break, // EOF
                Ok(_) => {
                    let line = String::from_utf8_lossy(&buffer);
                    if line.contains("HTTP server listening") {
                        let _ = ready_tx.send(true).await;
                    }
                    log::info!("[llamacpp stdout] {}", line.trim());
                }
                Err(e) => {
                    log::error!("读取stdout错误: {}", e);
                    break;
                }
            }
        }
    });
    
    // 监控stderr
    let _stderr_task = tokio::spawn(async move {
        let mut reader = BufReader::new(stderr);
        let mut buffer = Vec::new();
        
        while let Ok(n) = reader.read_until(b'\n', &mut buffer).await {
            if n == 0 break;
            let line = String::from_utf8_lossy(&buffer);
            log::warn!("[llamacpp stderr] {}", line.trim());
            buffer.clear();
        }
    });
    
    // 等待服务器就绪
    tokio::select! {
        _ = ready_rx.recv() => {
            log::info!("LLama服务器已就绪，端口: {}", port);
        }
        _ = tokio::time::sleep(Duration::from_secs(30)) => {
            return Err(ServerError::StartupTimeout("服务器启动超时".to_string()));
        }
    }
    
    // 创建会话信息
    let session_info = SessionInfo {
        pid: child.id().unwrap_or(0) as i32,
        port,
        model_id: model_id.unwrap_or_default(),
        model_path: model_path.to_string_lossy().to_string(),
        api_key: api_key.unwrap_or_default(),
    };
    
    // 存储会话
    let session = LLamaBackendSession {
        child,
        info: session_info.clone(),
    };
    
    process_map.insert(session_info.pid, session);
    
    Ok(session_info)
}

#[tauri::command]
pub async fn unload_llama_model<R: Runtime>(
    app_handle: tauri::AppHandle<R>,
    pid: i32,
) -> ServerResult<UnloadResult> {
    let state: State<LlamacppState> = app_handle.state();
    let mut process_map = state.llama_server_process.lock().await;
    
    if let Some(mut session) = process_map.remove(&pid) {
        // 尝试优雅终止
        #[cfg(unix)]
        {
            if let Err(e) = graceful_terminate_process(pid).await {
                log::warn!("优雅终止失败，强制终止: {}", e);
                let _ = session.child.kill().await;
            }
        }
        
        #[cfg(windows)]
        {
            let _ = session.child.kill().await;
        }
        
        Ok(UnloadResult { success: true, error: None })
    } else {
        Ok(UnloadResult { 
            success: false, 
            error: Some(format!("进程 {} 未找到", pid)) 
        })
    }
}
```

#### 跨平台进程管理
```rust
// plugins/tauri-plugin-llamacpp/src/process.rs

#[cfg(unix)]
pub async fn graceful_terminate_process(pid: i32) -> Result<(), String> {
    use nix::sys::signal::{kill, Signal};
    use nix::unistd::Pid;
    
    let pid = Pid::from_raw(pid);
    
    // 发送SIGTERM信号
    kill(pid, Signal::SIGTERM).map_err(|e| format!("发送SIGTERM失败: {}", e))?;
    
    // 等待进程退出
    for _ in 0..30 { // 最多等待30秒
        tokio::time::sleep(Duration::from_secs(1)).await;
        
        if !is_process_running_by_pid(pid.as_raw()) {
            return Ok(());
        }
    }
    
    // 如果仍未退出，发送SIGKILL
    kill(pid, Signal::SIGKILL).map_err(|e| format!("发送SIGKILL失败: {}", e))?;
    
    Ok(())
}

#[cfg(windows)]
pub async fn force_terminate_process(pid: i32) -> Result<(), String> {
    use std::process::Command;
    
    let output = Command::new("taskkill")
        .args(["/F", "/PID", &pid.to_string()])
        .output()
        .map_err(|e| format!("执行taskkill失败: {}", e))?;
    
    if !output.status.success() {
        let error = String::from_utf8_lossy(&output.stderr);
        return Err(format!("终止进程失败: {}", error));
    }
    
    Ok(())
}

pub fn is_process_running_by_pid(pid: i32) -> bool {
    use sysinfo::{PidExt, ProcessExt, System, SystemExt};
    
    let mut system = System::new_all();
    system.refresh_processes();
    
    system.processes().iter().any(|(process_pid, _)| {
        process_pid.as_u32() == pid as u32
    })
}
```

## 性能优化

### 数据库优化策略

1. **索引优化**
```sql
-- 针对常用查询路径创建复合索引
CREATE INDEX idx_books_status_composite ON books(title, author, updated_at);
CREATE INDEX idx_book_status_progress ON book_status(status, last_read_at DESC);
CREATE INDEX idx_vector_similarity ON vec_chunks USING vec(embedding vec_cosine_ops);
```

2. **查询优化**
```rust
// 使用JOIN避免N+1查询问题
pub async fn get_books_with_status_optimized() -> Result<Vec<BookWithStatus>> {
    let query = r#"
        SELECT b.*, s.status, s.progress_current, s.progress_total,
               s.last_reading_position, s.reading_time_minutes
        FROM books b 
        LEFT JOIN book_status s ON b.id = s.book_id
        ORDER BY b.updated_at DESC
    "#;
    
    // 一次查询获取所有数据，避免循环查询
}
```

3. **连接池配置**
```rust
// 配置SQLite连接池以支持并发访问
let pool = SqlitePoolOptions::new()
    .max_connections(5)
    .acquire_timeout(Duration::from_secs(3))
    .connect(&database_url).await?;
```

### 前端性能优化

1. **状态管理优化**
```typescript
// 使用Jotai实现精细化状态更新
const bookListAtom = atom<Book[]>([]);
const filteredBooksAtom = atom((get) => {
  const books = get(bookListAtom);
  const searchQuery = get(searchAtom);
  return books.filter(book => 
    book.title.toLowerCase().includes(searchQuery.toLowerCase())
  );
});

// 避免不必要的重渲染
const BookItem = memo(({ book }: { book: Book }) => {
  return <div>{book.title}</div>;
});
```

2. **虚拟化列表**
```typescript
// 使用react-window实现大列表性能优化
import { VariableSizeList as List } from 'react-window';

function BookList({ books }: { books: Book[] }) {
  const getItemSize = (index: number) => books[index].coverHeight + 20;
  
  return (
    <List
      height={600}
      itemCount={books.length}
      itemSize={getItemSize}
      itemData={books}
    >
      {BookRow}
    </List>
  );
}
```

3. **图片懒加载**
```typescript
// 封面图片懒加载优化
const BookCover = ({ coverUrl }: { coverUrl: string }) => {
  const [imageSrc, setImageSrc] = useState<string | null>(null);
  const [isIntersecting, setIsIntersecting] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  
  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting) {
        setIsIntersecting(true);
        observer.disconnect();
      }
    });
    
    if (imgRef.current) {
      observer.observe(imgRef.current);
    }
    
    return () => observer.disconnect();
  }, []);
  
  useEffect(() => {
    if (isIntersecting && coverUrl) {
      setImageSrc(coverUrl);
    }
  }, [isIntersecting, coverUrl]);
  
  return (
    <img
      ref={imgRef}
      src={imageSrc || '/placeholder.png'}
      alt="Book Cover"
      loading="lazy"
    />
  );
};
```

### 向量化性能优化

1. **批量处理**
```rust
// 批量向量化请求减少HTTP开销
pub async fn vectorize_batch(&self, texts: &[String]) -> Result<Vec<Vec<f32>>> {
    let request = EmbeddingRequest {
        input: texts.to_vec(),
        model: self.model_name.clone(),
        encoding_format: "float".to_string(),
    };
    
    let response: EmbeddingResponse = self.client
        .post(&format!("{}/v1/embeddings", self.base_url))
        .json(&request)
        .send().await?
        .json().await?;
    
    Ok(response.data.into_iter().map(|d| d.embedding).collect())
}
```

2. **内存管理**
```rust
// 大文件分块处理控制内存占用
pub async fn process_large_epub(epub_path: &Path) -> Result<()> {
    const CHUNK_SIZE: usize = 1000; // 每次处理1000个分片
    
    let text_chunks = extract_all_chunks(epub_path)?;
    
    for chunk_batch in text_chunks.chunks(CHUNK_SIZE) {
        let embeddings = vectorizer.vectorize_batch(chunk_batch).await?;
        
        // 批量插入数据库
        insert_chunks_batch(chunk_batch, embeddings).await?;
        
        // 显式清理内存
        drop(embeddings);
    }
    
    Ok(())
}
```

3. **缓存策略**
```rust
// 向量结果缓存避免重复计算
use std::collections::HashMap;
use std::hash::{Hash, Hasher};

struct VectorCache {
    cache: HashMap<u64, Vec<f32>>,
    max_size: usize,
}

impl VectorCache {
    pub async fn get_or_compute<F>(&mut self, text: &str, compute: F) -> Result<Vec<f32>>
    where
        F: Future<Output = Result<Vec<f32>>>,
    {
        let key = self.hash_text(text);
        
        if let Some(cached) = self.cache.get(&key) {
            return Ok(cached.clone());
        }
        
        let result = compute.await?;
        
        // 缓存大小控制
        if self.cache.len() >= self.max_size {
            self.evict_oldest();
        }
        
        self.cache.insert(key, result.clone());
        Ok(result)
    }
}
```

这个技术实现文档涵盖了项目的核心技术细节，为开发者提供了深入理解和维护代码所需的详细信息。每个模块都包含了完整的代码示例和实现思路，便于后续的功能扩展和性能优化。 