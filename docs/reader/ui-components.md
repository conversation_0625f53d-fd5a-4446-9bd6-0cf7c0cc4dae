# Reader UI 组件设计分析

## UI 组件架构概览

Reader 的 UI 系统采用模块化设计，包含以下主要组件层次：

```
BookGrid (主容器)
├── HeaderBar (顶部工具栏)
├── FoliateViewer (内容渲染器)
├── FooterBar (底部控制栏)
├── Sidebar (侧边栏系统)
├── Annotator (注释系统)
├── DoubleBorder (装饰边框)
└── HintInfo (提示信息)
```

## 核心 UI 组件详解

### 1. BookGrid - 主布局容器

**职责**：管理阅读器的整体布局和多书籍网格显示

#### 核心特性

```typescript
interface BookGridProps {
  bookKey: string;
  onCloseBook: (bookKey: string) => void;
}

// 动态网格计算
const aspectRatio = window.innerWidth / window.innerHeight;
const gridTemplate = getGridTemplate(1, aspectRatio);

// 安全区域处理
const calcGridInsets = (index: number, count: number) => {
  if (!screenInsets) return { top: 0, right: 0, bottom: 0, left: 0 };
  const { top, right, bottom, left } = getInsetEdges(index, count, aspectRatio);
  return {
    top: top ? screenInsets.top : 0,
    right: right ? screenInsets.right : 0,
    bottom: bottom ? screenInsets.bottom : 0,
    left: left ? screenInsets.left : 0,
  };
};
```

#### 响应式设计

```typescript
// CSS Grid 样式应用
<div
  className={clsx("books-grid relative grid h-full flex-grow")}
  style={{
    gridTemplateColumns: gridTemplate.columns,
    gridTemplateRows: gridTemplate.rows,
  }}
>
```

#### 延迟渲染优化

```typescript
const [shouldRenderViewers, setShouldRenderViewers] = useState(false);

useEffect(() => {
  const timer = setTimeout(() => {
    setShouldRenderViewers(true);
  }, 50); // 延迟50ms避免阻塞UI

  return () => clearTimeout(timer);
}, []);
```

### 2. HeaderBar - 顶部工具栏

**职责**：提供导航、搜索、设置等核心功能入口

#### 组件结构

```typescript
interface HeaderBarProps {
  bookKey: string;
  bookTitle: string;
  isTopLeft: boolean;
  isHoveredAnim: boolean;
  gridInsets: Insets;
  onCloseBook: (bookKey: string) => void;
  onSetSettingsDialogOpen: (open: boolean) => void;
  section: string;
}
```

#### 核心功能

1. **TOC 导航下拉菜单**
   ```typescript
   const [isTocDropdownOpen, setIsTocDropdownOpen] = useState(false);
   
   <DropdownMenu open={isTocDropdownOpen} onOpenChange={handleToggleTocDropdown}>
     <DropdownMenuTrigger asChild>
       <Button variant="ghost" size="sm">
         <TableOfContents className="h-4 w-4" />
       </Button>
     </DropdownMenuTrigger>
     <DropdownMenuContent>
       <TOCView bookKey={bookKey} onItemSelect={handleTocItemSelect} />
     </DropdownMenuContent>
   </DropdownMenu>
   ```

2. **搜索功能集成**
   ```typescript
   <SearchDropdown bookKey={bookKey} />
   ```

3. **设置面板**
   ```typescript
   <SettingsDropdown 
     bookKey={bookKey} 
     onSetSettingsDialogOpen={onSetSettingsDialogOpen} 
   />
   ```

#### 样式设计

```typescript
<div className={clsx(
  "header-bar data-tauri-drag-region pointer-events-auto visible",
  "flex h-11.5 w-full items-center px-2 pl-4",
  "transition-[opacity,margin-top] duration-300",
  "bg-gradient-to-b from-background/90 to-transparent",
  "backdrop-blur-sm border-b border-border/50"
)} />
```

### 3. FooterBar - 底部控制栏

**职责**：显示阅读进度和提供翻页控制

#### 动态显示控制

```typescript
const [showControls, setShowControls] = useState(true);

useEffect(() => {
  const handleIframeMouseMove = (event: MessageEvent) => {
    if (event.data && event.data.type === "iframe-mousemove") {
      const { clientX } = event.data;
      const container = document.getElementById(`gridcell-${bookKey}`);
      
      if (container) {
        const containerRect = container.getBoundingClientRect();
        const containerWidth = containerRect.width;
        
        // 鼠标在边缘区域时显示控制
        const leftBoundary = containerWidth * 0.02;
        const rightBoundary = containerWidth * 0.92;
        
        if (clientX <= leftBoundary || clientX >= rightBoundary) {
          setShowControls(true);
        }
      }
    }
  };
  
  window.addEventListener("message", handleIframeMouseMove);
  return () => window.removeEventListener("message", handleIframeMouseMove);
}, [bookKey]);
```

#### 翻页控制

```typescript
// 集成分页系统
const { prev, next, currentPage, totalPages } = viewPagination(view);

<div className="flex items-center gap-2">
  <Button 
    variant="ghost" 
    size="sm" 
    onClick={prev}
    disabled={currentPage <= 1}
  >
    Previous
  </Button>
  
  <span className="text-sm text-muted-foreground">
    {currentPage} / {totalPages}
  </span>
  
  <Button 
    variant="ghost" 
    size="sm" 
    onClick={next}
    disabled={currentPage >= totalPages}
  >
    Next
  </Button>
</div>
```

### 4. Sidebar - 侧边栏系统

#### 架构设计

```typescript
const MIN_SIDEBAR_WIDTH = 0.05;
const MAX_SIDEBAR_WIDTH = 0.45;
const VELOCITY_THRESHOLD = 0.5;

interface SideBarProps {
  onGoToLibrary: () => void;
}
```

#### 可调整宽度系统

```typescript
const { isDragging, dragProps } = useDrag({
  onDrag: (deltaX) => {
    const newWidth = Math.max(
      MIN_SIDEBAR_WIDTH,
      Math.min(MAX_SIDEBAR_WIDTH, sideBarWidth + deltaX / window.innerWidth)
    );
    handleSideBarResize(newWidth);
  },
  onDragEnd: (velocity) => {
    // 根据拖拽速度决定是否自动展开/收起
    if (Math.abs(velocity.x) > VELOCITY_THRESHOLD) {
      const shouldExpand = velocity.x > 0;
      setSideBarVisible(shouldExpand);
    }
  }
});
```

#### 响应式行为

```typescript
const isMobile = window.innerWidth < 640;

const {
  sideBarWidth,
  isSideBarPinned,
  isSideBarVisible,
  setSideBarVisible,
  handleSideBarResize,
  handleSideBarTogglePin,
} = useSidebar(
  settings.globalReadSettings.sideBarWidth,
  isMobile ? false : settings.globalReadSettings.isSideBarPinned,
  isMobile ? false : settings.globalReadSettings.isSideBarVisible
);
```

#### 内容组件

1. **BookCard - 书籍信息卡片**
   ```typescript
   <BookCard 
     bookKey={sideBarBookKey} 
     onGoToLibrary={onGoToLibrary} 
   />
   ```

2. **SidebarContent - 主要内容区域**
   ```typescript
   <SidebarContent 
     bookKey={sideBarBookKey}
     searchResults={searchResults}
     onSearchResultClick={handleSearchResultClick}
   />
   ```

3. **SearchBar - 搜索栏**
   ```typescript
   <SearchBar
     visible={isSearchBarVisible}
     onSearch={handleSearch}
     onToggle={setIsSearchBarVisible}
   />
   ```

### 5. Annotator - 注释系统

#### 核心组件结构

```typescript
interface AnnotatorProps {
  bookKey: string;
  view: FoliateView | null;
  containerRef: RefObject<HTMLDivElement>;
}

const Annotator: React.FC<AnnotatorProps> = ({ bookKey, view, containerRef }) => {
  // 文本选择处理
  const { selectedText, selectionRect } = useTextSelector(view);
  
  // 注释弹窗管理
  const [showPopup, setShowPopup] = useState(false);
  const [annotations, setAnnotations] = useState<Annotation[]>([]);
  
  return (
    <>
      {showPopup && selectionRect && (
        <AnnotationPopup
          position={selectionRect}
          selectedText={selectedText}
          onCreateAnnotation={handleCreateAnnotation}
          onAskAI={handleAskAI}
        />
      )}
      
      {annotations.map(annotation => (
        <AnnotationMarker
          key={annotation.id}
          annotation={annotation}
          onClick={() => showAnnotationDetails(annotation)}
        />
      ))}
    </>
  );
};
```

#### 文本选择处理

```typescript
const useTextSelector = (view: FoliateView | null) => {
  const [selectedText, setSelectedText] = useState("");
  const [selectionRect, setSelectionRect] = useState<DOMRect | null>(null);
  
  useEffect(() => {
    if (!view) return;
    
    const handleSelection = () => {
      const selection = view.renderer.getSelection();
      if (selection && !selection.isCollapsed) {
        setSelectedText(selection.toString());
        setSelectionRect(selection.getRangeAt(0).getBoundingClientRect());
      } else {
        setSelectedText("");
        setSelectionRect(null);
      }
    };
    
    // 监听选择变化
    document.addEventListener("selectionchange", handleSelection);
    return () => document.removeEventListener("selectionchange", handleSelection);
  }, [view]);
  
  return { selectedText, selectionRect };
};
```

### 6. DoubleBorder - 装饰边框

#### 条件渲染

```typescript
{viewSettings?.doubleBorder && (
  <DoubleBorder 
    gridInsets={gridInsets}
    className="absolute inset-0 pointer-events-none"
  />
)}
```

#### 样式实现

```typescript
const DoubleBorder: React.FC<{
  gridInsets: Insets;
  className?: string;
}> = ({ gridInsets, className }) => {
  return (
    <div 
      className={clsx(
        "border-2 border-border",
        "before:content-[''] before:absolute before:inset-1",
        "before:border before:border-border",
        className
      )}
      style={{
        margin: `${gridInsets.top}px ${gridInsets.right}px ${gridInsets.bottom}px ${gridInsets.left}px`
      }}
    />
  );
};
```

## UI 交互设计

### 1. 悬停动画系统

```typescript
const [isHoveredAnim, setIsHoveredAnim] = useState(false);

<div
  onMouseEnter={() => {
    setIsHoveredAnim(true);
    setHoveredBookKey(bookKey);
  }}
  onMouseLeave={() => {
    setIsHoveredAnim(false);
    setHoveredBookKey(null);
  }}
  className={clsx(
    "transition-all duration-300",
    isHoveredAnim && "shadow-lg transform scale-[1.02]"
  )}
>
```

### 2. 触摸手势支持

```typescript
// 在 useIframeEvents 中处理触摸事件
export const useTouchEvent = (bookKey: string, handleContinuousScroll: Function) => {
  const [touchStart, setTouchStart] = useState<{ x: number; y: number } | null>(null);
  const [lastTouchMove, setLastTouchMove] = useState<{ x: number; y: number } | null>(null);

  const handleTouchStart = useCallback((event: TouchEvent) => {
    const touch = event.touches[0];
    if (touch) {
      setTouchStart({ x: touch.clientX, y: touch.clientY });
    }
  }, []);

  const handleTouchMove = useCallback((event: TouchEvent) => {
    const touch = event.touches[0];
    if (touch && touchStart) {
      setLastTouchMove({ x: touch.clientX, y: touch.clientY });
      
      // 计算滑动方向和距离
      const deltaX = touch.clientX - touchStart.x;
      const deltaY = touch.clientY - touchStart.y;
      
      // 处理翻页手势
      if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
        if (deltaX > 0) {
          // 向右滑动 - 上一页
          handlePageFlip('prev');
        } else {
          // 向左滑动 - 下一页  
          handlePageFlip('next');
        }
      }
    }
  }, [touchStart, handlePageFlip]);

  return { handleTouchStart, handleTouchMove };
};
```

### 3. 键盘快捷键系统

```typescript
// useBookShortcuts.ts
const useBookShortcuts = (bookKey: string) => {
  const shortcuts = useMemo(() => ({
    'ArrowLeft': () => getView(bookKey)?.goToPrevious(),
    'ArrowRight': () => getView(bookKey)?.goToNext(),
    'Home': () => getView(bookKey)?.goToFraction(0),
    'End': () => getView(bookKey)?.goToFraction(1),
    'f': () => toggleFullscreen(),
    't': () => toggleTheme(),
    's': () => toggleSidebar(),
    '/': () => focusSearch(),
  }), [bookKey]);

  useShortcuts(shortcuts);
};
```

## 性能优化策略

### 1. 虚拟化长列表

```typescript
// 在 TOC 和搜索结果中使用虚拟化
import { FixedSizeList as List } from 'react-window';

const TOCVirtualList = ({ items }: { items: TOCItem[] }) => (
  <List
    height={400}
    itemCount={items.length}
    itemSize={32}
    itemData={items}
  >
    {({ index, style, data }) => (
      <div style={style}>
        <TOCItem item={data[index]} />
      </div>
    )}
  </List>
);
```

### 2. 懒加载组件

```typescript
// 延迟加载复杂组件
const LazyAnnotator = lazy(() => import('./annotator/Annotator'));

{shouldRenderViewers && (
  <Suspense fallback={<div>Loading annotator...</div>}>
    <LazyAnnotator bookKey={bookKey} view={jotaiView} containerRef={containerRef} />
  </Suspense>
)}
```

### 3. 事件委托

```typescript
// 使用事件委托减少事件监听器数量
useEffect(() => {
  const handleGlobalClick = (event: MouseEvent) => {
    const target = event.target as Element;
    
    // 注释相关点击
    if (target.closest('.annotation-marker')) {
      handleAnnotationClick(event);
    }
    
    // 链接点击
    if (target.closest('a[href]')) {
      handleLinkClick(event);
    }
    
    // 图片点击
    if (target.closest('img')) {
      handleImageClick(event);
    }
  };
  
  document.addEventListener('click', handleGlobalClick);
  return () => document.removeEventListener('click', handleGlobalClick);
}, []);
```

## 无障碍设计

### 1. ARIA 标签

```typescript
<button
  aria-label={_("Go to previous page")}
  aria-keyshortcuts="ArrowLeft"
  onClick={handlePrevPage}
>
  <ChevronLeft className="h-4 w-4" />
</button>
```

### 2. 焦点管理

```typescript
// 模态框打开时管理焦点
const useFocusManagement = (isOpen: boolean) => {
  const modalRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    if (isOpen && modalRef.current) {
      const focusableElements = modalRef.current.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      
      if (focusableElements.length > 0) {
        (focusableElements[0] as HTMLElement).focus();
      }
    }
  }, [isOpen]);
  
  return modalRef;
};
```

### 3. 高对比度支持

```typescript
// 支持系统高对比度模式
@media (prefers-contrast: high) {
  .header-bar {
    @apply border-2 border-solid;
    background: var(--background);
  }
  
  .annotation-marker {
    @apply outline outline-2 outline-primary;
  }
}
```

这种精心设计的 UI 组件系统为 Reader 提供了直观、流畅且功能丰富的用户界面，同时保持了良好的性能和可访问性。 