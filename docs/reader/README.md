# Reader 组件设计文档

本目录包含关于阅读器组件设计的详细文档。

## 文档结构

- **architecture.md** - 阅读器架构设计概览
- **state-management.md** - 状态管理设计详解
- **foliate-integration.md** - Foliate.js 集成详解
- **ui-components.md** - UI 组件设计分析

## 概述

Reader 组件是整个应用的核心功能，负责书籍的渲染、交互和管理。它采用了分层架构设计，结合了 Jotai 和 Zustand 两种状态管理方案，集成了 Foliate.js 作为底层渲染引擎。

## 核心特性

- **多书籍支持**: 支持同时打开多本书籍
- **响应式布局**: 自适应网格布局系统
- **丰富的交互**: 支持注释、搜索、导航等
- **高性能渲染**: 基于 Foliate.js 的高效渲染
- **状态持久化**: 阅读进度和设置的持久化存储
- **可配置视图**: 丰富的显示设置和主题支持

## 技术栈

- **React + TypeScript**: 前端框架
- **Foliate.js**: 电子书渲染引擎
- **Jotai**: 原子化状态管理（本地状态）
- **Zustand**: 全局状态管理
- **Tailwind CSS**: 样式系统
- **Tauri**: 桌面应用程序接口 