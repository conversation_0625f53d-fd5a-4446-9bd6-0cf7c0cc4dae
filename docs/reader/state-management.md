# Reader 状态管理设计

## 状态管理策略概述

Reader 组件采用了 **双重状态管理架构**，结合 Jotai 和 Zustand 两种状态管理方案，各司其职：

- **Jotai**: 管理单个阅读器实例的本地状态
- **Zustand**: 管理全局的多书籍状态和应用级状态

这种设计能够在保持性能的同时，提供灵活的状态管理能力。

## Jotai 原子状态 (本地状态)

### 核心原子定义

位于 `atoms/readerAtoms.ts`：

```typescript
// 书籍数据原子
export const bookDataAtom = atom<BookData | null>(null);

// 阅读进度原子
export const progressAtom = atom<BookProgress | null>(null);

// 视图实例原子
export const viewAtom = atom<FoliateView | null>(null);

// 视图设置原子
export const viewSettingsAtom = atom<ViewSettings | null>(null);
```

### 动作原子 (Action Atoms)

```typescript
// 书籍数据初始化动作
export const initBookDataActionAtom = atom(
  null,
  async (get, set, { bookKey, envConfig }: InitBookDataParams) => {
    try {
      const activeBookId = bookKey.split("-")[0]!;
      
      // 加载书籍数据
      const bookData = await loadBookDataFromDisk(envConfig, activeBookId);
      
      // 更新原子状态
      set(bookDataAtom, bookData);
      set(bookKeyAtom, bookKey);
      
      // 同时更新全局状态
      const { setBookData } = useBookDataStore.getState();
      setBookData(activeBookId, bookData);
      
    } catch (error) {
      console.error('Failed to initialize book data:', error);
    }
  }
);
```

### 状态更新模式

Jotai 原子采用不可变更新模式：

```typescript
// 在 useFoliateViewer 中更新进度
const progressRelocateHandler = (event: Event) => {
  const detail = (event as CustomEvent).detail;
  
  // 更新 Jotai 原子
  setJotaiProgress({
    location: detail.cfi,
    sectionHref: detail.tocItem?.href || "",
    sectionLabel: detail.tocItem?.label || "",
    sectionId: detail.tocItem?.id ?? 0,
    section: detail.section,
    pageinfo: detail.location,
    timeinfo: detail.time,
    range: detail.range,
  });
};
```

## Zustand 全局状态

### readerStore 核心状态结构

```typescript
interface ViewState {
  key: string;                    // 视图唯一标识
  view: FoliateView | null;       // Foliate 视图实例
  isPrimary: boolean;             // 是否为主视图
  loading: boolean;               // 加载状态
  error: string | null;           // 错误状态
  progress: BookProgress | null;  // 阅读进度
  ribbonVisible: boolean;         // 书签显示状态
  ttsEnabled: boolean;            // TTS 开启状态
  gridInsets: Insets | null;      // 网格边距
  viewSettings: ViewSettings | null; // 视图设置
}

interface ReaderStore {
  viewStates: { [key: string]: ViewState }; // 所有视图状态
  bookKeys: string[];                        // 当前打开的书籍键列表
  hoveredBookKey: string | null;             // 当前悬停的书籍键
  
  // 状态管理方法
  setBookKeys: (keys: string[]) => void;
  setView: (key: string, view: FoliateView) => void;
  getView: (key: string | null) => FoliateView | null;
  setProgress: (key: string, ...) => void;
  // ...其他方法
}
```

### 视图状态初始化

```typescript
initViewState: async (envConfig: EnvConfigType, id: string, key: string, isPrimary = true) => {
  const booksData = useBookDataStore.getState().booksData;
  const bookData = booksData[id];
  
  set((state) => ({
    viewStates: {
      ...state.viewStates,
      [key]: {
        key,
        view: null,
        isPrimary,
        loading: true,
        error: null,
        progress: null,
        ribbonVisible: false,
        ttsEnabled: false,
        gridInsets: null,
        viewSettings: null,
      }
    }
  }));
  
  // 异步加载书籍数据和配置
  if (!bookData) {
    await loadAndCacheBookData(envConfig, id);
  }
  
  // 加载完成后更新状态
  set((state) => ({
    viewStates: {
      ...state.viewStates,
      [key]: { ...state.viewStates[key]!, loading: false }
    }
  }));
}
```

## 状态同步机制

### Jotai ↔ Zustand 双向同步

Reader 组件需要在 Jotai 和 Zustand 之间保持状态同步：

#### 1. 进度同步

```typescript
// useFoliateViewer.ts
const progressRelocateHandler = (event: Event) => {
  const detail = (event as CustomEvent).detail;
  
  // 更新 Zustand 全局状态
  setProgress(bookKey, detail.cfi, detail.tocItem, detail.section, detail.location, detail.time, detail.range);
  
  // 同时更新 Jotai 本地状态
  setJotaiProgress({
    location: detail.cfi,
    sectionHref: detail.tocItem?.href || "",
    sectionLabel: detail.tocItem?.label || "",
    // ...
  });
};
```

#### 2. 视图设置同步

```typescript
// 设置更新时的双向同步
const updateViewSettings = useCallback((newSettings: ViewSettings) => {
  // 更新 Jotai 原子
  setJotaiViewSettings(newSettings);
  
  // 更新 Zustand store
  setViewSettings(bookKey, newSettings);
  
  // 如果是主视图，还需要持久化到配置文件
  if (isPrimary) {
    saveBookConfig(bookKey.split("-")[0], { viewSettings: newSettings });
  }
}, [bookKey, isPrimary]);
```

#### 3. 视图实例同步

```typescript
// 视图创建时的同步
useEffect(() => {
  const openBook = async () => {
    // 创建 Foliate 视图
    const view = wrappedFoliateView(document.createElement("foliate-view"));
    await view.open(bookDoc);
    
    // 同时更新两个状态管理系统
    viewRef.current = view;
    setFoliateView(bookKey, view);  // Zustand
    setJotaiView(view);             // Jotai
  };
  
  openBook();
}, []);
```

## 状态持久化策略

### 1. 配置文件持久化

通过 Tauri 接口将关键状态持久化到本地文件：

```typescript
// 保存阅读进度
const saveProgress = async (bookKey: string, progress: BookProgress) => {
  const bookId = bookKey.split("-")[0];
  await invoke("save_book_progress", {
    bookId,
    location: progress.location,
    sectionHref: progress.sectionHref,
    // ...
  });
};

// 保存视图设置
const saveViewSettings = async (bookKey: string, settings: ViewSettings) => {
  const bookId = bookKey.split("-")[0];
  await invoke("save_book_config", {
    bookId,
    config: { viewSettings: settings }
  });
};
```

### 2. 内存缓存策略

使用 `bookDataStore` 作为内存缓存：

```typescript
// bookDataStore.ts
interface BookDataStore {
  booksData: { [id: string]: BookData };
  setBookData: (id: string, data: BookData) => void;
  getBookData: (id: string) => BookData | undefined;
  clearBookData: (id: string) => void;
}
```

## 性能优化

### 1. 选择性更新

只更新实际变化的状态部分：

```typescript
// 只在必要时更新视图设置
const updateOnlyChangedSettings = useCallback((newSettings: Partial<ViewSettings>) => {
  setJotaiViewSettings(prev => {
    if (!prev) return newSettings as ViewSettings;
    
    // 浅比较，只有变化时才更新
    const hasChanges = Object.keys(newSettings).some(key => 
      prev[key as keyof ViewSettings] !== newSettings[key as keyof ViewSettings]
    );
    
    return hasChanges ? { ...prev, ...newSettings } : prev;
  });
}, []);
```

### 2. 批量更新

避免频繁的状态更新：

```typescript
// 批量更新多个相关状态
const batchUpdateBookState = useCallback((updates: {
  progress?: BookProgress;
  viewSettings?: ViewSettings;
  gridInsets?: Insets;
}) => {
  set((state) => ({
    viewStates: {
      ...state.viewStates,
      [bookKey]: {
        ...state.viewStates[bookKey]!,
        ...updates
      }
    }
  }));
}, [bookKey]);
```

### 3. 惰性初始化

推迟非关键状态的初始化：

```typescript
// 延迟加载视图设置
const viewSettingsAtom = atom<ViewSettings | null>(async (get) => {
  const bookData = get(bookDataAtom);
  if (!bookData) return null;
  
  // 只在需要时加载设置
  return await loadViewSettings(bookData.book.id);
});
```

## 状态调试和监控

### 开发工具集成

```typescript
// 开发环境下的状态监控
if (process.env.NODE_ENV === 'development') {
  // Jotai 状态监控
  const stateAtom = atom((get) => ({
    bookData: get(bookDataAtom),
    progress: get(progressAtom),
    viewSettings: get(viewSettingsAtom),
  }));
  
  // Zustand 状态监控
  useReaderStore.subscribe((state) => {
    console.log('ReaderStore updated:', state);
  });
}
```

### 状态同步验证

```typescript
// 开发环境下验证状态同步
const validateStateSync = useCallback(() => {
  const jotaiProgress = jotaiProgressValue;
  const zustandProgress = getProgress(bookKey);
  
  if (jotaiProgress?.location !== zustandProgress?.location) {
    console.warn('State sync mismatch:', { jotaiProgress, zustandProgress });
  }
}, [jotaiProgressValue, bookKey]);
```

这种双重状态管理架构为 Reader 组件提供了既灵活又高性能的状态管理能力，能够支持复杂的多书籍阅读场景，同时保持代码的可维护性和可测试性。 