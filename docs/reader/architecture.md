# Reader 组件架构设计

## 组件层次结构

Reader 组件采用分层架构，从外到内分为以下层次：

```
Reader.tsx (入口层)
└── ReaderContent.tsx (内容层)
    └── BookGrid.tsx (布局层)
        └── FoliateViewer.tsx (渲染层)
```

### 1. 入口层 - Reader.tsx

**职责**：
- 初始化阅读器环境
- 加载全局设置
- 提供最外层的上下文

**核心功能**：
- 调用 `useInitializeLibrary` 初始化书籍库
- 设置 Jotai Provider
- 渲染 `ReaderContent` 组件

### 2. 内容层 - ReaderContent.tsx

**职责**：
- 处理书籍加载逻辑
- 管理多书籍状态
- 事件监听和响应

**核心功能**：
- 通过 `useBooksManager` 管理书籍的打开/关闭
- 监听 URL 参数变化，同步书籍状态
- 处理书籍加载完成后的初始化
- 渲染 `BookGrid` 组件

**关键逻辑**：
```typescript
// 从URL获取书籍ID并初始化
useEffect(() => {
  const ids = searchParams.get("ids")?.split(",") || [];
  // 处理书籍加载逻辑
}, [searchParams]);
```

### 3. 布局层 - BookGrid.tsx

**职责**：
- 管理阅读器的网格布局
- 处理多书籍的视觉呈现
- 协调各种UI组件

**核心功能**：
- 使用 `getGridTemplate` 计算网格布局
- 渲染 `HeaderBar`、`FooterBar`、`FoliateViewer`
- 管理 `Annotator` 注释系统
- 处理设备适配和响应式布局

**布局计算**：
```typescript
const aspectRatio = window.innerWidth / window.innerHeight;
const gridTemplate = getGridTemplate(1, aspectRatio);
```

### 4. 渲染层 - FoliateViewer.tsx

**职责**：
- 集成 Foliate.js 渲染引擎
- 管理底层阅读器实例

**核心功能**：
- 调用 `useFoliateViewer` hook
- 提供渲染容器
- 处理鼠标和触摸事件

## 核心 Hooks 架构

### useFoliateViewer (核心引擎)

**职责**：集成 Foliate.js，处理书籍渲染的所有底层逻辑

**主要功能模块**：

1. **视图管理**：
   - 创建和管理 Foliate 视图实例
   - 处理视图的生命周期
   - 管理多个视图的并行状态

2. **样式和布局**：
   - 动态计算容器尺寸
   - 应用用户设置的视图样式
   - 处理响应式布局更新

3. **事件处理**：
   - 文档加载事件
   - 进度更新事件
   - 用户交互事件

4. **内容转换**：
   - CSS 样式转换
   - HTML 内容处理
   - 图片和媒体处理

### useBooksManager (书籍管理)

**职责**：管理多书籍的打开、关闭和状态同步

**核心方法**：
- `appendBook()`: 添加新书籍
- `dismissBook()`: 关闭书籍
- `openParallelView()`: 打开并行视图

### usePagination (翻页控制)

**职责**：处理翻页逻辑和导航

**支持的交互方式**：
- 鼠标点击翻页
- 键盘快捷键
- 触摸手势
- 滚轮操作

## 状态管理架构

### 双重状态管理策略

Reader 组件采用 **Jotai + Zustand** 的混合状态管理：

**Jotai (本地状态)**：
- `bookDataAtom`: 单本书籍数据
- `progressAtom`: 阅读进度
- `viewAtom`: 视图实例引用
- `viewSettingsAtom`: 视图设置

**Zustand (全局状态)**：
- `readerStore`: 多书籍的全局状态
- `bookDataStore`: 书籍数据缓存
- `sidebarStore`: 侧边栏状态

### 状态同步机制

状态在 Jotai 和 Zustand 之间保持同步：

```typescript
// 双向同步示例
const progressRelocateHandler = (event: Event) => {
  // 更新 Zustand store
  setProgress(bookKey, detail.cfi, detail.tocItem, detail.section, detail.location, detail.time, detail.range);
  
  // 同时更新 Jotai atom
  setJotaiProgress({
    location: detail.cfi,
    sectionHref: detail.tocItem?.href || "",
    // ...
  });
};
```

## UI 组件架构

### HeaderBar 组件

**功能**：
- 显示书籍标题和章节信息
- 提供 TOC 导航下拉菜单
- 集成搜索功能
- 设置入口

### FooterBar 组件

**功能**：
- 显示阅读进度信息
- 提供翻页控制按钮
- 鼠标悬停时的控制显示

### Sidebar 系统

**组件结构**：
- `SideBar.tsx`: 主侧边栏容器
- `Content.tsx`: 侧边栏内容区域
- `TOCView.tsx`: 目录视图
- `SearchResults.tsx`: 搜索结果展示

**特性**：
- 可调整宽度
- 可固定/悬浮切换
- 响应式设计

### Annotator 系统

**功能**：
- 文本选择和高亮
- 注释创建和管理
- AI 对话集成

## 性能优化策略

### 1. 延迟渲染

```typescript
const [shouldRenderViewers, setShouldRenderViewers] = useState(false);

useEffect(() => {
  const timer = setTimeout(() => {
    setShouldRenderViewers(true);
  }, 50);
  return () => clearTimeout(timer);
}, []);
```

### 2. 事件处理优化

- 使用 `useCallback` 优化事件处理函数
- 防抖和节流机制
- 事件委托和清理

### 3. 内存管理

- 视图实例的正确清理
- 定时器和事件监听器的清理
- 大对象的及时释放

### 4. 渲染优化

- 条件渲染避免不必要的组件创建
- 使用 React.memo 防止重复渲染
- 虚拟化长列表（如 TOC）

## 扩展性设计

### 插件化架构

Reader 组件支持通过以下方式扩展：

1. **自定义转换器**: 通过 `transformService` 添加内容处理插件
2. **主题系统**: 动态主题和样式定制
3. **交互扩展**: 通过事件系统添加新的交互方式
4. **UI 组件扩展**: 模块化的 UI 组件系统

### 配置系统

所有核心功能都支持配置：
- 视图设置 (字体、间距、颜色等)
- 交互行为 (快捷键、手势等)
- 显示选项 (侧边栏、工具栏等)

这种架构设计确保了 Reader 组件既功能强大又易于维护和扩展。 