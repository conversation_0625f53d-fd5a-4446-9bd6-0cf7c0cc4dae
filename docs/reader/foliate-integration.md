# Foliate.js 集成详解

## 概述

Foliate.js 是一个现代化的电子书渲染引擎，我们的 Reader 组件通过深度集成实现了高性能的书籍渲染和交互功能。本文档详细描述了集成架构、核心功能和最佳实践。

## 集成架构

### 核心集成点

```typescript
// useFoliateViewer.ts - 主要集成入口
export const useFoliateViewer = (bookKey: string, bookDoc: BookDoc, config: BookConfig, insets: Insets) => {
  const viewRef = useRef<FoliateView | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const isViewCreated = useRef(false);
  
  // ... 集成逻辑
};
```

### 视图创建和生命周期管理

```typescript
useEffect(() => {
  if (isViewCreated.current) return;
  isViewCreated.current = true;

  const openBook = async () => {
    // 1. 动态导入 Foliate.js
    await import("foliate-js/view.js");
    
    // 2. 创建包装后的视图实例
    const view = wrappedFoliateView(document.createElement("foliate-view") as FoliateView);
    view.id = `foliate-view-${bookKey}`;
    
    // 3. 添加到 DOM
    document.body.append(view);
    containerRef.current?.appendChild(view);
    
    // 4. 打开书籍
    await view.open(bookDoc);
    
    // 5. 配置和初始化
    await configureView(view);
  };

  openBook();
  
  // 清理逻辑
  return () => {
    if (viewRef.current) {
      const view = viewRef.current;
      viewRef.current = null;
      isViewCreated.current = false;
      
      // 延迟清理避免竞态条件
      setTimeout(() => {
        try {
          if (view.close) view.close();
          if (view.remove) view.remove();
        } catch (cleanupError) {
          console.warn("Error in delayed cleanup:", cleanupError);
        }
      }, 50);
    }
  };
}, []);
```

## 核心功能集成

### 1. 视图配置系统

#### 样式应用

```typescript
const performUpdate = useCallback((dimensions: { width: number; height: number }) => {
  if (!viewRef.current?.renderer) return;
  
  // 获取最新的视图设置
  const viewSettings = getViewSettings?.(bookKey) ?? jotaiViewSettings;
  if (!viewSettings) return;
  
  // 计算布局属性
  const isVertical = !!viewSettings.vertical;
  const containerSize = isVertical ? dimensions.height : dimensions.width;
  
  // 计算间距
  const g = (viewSettings.gapPercent ?? 7) / 100;
  const gapPx = (-g / (g - 1)) * containerSize;
  
  // 设置渲染器属性
  viewRef.current.renderer.setAttribute("max-column-count", String(computedMaxColumnCount));
  viewRef.current.renderer.setAttribute("max-inline-size", `${computedMaxInlineSize}px`);
  viewRef.current.renderer.setStyles?.(getStyles(viewSettings));
}, [jotaiViewSettings, getViewSettings, bookKey]);
```

#### 列模式控制

```typescript
// 支持单列、双列、自动模式
const columnMode = (viewSettings as any).columnMode ?? "auto";

if (!viewSettings.scrolled) {
  if (columnMode === "one") {
    computedMaxColumnCount = 1;
    computedMaxInlineSize = Math.max(containerSize, 2000);
  } else if (columnMode === "two") {
    computedMaxColumnCount = 2;
    const target = Math.floor(containerSize / 2 - gapPx);
    computedMaxInlineSize = Math.max(120, target);
  }
}
```

### 2. 事件系统集成

#### 文档事件处理

```typescript
const docLoadHandler = (event: Event) => {
  const detail = (event as CustomEvent).detail;
  if (detail.doc) {
    const writingDir = viewRef.current?.renderer.setStyles && getDirection(detail.doc);
    const viewSettings = jotaiViewSettings;
    
    // 动态检测书籍方向
    viewSettings.vertical = writingDir?.vertical || viewSettings.writingMode.includes("vertical");
    viewSettings.rtl = writingDir?.rtl || getDirFromUILanguage() === "rtl";
    
    // 应用字体
    mountAdditionalFonts(detail.doc, isCJKLang(bookData.book?.primaryLanguage));
    
    // 应用样式
    if (bookDoc.rendition?.layout === "pre-paginated") {
      applyFixedlayoutStyles(detail.doc, viewSettings);
    }
    applyImageStyle(detail.doc);
    
    // 语法高亮
    if (viewSettings.codeHighlighting) {
      manageSyntaxHighlighting(detail.doc, viewSettings);
    }
    
    // 添加事件监听器
    addIframeEventListeners(detail.doc, bookKey);
  }
};
```

#### 进度跟踪

```typescript
const progressRelocateHandler = (event: Event) => {
  const detail = (event as CustomEvent).detail;
  
  // 更新多个状态管理系统
  setProgress(bookKey, detail.cfi, detail.tocItem, detail.section, detail.location, detail.time, detail.range);
  
  setJotaiProgress({
    location: detail.cfi,
    sectionHref: detail.tocItem?.href || "",
    sectionLabel: detail.tocItem?.label || "",
    sectionId: detail.tocItem?.id ?? 0,
    section: detail.section,
    pageinfo: detail.location,
    timeinfo: detail.time,
    range: detail.range,
  });
};
```

### 3. 内容转换系统

#### 动态内容处理

```typescript
const getDocTransformHandler = ({ width, height }: { width: number; height: number }) => {
  return (event: Event) => {
    const { detail } = event as CustomEvent;
    
    detail.data = Promise.resolve(detail.data)
      .then((data) => {
        const viewSettings = jotaiViewSettings;
        
        // CSS 样式表转换
        if (viewSettings && detail.type === "text/css") {
          return transformStylesheet(width, height, data);
        }
        
        // HTML 内容转换
        if (viewSettings && detail.type === "application/xhtml+xml") {
          const ctx = {
            bookKey,
            viewSettings,
            content: data,
            transformers: ["punctuation", "footnote"], // 可配置的转换器
          };
          return transformContent(ctx);
        }
        
        return data;
      })
      .catch((e) => {
        console.error(new Error(`Failed to load ${detail.name}`, { cause: e }));
        return "";
      });
  };
};
```

### 4. 交互系统

#### 鼠标和触摸事件

```typescript
// iframe 事件桥接
const addIframeEventListeners = (doc: Document, bookKey: string) => {
  if (!doc.isEventListenersAdded) {
    doc.isEventListenersAdded = true;
    
    // 键盘事件
    doc.addEventListener("keydown", handleKeydown.bind(null, bookKey));
    
    // 鼠标事件
    doc.addEventListener("mousedown", handleMousedown.bind(null, bookKey));
    doc.addEventListener("mouseup", handleMouseup.bind(null, bookKey));
    doc.addEventListener("mousemove", handleMouseMove.bind(null, bookKey));
    doc.addEventListener("click", handleClick.bind(null, bookKey));
    doc.addEventListener("wheel", handleWheel.bind(null, bookKey));
    
    // 触摸事件
    doc.addEventListener("touchstart", handleTouchStart.bind(null, bookKey));
    doc.addEventListener("touchmove", handleTouchMove.bind(null, bookKey));
    doc.addEventListener("touchend", handleTouchEnd.bind(null, bookKey));
  }
};
```

#### 翻页控制集成

```typescript
const { handlePageFlip, handleContinuousScroll } = usePagination(
  bookKey,
  viewRef,
  containerRef as RefObject<HTMLDivElement>,
);

// 鼠标事件处理器
const mouseHandlers = useMouseEvent(bookKey, handlePageFlip, handleContinuousScroll);

// 触摸事件处理器
const touchHandlers = useTouchEvent(bookKey, handleContinuousScroll);
```

## 高级功能

### 1. 并行视图支持

```typescript
const docRelocateHandler = (event: Event) => {
  const detail = (event as CustomEvent).detail;
  if (detail.reason !== "scroll" && detail.reason !== "page") return;
  
  // 同步并行视图
  const parallelViews = getParallels(bookKey);
  if (parallelViews && parallelViews.size > 0) {
    parallelViews.forEach((key) => {
      if (key !== bookKey) {
        getView(key)?.renderer.goTo?.({ 
          index: detail.index, 
          anchor: detail.fraction 
        });
      }
    });
  }
};
```

### 2. 搜索高亮集成

```typescript
// 单击清除搜索高亮
useEffect(() => {
  const handleIframeSingleClick = (event: MessageEvent) => {
    if (event?.data?.type === "iframe-single-click" && 
        event?.data?.bookKey === bookKey) {
      try {
        getView(bookKey)?.clearSearch();
      } catch (e) {
        console.warn("Failed to clear search on single click:", e);
      }
    }
  };
  
  window.addEventListener("message", handleIframeSingleClick);
  return () => window.removeEventListener("message", handleIframeSingleClick);
}, [bookKey, getView]);
```

### 3. 主题和样式动态更新

```typescript
useEffect(() => {
  if (viewRef.current?.renderer) {
    const viewSettings = jotaiViewSettings;
    if (!viewSettings) return;
    
    // 应用新样式
    viewRef.current.renderer.setStyles?.(getStyles(viewSettings));
    
    // 固定布局特殊处理
    if (bookDoc.rendition?.layout === "pre-paginated") {
      const docs = viewRef.current.renderer.getContents();
      docs.forEach(({ doc }) => applyFixedlayoutStyles(doc, viewSettings));
    }
  }
}, [themeCode, isDarkMode, jotaiViewSettings?.overrideColor, jotaiViewSettings?.invertImgColorInDark]);
```

## 性能优化

### 1. 延迟加载

```typescript
// 动态导入减少初始包大小
await import("foliate-js/view.js");
```

### 2. 事件优化

```typescript
// 防抖更新
useEffect(() => {
  const handleFoliateResize = (event: CustomEvent) => {
    const { bookIds } = event.detail;
    if (bookIds?.includes(bookKey)) {
      setTimeout(() => manualUpdate(), 100); // 延迟执行避免频繁更新
    }
  };
  
  window.addEventListener("foliate-resize-update", handleFoliateResize as EventListener);
  return () => window.removeEventListener("foliate-resize-update", handleFoliateResize as EventListener);
}, [bookKey, manualUpdate]);
```

### 3. 内存管理

```typescript
// 及时清理视图实例
const cleanup = () => {
  if (viewRef.current) {
    try {
      const view = viewRef.current;
      viewRef.current = null;
      isViewCreated.current = false;
      
      // 延迟清理避免竞态条件
      setTimeout(() => {
        try {
          if (view.close) view.close();
          if (view.remove) view.remove();
        } catch (cleanupError) {
          console.warn("Error in delayed cleanup:", cleanupError);
        }
      }, 50);
    } catch (error) {
      console.warn("Error during foliate view cleanup:", error);
    }
  }
};
```

## 自定义扩展

### 1. 自定义转换器

```typescript
// 在 transformService 中注册新的转换器
const customTransformers = {
  "custom-formatter": (content: string, settings: ViewSettings) => {
    // 自定义内容处理逻辑
    return processCustomFormat(content, settings);
  }
};
```

### 2. 自定义样式处理

```typescript
// 扩展样式系统
const applyCustomStyles = (doc: Document, settings: ViewSettings) => {
  if (settings.customStyleEnabled) {
    const customCSS = generateCustomCSS(settings);
    const styleEl = doc.createElement('style');
    styleEl.textContent = customCSS;
    doc.head.appendChild(styleEl);
  }
};
```

## 调试和监控

### 1. 开发工具

```typescript
if (process.env.NODE_ENV === 'development') {
  // 视图状态监控
  window.__FOLIATE_DEBUG__ = {
    getView: (key: string) => get().viewStates[key]?.view,
    getAllViews: () => Object.values(get().viewStates).map(s => s.view),
    getViewSettings: (key: string) => getViewSettings?.(key),
  };
}
```

### 2. 错误处理

```typescript
const safeViewOperation = (operation: () => void, context: string) => {
  try {
    operation();
  } catch (error) {
    console.error(`Error in ${context}:`, error);
    // 可选：上报错误到监控系统
    reportError(error, { context, bookKey });
  }
};
```

这种深度集成方式使得我们能够充分利用 Foliate.js 的强大功能，同时保持代码的可维护性和扩展性。通过细致的事件处理、状态同步和性能优化，我们实现了一个功能完整且高性能的电子书阅读器。 