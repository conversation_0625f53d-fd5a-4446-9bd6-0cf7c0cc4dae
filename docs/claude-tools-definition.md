# Claude工具定义文档

## 概述

本文档记录了Claude在Cursor环境中可用的所有工具定义，包括参数、使用规则和最佳实践。

## 工具列表

### 1. codebase_search - 语义搜索工具

**描述**: 语义搜索，通过意图而非精确文本查找代码

**参数**:
- `explanation` (required): 使用工具的原因和目标的一句话解释
- `query` (required): 完整的问题描述，如"How does X work?"、"Where is Y handled?"
- `search_only_prs` (optional): 如果为true，只搜索pull requests
- `target_directories` (required): 限制搜索范围的目录路径数组（单个目录，不支持glob模式）

**使用指南**:
- 适用于探索未知代码库、理解行为、按意图查找代码
- 不适用于精确文本匹配、读取已知文件、简单符号查找
- 目标目录：提供单个目录路径，[]表示搜索整个仓库
- 搜索策略：先从探索性查询开始，然后根据结果缩小范围

### 2. read_file - 文件读取工具

**描述**: 从本地文件系统读取文件内容

**参数**:
- `target_file` (required): 要读取的文件路径
- `should_read_entire_file` (required): 是否读取整个文件
- `start_line_one_indexed` (optional): 起始行号（1-based）
- `end_line_one_indexed_inclusive` (optional): 结束行号（包含）
- `explanation` (required): 使用工具的原因说明

**限制**:
- 最多一次读取1500行
- 建议至少读取150行以获得足够上下文
- 可以多次调用以读取大文件的不同部分

**使用技巧**:
- 并行调用多个read_file来同时读取多个文件
- 读取失败后应重新读取文件（文件可能已被修改）
- 避免设置should_read_entire_file为true如果已读取过文件的一部分

### 3. run_terminal_cmd - 终端命令执行工具

**描述**: 在用户系统上执行终端命令

**参数**:
- `command` (required): 要执行的终端命令
- `is_background` (required): 是否在后台运行
- `explanation` (optional): 执行命令的原因说明

**使用规则**:
- 根据对话历史判断是否在同一shell或新shell中
- 新shell时需要cd到合适目录并进行必要设置
- 同一shell时注意当前工作目录和环境变量
- 非交互式命令使用--yes等标志
- 长时间运行的命令使用is_background=true
- 使用pager的命令后缀 | cat

### 4. list_dir - 目录列表工具

**描述**: 列出目录内容

**参数**:
- `relative_workspace_path` (required): 相对于工作区根目录的路径
- `explanation` (required): 使用工具的原因说明

### 5. grep_search - 精确文本搜索工具

**描述**: 使用ripgrep引擎进行快速精确的正则表达式搜索

**参数**:
- `query` (required): 要搜索的正则表达式模式
- `case_sensitive` (optional): 是否区分大小写
- `include_pattern` (optional): 包含文件的glob模式（如'*.ts'）
- `exclude_pattern` (optional): 排除文件的glob模式
- `explanation` (required): 使用工具的原因说明

**使用指南**:
- 适用于已知确切符号/函数名的搜索
- 结果限制在50个匹配项内
- 必须转义正则表达式特殊字符：( ) [ ] { } + * ? ^ $ | . \
- 优先于语义搜索当知道确切搜索内容时

### 6. edit_file - 文件编辑工具

**描述**: 编辑现有文件或创建新文件

**参数**:
- `target_file` (required): 要编辑的文件路径（必须是第一个参数）
- `instructions` (required): 编辑操作的一句话描述
- `code_edit` (required): 要编辑的精确代码行，使用`// ... existing code ...`表示未改变的代码

**使用规则**:
- 推荐用于小于2500行的文件
- 必须使用`// ... existing code ...`注释表示未改变的代码
- 不要省略现有代码而不使用注释
- 提供足够上下文以消除歧义
- 单次调用处理文件的所有编辑

### 7. search_replace - 搜索替换工具

**描述**: 在文件中进行精确的搜索和替换操作

**参数**:
- `file_path` (required): 要搜索替换的文件路径
- `old_string` (required): 要替换的文本（必须在文件中唯一）
- `new_string` (required): 替换后的文本
- `explanation` (required): 操作原因说明

**关键要求**:
- old_string必须唯一标识要更改的特定实例
- 包含至少3-5行前后上下文
- 包含所有空白、缩进和周围代码
- 一次只能更改一个实例
- 推荐用于大于2500行的文件

### 8. file_search - 文件名搜索工具

**描述**: 基于模糊匹配搜索文件路径

**参数**:
- `query` (required): 要搜索的模糊文件名
- `explanation` (required): 使用工具的原因说明

**使用指南**:
- 适用于知道部分文件路径但不知道确切位置的情况
- 结果限制在10个匹配项内
- 查询越具体，结果越精确

### 9. delete_file - 文件删除工具

**描述**: 删除指定路径的文件

**参数**:
- `target_file` (required): 要删除的文件路径（相对于工作区根目录）
- `explanation` (required): 删除文件的原因说明

**安全特性**:
- 文件不存在时会优雅失败
- 安全原因拒绝操作时会优雅失败
- 无法删除时会优雅失败

### 10. reapply - 智能重新应用工具

**描述**: 调用更智能的模型重新应用最后一次编辑

**参数**:
- `target_file` (required): 要重新应用编辑的文件路径

**使用场景**:
- 仅在edit_file工具调用结果不符合预期时使用
- 表明应用编辑的模型不够智能，需要更智能的模型

### 11. web_search - 网络搜索工具

**描述**: 搜索网络获取实时信息

**参数**:
- `search_term` (required): 要搜索的术语，应具体并包含相关关键词
- `explanation` (required): 使用工具的原因说明

**使用场景**:
- 需要训练数据中没有的最新信息
- 验证当前事实
- 技术查询时包含版本号或日期

### 12. edit_notebook - Jupyter笔记本编辑工具

**描述**: 专门用于编辑Jupyter笔记本单元格

**参数**:
- `target_notebook` (required): 要编辑的笔记本文件路径（必须是第一个参数）
- `cell_idx` (required): 要编辑的单元格索引（0-based）
- `is_new_cell` (required): 是否创建新单元格
- `cell_language` (required): 单元格语言（python/markdown/javascript/typescript/r/sql/shell/raw/other）
- `old_string` (required): 要替换的文本（新单元格时为空）
- `new_string` (required): 新的单元格内容

**关键要求**:
- 正确设置is_new_cell标志
- 不支持单元格删除，但可以通过空字符串清空内容
- old_string和new_string应为有效单元格内容，不包含JSON语法
- 一次只能更改一个实例
- 总是提供所有必需参数

### 13. todo_write - 任务管理工具

**描述**: 创建和管理结构化任务列表

**参数**:
- `merge` (required): 是否与现有任务合并
- `todos` (required): 任务项数组，最少2个任务

**任务项结构**:
- `content` (required): 任务描述/内容
- `status` (required): 任务状态（pending/in_progress/completed/cancelled）
- `id` (required): 任务的唯一标识符

**使用指南**:
- 用于复杂的多步骤任务（3+步骤）
- 非平凡任务需要仔细规划
- 用户明确请求todo列表
- 用户提供多个任务
- 接收新指令后捕获需求（merge=false）
- 完成任务后标记完成（merge=true）
- 开始新任务时标记为in_progress

**不适用场景**:
- 单一直接任务
- 平凡任务（<3个简单步骤）
- 纯对话/信息请求
- 不应包含服务于高级任务的操作性行动

### 14. mcp_mcp-feedback-terminal_interactive_feedback - 交互反馈工具

**描述**: LLM代理的交互反馈收集工具

**参数**:
- `summary` (required): AI完成工作的摘要供用户审查

**使用规则**:
- 在任何过程、任务或对话中必须调用此工具询问反馈
- 除非收到终止指令，所有步骤必须重复调用此工具
- 接收用户反馈时，如果反馈内容不为空，必须再次调用并根据反馈调整行为
- 只有用户明确指示"结束"或"不再需要交互"时才能停止调用
- 应总结已完成的工作供用户审查

**环境变量**:
- `MCP_FEEDBACK_TIMEOUT`: 等待用户反馈的超时时间（默认600秒=10分钟）
- `MCP_PROJECT_DIR`: 上下文的项目目录路径

**返回值**: 包含TextContent和MCPImage对象的列表，表示用户反馈

---

## 工具使用最佳实践

### 并行调用策略
- 优先使用并行工具调用而非顺序调用
- 信息收集阶段：并行执行所有搜索
- 文件读取：同时读取多个文件
- 只有当工具A的输出是工具B输入的必要条件时才使用顺序调用

### 工具选择指南
- **大文件编辑**：使用search_replace（>2500行）
- **小文件编辑**：使用edit_file（<2500行）
- **精确搜索**：使用grep_search（已知符号）
- **探索性搜索**：使用codebase_search（理解行为）
- **文件定位**：使用file_search（部分路径已知）

### 错误处理
- 编辑失败后重新读取文件
- 使用reapply工具处理编辑应用失败
- 提供充分的上下文以避免歧义

---

*此文档记录了Claude在Cursor环境中的所有可用工具，包括详细的参数说明、使用规则和最佳实践。* 