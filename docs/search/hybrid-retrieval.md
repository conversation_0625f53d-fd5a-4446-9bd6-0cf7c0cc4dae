# 混合检索核心算法

## 🎯 混合检索概述

混合检索是现代信息检索系统的核心技术，它结合了传统的关键词匹配（BM25）和现代的语义理解（向量搜索），为用户提供更准确、更智能的搜索体验。

## 🧠 核心理念

### 互补性原理
- **BM25优势**: 精确的关键词匹配，快速响应，适合专业术语
- **向量搜索优势**: 语义理解，同义词匹配，概念关联
- **混合优势**: 取长补短，覆盖更广泛的搜索需求

### 智能平衡
```
用户查询 → 查询分析 → 权重调整 → 并行搜索 → 结果合并 → 智能排序
```

## ⚖️ 分数归一化算法

### Min-Max归一化

#### 数学公式
```
normalized_score = (original_score - min_score) / (max_score - min_score)
```

#### 实现代码
```rust
fn normalize_scores(&self, results: &[SearchResult]) -> Vec<f32> {
    if results.is_empty() {
        return Vec::new();
    }

    let scores: Vec<f32> = results.iter().map(|r| r.similarity_score).collect();
    let min_score = scores.iter().fold(f32::INFINITY, |a, &b| a.min(b));
    let max_score = scores.iter().fold(f32::NEG_INFINITY, |a, &b| a.max(b));

    if (max_score - min_score).abs() < f32::EPSILON {
        // 所有分数相同，返回均匀分数
        return vec![1.0; results.len()];
    }

    // Min-Max归一化
    scores
        .into_iter()
        .map(|score| (score - min_score) / (max_score - min_score))
        .collect()
}
```

#### 归一化示例

**向量搜索结果**:
```
原始分数: [0.95, 0.87, 0.82, 0.76, 0.71]
min = 0.71, max = 0.95
归一化后: [1.0, 0.67, 0.46, 0.21, 0.0]
```

**BM25搜索结果**:
```
原始分数: [15.2, 12.8, 9.4, 6.1, 3.7]
min = 3.7, max = 15.2
归一化后: [1.0, 0.79, 0.50, 0.21, 0.0]
```

### 归一化的必要性

#### 1. 分数范围差异
- **向量相似度**: 通常在[0, 1]或[-1, 1]范围
- **BM25分数**: 理论上无上界，实际可能达到几十

#### 2. 公平性保证
```rust
// 未归一化的问题示例
vector_score = 0.9    // 高相关性
bm25_score = 15.0     // 高相关性

// 直接加权会被BM25主导
combined = 0.7 * 0.9 + 0.3 * 15.0 = 5.13  // BM25权重实际很大

// 归一化后的公平合并
vector_normalized = 1.0
bm25_normalized = 1.0
combined = 0.7 * 1.0 + 0.3 * 1.0 = 1.0    // 权重按预期工作
```

## 🔄 加权合并算法

### 核心公式
```
combined_score = vector_weight × normalized_vector_score + bm25_weight × normalized_bm25_score
```

### 实现逻辑
```rust
fn calculate_combined_score(
    &self,
    vector_score: Option<f32>,
    bm25_score: Option<f32>,
    config: &HybridSearchConfig,
) -> f32 {
    match (vector_score, bm25_score) {
        (Some(v_score), Some(b_score)) => {
            // 两种搜索都有结果，使用加权平均
            config.vector_weight * v_score + config.bm25_weight * b_score
        }
        (Some(v_score), None) => {
            // 只有向量搜索有结果
            config.vector_weight * v_score
        }
        (None, Some(b_score)) => {
            // 只有BM25搜索有结果
            config.bm25_weight * b_score
        }
        (None, None) => 0.0,
    }
}
```

### 不同场景的处理

#### 1. 完整匹配场景
```rust
// 文档在两种搜索中都有结果
vector_score = Some(0.8)    // 归一化后
bm25_score = Some(0.6)      // 归一化后
weights = (0.7, 0.3)

combined = 0.7 * 0.8 + 0.3 * 0.6 = 0.74
```

#### 2. 部分匹配场景
```rust
// 文档只在向量搜索中有结果
vector_score = Some(0.9)
bm25_score = None
weights = (0.7, 0.3)

combined = 0.7 * 0.9 = 0.63  // 按向量权重缩放
```

#### 3. 权重调整的影响
```rust
// 同一文档，不同权重配置的结果
vector_score = 0.8, bm25_score = 0.6

// 偏重语义理解
weights = (0.8, 0.2): combined = 0.8 * 0.8 + 0.2 * 0.6 = 0.76

// 平衡配置
weights = (0.5, 0.5): combined = 0.5 * 0.8 + 0.5 * 0.6 = 0.70

// 偏重关键词匹配
weights = (0.2, 0.8): combined = 0.2 * 0.8 + 0.8 * 0.6 = 0.64
```

## 🚀 并行搜索优化

### 并行执行架构
```rust
pub fn search(
    &self,
    query: &str,
    query_embedding: &[f32],
    limit: usize,
    config: &HybridSearchConfig,
) -> Result<Vec<SearchResult>> {
    match config.mode {
        SearchMode::Hybrid => {
            // 并行执行两种搜索
            let vector_search = VectorDatabase::vector_search(query_embedding, limit);
            let bm25_search = BM25Search::search(query, limit);
            
            // 等待两个搜索完成
            let (vector_results, bm25_results) = join!(vector_search, bm25_search);
            
            // 合并结果
            self.merge_results(vector_results?, bm25_results?, config)
        }
        // ... 其他模式
    }
}
```

### 性能优化策略

#### 1. 智能降级
```rust
// 向量化失败时自动降级为BM25搜索
if query_embedding.is_empty() {
    log::warn!("向量化失败，降级为BM25搜索");
    return self.bm25_only_search(query, limit);
}
```

#### 2. 缓存机制
```rust
// BM25统计信息缓存
if let Some(cached_stats) = self.get_cached_bm25_stats() {
    return cached_stats;
}
```

#### 3. 批量处理
```rust
// 批量向量查询优化
let embeddings = batch_vectorize(queries)?;
let results = batch_vector_search(embeddings, limit)?;
```

## 🎯 智能权重调整

### 查询分析算法
```rust
pub fn adjust_weights_for_query(&self, query: &str) -> (f32, f32) {
    let word_count = query.split_whitespace().count();
    let query_len = query.len();
    
    // 基于查询特征的智能调整
    if word_count <= 2 || query_len < 10 {
        // 短查询：关键词匹配更重要
        (0.4, 0.6)  // (vector_weight, bm25_weight)
    } else if word_count > 10 || query_len > 100 {
        // 长查询：语义理解更重要
        (0.8, 0.2)
    } else {
        // 中等查询：平衡配置
        (0.7, 0.3)
    }
}
```

### 权重调整的逻辑

#### 短查询策略
- **特征**: 1-2个关键词，查询长度<10字符
- **用户意图**: 寻找特定术语或概念
- **策略**: 偏重BM25，确保精确匹配
- **权重**: vector:0.4, bm25:0.6

#### 长查询策略
- **特征**: >10个词，查询长度>100字符
- **用户意图**: 复杂问题，需要理解上下文
- **策略**: 偏重向量搜索，理解语义
- **权重**: vector:0.8, bm25:0.2

#### 中等查询策略
- **特征**: 3-9个词，中等长度
- **用户意图**: 平衡的信息需求
- **策略**: 平衡两种搜索方式
- **权重**: vector:0.7, bm25:0.3

## 📊 结果去重和排序

### 去重算法
```rust
fn merge_results(
    &self,
    vector_results: Vec<SearchResult>,
    bm25_results: Vec<SearchResult>,
    config: &HybridSearchConfig,
) -> Result<Vec<HybridSearchResult>> {
    // 使用HashMap按chunk_id去重
    let mut all_results: HashMap<i64, SearchResult> = HashMap::new();
    let mut vector_map: HashMap<i64, f32> = HashMap::new();
    let mut bm25_map: HashMap<i64, f32> = HashMap::new();
    
    // 收集所有唯一结果
    for (result, score) in vector_results.into_iter().zip(normalized_vector) {
        vector_map.insert(result.chunk_id, score);
        all_results.insert(result.chunk_id, result);
    }
    
    for (result, score) in bm25_results.into_iter().zip(normalized_bm25) {
        bm25_map.insert(result.chunk_id, score);
        all_results.insert(result.chunk_id, result);
    }
    
    // 计算合并分数并排序
    let mut hybrid_results: Vec<HybridSearchResult> = all_results
        .into_iter()
        .map(|(chunk_id, result)| {
            let combined_score = self.calculate_combined_score(
                vector_map.get(&chunk_id).copied(),
                bm25_map.get(&chunk_id).copied(),
                config,
            );
            
            HybridSearchResult {
                search_result: result,
                combined_score,
                vector_score: vector_map.get(&chunk_id).copied(),
                bm25_score: bm25_map.get(&chunk_id).copied(),
            }
        })
        .collect();
    
    // 按合并分数降序排序
    hybrid_results.sort_by(|a, b| {
        b.combined_score.partial_cmp(&a.combined_score).unwrap_or(Ordering::Equal)
    });
    
    Ok(hybrid_results)
}
```

### 排序策略
1. **主要排序**: 按合并分数降序
2. **次要排序**: 分数相同时按chunk_id升序
3. **稳定排序**: 保证结果的一致性

## 🎯 实际应用效果

### 查询示例分析

#### 示例1: 专业术语查询
```
查询: "反向传播算法"
智能权重: (0.4, 0.6) - 偏重关键词

结果分析:
- BM25找到精确匹配的技术文档
- 向量搜索找到相关的概念解释
- 合并后优先显示精确匹配的内容
```

#### 示例2: 概念性查询
```
查询: "人工智能在医疗领域的应用前景和挑战"
智能权重: (0.8, 0.2) - 偏重语义

结果分析:
- 向量搜索理解复杂的概念关系
- BM25补充关键词匹配
- 合并后提供全面的概念性内容
```

#### 示例3: 平衡查询
```
查询: "机器学习模型训练技巧"
智能权重: (0.7, 0.3) - 平衡配置

结果分析:
- 两种搜索方式平衡工作
- 既有精确的技术内容，也有相关的概念解释
- 提供最全面的搜索结果
```

这种混合检索算法确保了系统能够智能地理解用户意图，提供最相关和最有用的搜索结果。
