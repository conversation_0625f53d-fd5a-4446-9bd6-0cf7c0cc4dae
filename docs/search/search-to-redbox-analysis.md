# 搜索结果到红色框显示的完整分析

## 概述

本文档深入分析了在 `use-annotation-search.ts` 中使用 `view.search` 之后，通过 `view.goTo(firstCfi)` 跳转并显示红色框的完整实现逻辑。

## 🔍 完整的搜索到红色框显示流程

### 1. 搜索触发 (use-annotation-search.ts)

```typescript
// 第149行：跳转到搜索结果
view.goTo(firstCfi);
```

### 2. View.goTo 执行跳转 (view.js:471-482)

```javascript
async goTo(target) {
  target = decodeURIComponent(target);              // URL解码
  const resolved = this.resolveNavigation(target); // 解析CFI
  await this.renderer.goTo(resolved);              // 执行跳转
  this.history.pushState(target);                  // 添加历史
  return resolved;
}
```

**关键步骤：**
- `resolveNavigation`: 将CFI字符串解析为 `{ index, anchor }` 对象
- `renderer.goTo`: 调用渲染器执行实际跳转

### 3. Paginator 处理跳转 (paginator.js:1023-1027, 1003-1022)

```javascript
async goTo(target) {
  if (this.#locked) return;
  const resolved = await target;
  if (this.#canGoToIndex(resolved.index)) return this.#goTo(resolved);
}

async #goTo({ index, anchor, select }) {
  if (index === this.#index) {
    // 同一章节，直接显示
    await this.#display({ index, anchor, select });
  } else {
    // 不同章节，加载新内容
    const oldIndex = this.#index;
    await this.#display(
      Promise.resolve(this.sections[index].load())
        .then((src) => ({ index, src, anchor, onLoad, select }))
    );
  }
}
```

### 4. #display 加载章节并触发覆盖层创建 (paginator.js:968-999)

```javascript
async #display(promise) {
  const { index, src, anchor, onLoad, select } = await promise;
  this.#index = index;
  
  if (src) {
    // 创建新视图
    const view = this.#createView();
    
    // 加载章节内容
    await view.load(src, afterLoad, beforeRender);
    
    // 🔑 关键：触发 create-overlayer 事件
    this.dispatchEvent(new CustomEvent("create-overlayer", {
      detail: {
        doc: view.document,
        index,
        attach: (overlayer) => (view.overlayer = overlayer),
      },
    }));
    
    this.#view = view;
  }
  
  // 滚动到目标位置
  await this.scrollToAnchor(anchor, select);
}
```

**关键点：**
- 每次加载新章节都会触发 `create-overlayer` 事件
- 这是红色框重新显示的触发时机

### 5. View 监听事件并创建覆盖层 (view.js:280, 415-433)

```javascript
// 构造函数中监听事件
this.renderer.addEventListener("create-overlayer", (e) => 
  e.detail.attach(this.#createOverlayer(e.detail)));

#createOverlayer({ doc, index }) {
  const overlayer = new Overlayer();
  
  // 添加点击事件监听
  doc.addEventListener("click", (e) => {
    const [value, range] = overlayer.hitTest(e);
    if (value && !value.startsWith(SEARCH_PREFIX)) {
      this.#emit("show-annotation", { value, index, range });
    }
  });

  // 🔑 关键：重新显示该章节的所有搜索结果
  const list = this.#searchResults.get(index);
  if (list) for (const item of list) this.addAnnotation(item);

  this.#emit("create-overlay", { index });
  return overlayer;
}
```

**搜索结果存储结构：**
- `#searchResults`: `Map<章节索引, 搜索结果数组>`
- 每个搜索结果格式：`{ value: "foliate-search:" + CFI }`

### 6. addAnnotation 创建红色框 (view.js:378-393)

```javascript
async addAnnotation(annotation, remove) {
  const { value } = annotation;
  
  // 处理搜索结果注解
  if (value.startsWith(SEARCH_PREFIX)) {  // "foliate-search:"
    const cfi = value.replace(SEARCH_PREFIX, "");
    const { index, anchor } = await this.resolveNavigation(cfi);
    const obj = this.#getOverlayer(index);
    
    if (obj) {
      const { overlayer, doc } = obj;
      if (remove) {
        overlayer.remove(value);
        return;
      }
      
      // 获取DOM Range
      const range = doc ? anchor(doc) : anchor;
      
      // 🔑 关键：添加红色边框
      overlayer.add(value, range, Overlayer.outline);
    }
    return;
  }
  
  // 处理普通注解...
}
```

**关键步骤：**
1. 检查是否为搜索结果注解（以 "foliate-search:" 开头）
2. 解析CFI获取定位信息
3. 获取对应章节的覆盖层对象
4. 将CFI转换为DOM Range
5. 使用 `Overlayer.outline` 绘制红色边框

### 7. Overlayer.outline 绘制红色框 (overlayer.js:142-158)

```javascript
static outline(rects, options = {}) {
  const { color = 'red', width: strokeWidth = 3, radius = 3 } = options;
  const g = createSVGElement('g');
  g.setAttribute('fill', 'none');
  g.setAttribute('stroke', color);
  g.setAttribute('stroke-width', strokeWidth);
  
  // 为每个文本矩形创建SVG边框
  for (const { left, top, height, width } of rects) {
    const el = createSVGElement('rect');
    el.setAttribute('x', left);
    el.setAttribute('y', top);
    el.setAttribute('height', height);
    el.setAttribute('width', width);
    el.setAttribute('rx', radius);  // 圆角
    g.append(el);
  }
  return g;
}
```

**SVG绘制参数：**
- 默认颜色：红色 (`color = 'red'`)
- 边框宽度：3px (`strokeWidth = 3`)
- 圆角半径：3px (`radius = 3`)
- 填充：无 (`fill = 'none'`)

### 8. overlayer.add 方法 (overlayer.js:17-24)

```javascript
add(key, range, draw, options) {
  if (this.#map.has(key)) this.remove(key);  // 移除已存在的
  if (typeof range === 'function') range = range(this.#svg.getRootNode());
  
  const rects = range.getClientRects();      // 获取文本矩形
  const element = draw(rects, options);      // 调用绘制函数
  this.#svg.append(element);                 // 添加到SVG
  
  // 存储到内部映射
  this.#map.set(key, { range, draw, options, element, rects });
}
```

## 🎯 关键技术机制

### 1. CFI (Canonical Fragment Identifier) 定位系统
- CFI是EPUB标准的精确定位格式
- 可以精确定位到文档中的任意位置
- 通过 `resolveNavigation` 和 `resolveCFI` 解析

### 2. SVG覆盖层技术
```javascript
// overlayer.js:7-13
constructor() {
  Object.assign(this.#svg.style, {
    position: 'absolute', 
    top: '0', 
    left: '0',
    width: '100%', 
    height: '100%',
    pointerEvents: 'none',  // 不阻挡下层交互
  });
}
```

### 3. 自动重显示机制
- 每次页面跳转都触发 `create-overlayer` 事件
- 自动重新显示该章节的所有搜索结果
- 无需手动触发，完全自动化

### 4. 章节级别的存储管理
```javascript
// 搜索时存储 (view.js:565-567)
const list = result.subitems.map(({ cfi }) => ({ value: SEARCH_PREFIX + cfi }));
this.#searchResults.set(result.index, list);

// 清除时删除 (view.js:583-585)
for (const list of this.#searchResults.values()) 
  for (const item of list) this.deleteAnnotation(item);
this.#searchResults.clear();
```

## 🔧 自定义红色框样式

### 1. 通过 options 参数自定义
```javascript
overlayer.add(value, range, Overlayer.outline, {
  color: 'blue',      // 自定义颜色
  width: 5,           // 自定义边框宽度
  radius: 8           // 自定义圆角
});
```

### 2. CSS变量支持（仅 highlight 方法）
```css
:root {
  --overlayer-highlight-opacity: 0.5;
  --overlayer-highlight-blend-mode: multiply;
}
```

## 🎨 系统架构特点

1. **事件驱动**: 通过 `create-overlayer` 事件触发覆盖层创建
2. **分层设计**: 内容层 + SVG覆盖层，互不干扰
3. **自动管理**: 搜索结果自动存储、显示、清理
4. **精确定位**: 基于CFI的精确文档定位
5. **响应式**: 窗口调整时自动重绘 (`overlayer.redraw()`)

## 📝 总结

这个系统通过精心设计的事件链和数据流，实现了从搜索到可视化显示的完整闭环：

1. **搜索阶段**: 生成CFI并存储搜索结果
2. **跳转阶段**: 解析CFI并执行页面跳转
3. **显示阶段**: 触发覆盖层创建事件
4. **渲染阶段**: 自动重新显示所有搜索结果的红色框

整个过程完全自动化，用户只需调用 `view.goTo(cfi)`，红色框就会自动显示在正确的位置上。 