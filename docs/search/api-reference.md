# API参考文档

## 📡 前端API

### ragSearchTool

智能混合检索工具，支持向量搜索、BM25搜索和混合搜索三种模式。

#### 接口定义

```typescript
export const ragSearchTool = tool({
  description: "在当前图书中执行智能混合检索...",
  inputSchema: z.object({
    reasoning: z.string().min(1),
    question: z.string().min(1),
    limit: z.number().int().min(1).max(20).default(3),
    format: z.boolean().default(true),
    searchMode: z.enum(["vector", "bm25", "hybrid"]).default("hybrid"),
    vectorWeight: z.number().min(0).max(1).default(0.7),
    bm25Weight: z.number().min(0).max(1).default(0.3),
  }),
  execute: async (params) => { /* ... */ }
});
```

#### 参数说明

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `reasoning` | string | - | 调用此工具的原因和目的 |
| `question` | string | - | 用户的查询问题 |
| `limit` | number | 3 | 返回的内容片段数量 (1-20) |
| `format` | boolean | true | 是否返回格式化的上下文文本 |
| `searchMode` | enum | "hybrid" | 搜索模式：vector/bm25/hybrid |
| `vectorWeight` | number | 0.7 | 向量搜索权重 (0-1) |
| `bm25Weight` | number | 0.3 | BM25搜索权重 (0-1) |

#### 搜索模式详解

##### Vector模式
```typescript
{
  searchMode: "vector",
  // vectorWeight和bm25Weight在此模式下被忽略
}
```
- **适用场景**: 概念性查询、同义词匹配、语义理解
- **优势**: 理解查询意图，找到语义相关内容
- **劣势**: 可能错过精确的关键词匹配

##### BM25模式
```typescript
{
  searchMode: "bm25",
  // vectorWeight和bm25Weight在此模式下被忽略
}
```
- **适用场景**: 专业术语、精确词汇匹配、关键词搜索
- **优势**: 精确匹配，快速响应
- **劣势**: 无法理解同义词和语义关系

##### Hybrid模式（推荐）
```typescript
{
  searchMode: "hybrid",
  vectorWeight: 0.7,    // 可调整
  bm25Weight: 0.3       // 可调整
}
```
- **适用场景**: 大多数搜索需求
- **优势**: 结合两种搜索的优点
- **智能特性**: 自动权重调整

#### 返回值格式

```typescript
interface SearchResponse {
  success: boolean;
  results: SearchResult[];
  searchStats?: {
    totalResults: number;
    searchTime: number;
    searchMode: string;
    vectorWeight?: number;
    bm25Weight?: number;
  };
  formattedContext?: string;
}

interface SearchResult {
  chunk_id: number;
  book_id: string;
  chunk_text: string;
  similarity_score: number;
  file_name?: string;
  chapter_title?: string;
  page_number?: number;
  chunk_order_in_file?: number;
}
```

#### 使用示例

##### 基础使用
```typescript
const results = await ragSearchTool.execute({
  reasoning: "用户询问关于机器学习的问题",
  question: "什么是深度学习？",
  limit: 5
});
```

##### 专业术语搜索
```typescript
const results = await ragSearchTool.execute({
  reasoning: "查找特定技术术语",
  question: "反向传播算法",
  searchMode: "bm25",
  limit: 3
});
```

##### 概念理解搜索
```typescript
const results = await ragSearchTool.execute({
  reasoning: "理解复杂概念",
  question: "人工智能的发展历程和未来趋势",
  searchMode: "vector",
  limit: 8
});
```

##### 自定义权重混合搜索
```typescript
const results = await ragSearchTool.execute({
  reasoning: "需要平衡关键词和语义匹配",
  question: "神经网络训练过程中的梯度消失问题",
  searchMode: "hybrid",
  vectorWeight: 0.5,
  bm25Weight: 0.5,
  limit: 6
});
```

## 🔧 后端API

### Tauri Commands

#### search_db

执行数据库搜索的核心命令。

```rust
#[tauri::command]
pub async fn search_db(
    query: String,
    limit: Option<usize>,
    format_context: Option<bool>,
    search_mode: Option<String>,
    vector_weight: Option<f32>,
    bm25_weight: Option<f32>,
    state: tauri::State<'_, PluginState>,
) -> Result<SearchResponse, String>
```

#### 参数说明

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `query` | String | - | 搜索查询字符串 |
| `limit` | Option<usize> | 3 | 返回结果数量限制 |
| `format_context` | Option<bool> | true | 是否格式化上下文 |
| `search_mode` | Option<String> | "hybrid" | 搜索模式 |
| `vector_weight` | Option<f32> | 0.7 | 向量搜索权重 |
| `bm25_weight` | Option<f32> | 0.3 | BM25搜索权重 |

### 内部API

#### VectorDatabase

数据库操作的核心接口。

```rust
impl VectorDatabase {
    /// 统一搜索接口
    pub fn search_with_mode(
        &self,
        query: &str,
        query_embedding: Option<&[f32]>,
        limit: usize,
        config: &HybridSearchConfig,
    ) -> Result<Vec<SearchResult>>;

    /// 向量搜索
    pub fn vector_search(
        &self,
        query_embedding: &[f32],
        limit: usize,
    ) -> Result<Vec<SearchResult>>;

    /// BM25搜索
    pub fn bm25_search(
        &self,
        query: &str,
        limit: usize,
    ) -> Result<Vec<SearchResult>>;

    /// 混合搜索
    pub fn hybrid_search(
        &self,
        query: &str,
        query_embedding: &[f32],
        limit: usize,
        config: &HybridSearchConfig,
    ) -> Result<Vec<SearchResult>>;
}
```

#### HybridSearch

混合搜索引擎的核心实现。

```rust
impl HybridSearch {
    /// 创建新的混合搜索实例
    pub fn new(db: &Connection) -> Self;

    /// 执行混合搜索
    pub fn search(
        &self,
        query: &str,
        query_embedding: &[f32],
        limit: usize,
        config: &HybridSearchConfig,
    ) -> Result<Vec<SearchResult>>;
}
```

#### BM25Search

BM25搜索引擎实现。

```rust
impl BM25Search {
    /// 创建BM25搜索实例
    pub fn new(db: &Connection, k1: f32, b: f32) -> Self;

    /// 执行BM25搜索
    pub fn search(
        &self,
        query: &str,
        limit: usize,
    ) -> Result<Vec<BM25SearchResult>>;
}
```

## 📊 配置API

### HybridSearchConfig

混合搜索配置结构。

```rust
#[derive(Debug, Clone)]
pub struct HybridSearchConfig {
    pub mode: SearchMode,
    pub vector_weight: f32,
    pub bm25_weight: f32,
    pub k1: f32,              // BM25参数
    pub b: f32,               // BM25参数
}

impl Default for HybridSearchConfig {
    fn default() -> Self {
        Self {
            mode: SearchMode::Hybrid,
            vector_weight: 0.7,
            bm25_weight: 0.3,
            k1: 1.2,
            b: 0.75,
        }
    }
}
```

### SearchMode

搜索模式枚举。

```rust
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum SearchMode {
    VectorOnly,
    BM25Only,
    Hybrid,
}
```

## 🔍 智能权重调整API

### SimpleSearchConfig

简化的搜索配置，支持智能权重调整。

```rust
impl SimpleSearchConfig {
    /// 根据查询特征调整权重
    pub fn adjust_weights_for_query(&self, query: &str) -> (f32, f32) {
        let word_count = query.split_whitespace().count();
        let query_len = query.len();

        if word_count <= 2 || query_len < 10 {
            // 短查询：偏重关键词匹配
            (0.4, 0.6)
        } else if word_count > 10 || query_len > 100 {
            // 长查询：偏重语义理解
            (0.8, 0.2)
        } else {
            // 中等查询：使用默认权重
            (self.default_vector_weight, self.default_bm25_weight)
        }
    }
}
```

## ⚠️ 错误处理

### 常见错误类型

```rust
pub enum SearchError {
    DatabaseError(String),
    VectorizationError(String),
    ConfigurationError(String),
    InvalidQuery(String),
}
```

### 错误响应格式

```typescript
interface ErrorResponse {
  success: false;
  error: string;
  error_type?: string;
  suggestions?: string[];
}
```

## 📈 性能指标

### 响应时间
- **Vector搜索**: 50-100ms
- **BM25搜索**: 10-30ms  
- **Hybrid搜索**: 60-120ms

### 并发支持
- 支持多个并发搜索请求
- 数据库连接池管理
- 内存使用优化

这个API设计确保了系统的易用性、灵活性和高性能，为开发者提供了完整的搜索功能接口。
