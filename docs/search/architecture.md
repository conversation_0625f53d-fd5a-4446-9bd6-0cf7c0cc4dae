# 混合检索系统架构设计

## 🏗️ 系统架构概览

混合检索系统采用分层架构设计，从前端用户界面到后端搜索引擎，实现了完整的智能检索链路。

```
┌─────────────────────────────────────────────────────────────┐
│                    前端层 (Frontend)                        │
├─────────────────────────────────────────────────────────────┤
│  ragSearchTool (TypeScript)                                │
│  ├─ 参数验证 (Zod Schema)                                   │
│  ├─ 搜索模式选择                                            │
│  └─ 结果格式化                                              │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   接口层 (Interface)                        │
├─────────────────────────────────────────────────────────────┤
│  Tauri Commands                                            │
│  ├─ search_db (混合搜索命令)                                │
│  ├─ 参数解析和验证                                          │
│  └─ 错误处理和响应                                          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   业务层 (Business)                         │
├─────────────────────────────────────────────────────────────┤
│  Pipeline Layer                                            │
│  ├─ search_db_with_mode()                                  │
│  ├─ 配置管理 (Config Management)                            │
│  ├─ 智能权重调整                                            │
│  └─ 向量化处理                                              │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   搜索层 (Search)                           │
├─────────────────────────────────────────────────────────────┤
│  VectorDatabase                                            │
│  ├─ search_with_mode() (统一搜索接口)                       │
│  ├─ vector_search() (向量搜索)                              │
│  ├─ bm25_search() (BM25搜索)                                │
│  └─ hybrid_search() (混合搜索)                              │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   算法层 (Algorithm)                        │
├─────────────────────────────────────────────────────────────┤
│  HybridSearch Engine                                       │
│  ├─ 并行搜索执行                                            │
│  ├─ 分数归一化 (Min-Max)                                    │
│  ├─ 加权合并算法                                            │
│  └─ 结果排序和去重                                          │
│                                                             │
│  BM25Search Engine          VectorSearch Engine           │
│  ├─ 文本预处理               ├─ sqlite-vec集成              │
│  ├─ TF-IDF计算              ├─ 余弦相似度计算               │
│  ├─ BM25评分算法            └─ 向量索引查询                 │
│  └─ 统计信息缓存                                            │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   存储层 (Storage)                          │
├─────────────────────────────────────────────────────────────┤
│  SQLite Database + sqlite-vec Extension                    │
│  ├─ chunks (文档分块表)                                     │
│  ├─ vec0 (向量索引表)                                       │
│  ├─ bm25_stats (BM25统计表)                                │
│  └─ 索引和约束                                              │
└─────────────────────────────────────────────────────────────┘
```

## 🔄 数据流设计

### 搜索请求流程
```
用户查询 → 参数验证 → 模式选择 → 配置生成 → 搜索执行 → 结果合并 → 格式化输出
```

### 混合搜索执行流程
```
1. 查询分析
   ├─ 查询长度检测
   ├─ 智能权重调整
   └─ 搜索模式确定

2. 并行搜索
   ├─ 向量搜索 (VectorSearch)
   │  ├─ 文本向量化
   │  ├─ 相似度计算
   │  └─ 结果排序
   │
   └─ BM25搜索 (BM25Search)
      ├─ 文本预处理
      ├─ TF-IDF计算
      └─ BM25评分

3. 结果合并
   ├─ 分数归一化 (Min-Max)
   ├─ 加权合并计算
   ├─ 去重处理
   └─ 最终排序
```

## 🧩 核心组件设计

### 1. 配置管理 (Configuration)
```rust
pub struct SimpleSearchConfig {
    pub default_vector_weight: f32,    // 0.7
    pub default_bm25_weight: f32,      // 0.3
    pub bm25_k1: f32,                  // 1.2
    pub bm25_b: f32,                   // 0.75
    pub enable_smart_weights: bool,    // true
}
```

**设计理念**:
- 简化配置，避免过度复杂化
- 智能默认值，开箱即用
- 灵活调整，支持高级用户定制

### 2. 搜索引擎 (Search Engines)

#### HybridSearch
- **职责**: 协调向量和BM25搜索，实现结果合并
- **核心算法**: 分数归一化 + 加权合并
- **优化**: 并行执行，智能降级

#### BM25Search
- **职责**: 实现经典的BM25文本检索算法
- **核心特性**: TF-IDF计算，文档长度归一化
- **优化**: 统计信息缓存，中文分词优化

#### VectorSearch
- **职责**: 基于embedding的语义相似度搜索
- **核心技术**: sqlite-vec扩展，余弦相似度
- **优化**: 向量索引，批量查询

### 3. 数据库设计 (Database Schema)

#### 核心表结构
```sql
-- 文档分块表
CREATE TABLE chunks (
    id INTEGER PRIMARY KEY,
    book_id TEXT NOT NULL,
    chunk_text TEXT NOT NULL,
    chunk_order_in_file INTEGER,
    -- ... 其他字段
);

-- 向量索引表 (sqlite-vec)
CREATE VIRTUAL TABLE vec0 USING vec0(
    embedding float[1024]  -- 动态维度支持
);

-- BM25统计表
CREATE TABLE bm25_stats (
    total_docs INTEGER NOT NULL,
    avg_doc_length REAL NOT NULL,
    updated_at TEXT NOT NULL
);
```

## 🎯 设计原则

### 1. 性能优先
- **并行执行**: 向量和BM25搜索并行进行
- **智能缓存**: BM25统计信息缓存
- **索引优化**: 向量索引和文本索引

### 2. 用户体验
- **智能默认**: 自动选择最佳搜索策略
- **灵活配置**: 支持高级用户自定义
- **向后兼容**: 保持原有API不变

### 3. 可扩展性
- **模块化设计**: 各组件职责清晰，易于扩展
- **接口统一**: 统一的搜索接口，支持新算法
- **配置驱动**: 通过配置控制行为，无需代码修改

### 4. 可靠性
- **错误处理**: 完善的错误处理和降级机制
- **数据一致性**: 事务保证数据完整性
- **类型安全**: Rust类型系统保证内存安全

## 🔧 技术选型

### 后端技术栈
- **Rust**: 高性能、内存安全的系统编程语言
- **SQLite**: 轻量级、嵌入式数据库
- **sqlite-vec**: 高性能向量搜索扩展
- **Tauri**: 现代化的跨平台应用框架

### 前端技术栈
- **TypeScript**: 类型安全的JavaScript超集
- **Zod**: 运行时类型验证和推断
- **React**: 现代化的用户界面框架

### 算法选择
- **BM25**: 经典且成熟的文本检索算法
- **向量相似度**: 现代语义搜索的标准方法
- **Min-Max归一化**: 简单有效的分数标准化方法

## 📈 扩展性考虑

### 水平扩展
- 支持多数据库分片
- 分布式向量索引
- 负载均衡和缓存

### 算法扩展
- 新的文本检索算法
- 多模态搜索支持
- 个性化排序算法

### 功能扩展
- 实时搜索建议
- 搜索结果聚类
- 用户行为分析

这种架构设计确保了系统的高性能、高可用性和良好的用户体验，同时为未来的功能扩展留下了充足的空间。
