# 提示词集成说明

## 📋 概述

本文档说明RAG工具系统如何与AI助手的提示词（prompt）集成，确保工具被正确和高效地使用。

## 🎯 核心设计原则

### 1. reasoning参数强制要求
- **目的**: 提高AI工具调用的透明度
- **实现**: 所有RAG工具都需要reasoning参数
- **效果**: 用户能清楚了解AI为什么调用特定工具

### 2. 分层检索策略
- **第一层**: ragSearch作为所有RAG查询的起点
- **第二层**: 根据需求选择扩展工具
- **效果**: 避免盲目调用工具，提高检索效率

## 📝 提示词中的工具指导

### 核心提示内容

```markdown
#### ⚠️ 重要：所有RAG工具都需要reasoning参数
**调用任何RAG工具时，都必须提供reasoning参数来说明调用原因！**
- **ragSearch需要**：`reasoning: "用户询问关于XX的问题，需要搜索相关内容"`
- **ragContext需要**：`reasoning: "需要获取更多上下文来理解用户问题"`
- **ragToc需要**：`reasoning: "用户想了解整个章节的内容"`
- **ragRange需要**：`reasoning: "需要获取特定索引范围的连续内容进行分析"`
```

### 标准使用流程

```markdown
#### 💡 简单的使用决策
**标准流程：**
1. 先调用 `ragSearch` 获取相关内容（记得提供reasoning）
2. 分析 top1 结果是否充分回答用户问题
3. 如果不够充分：
   - 需要理解上下文 → `ragContext`（记得提供reasoning）
   - 需要完整章节讨论 → `ragToc`（记得提供reasoning）
4. 基于获取的内容给出完整回答
```

## 🛠️ 工具调用示例

### ragSearch示例
```typescript
// ✅ 正确的调用方式
await ragSearch({
  reasoning: "用户询问关于思考代价的概念，需要搜索相关内容",
  question: "什么是思考的代价",
  limit: 5
});

// ❌ 错误的调用方式（缺少reasoning）
await ragSearch({
  question: "什么是思考的代价",
  limit: 5
});
```

### ragContext示例
```typescript
// ✅ 正确的调用方式
await ragContext({
  reasoning: "搜索结果中的概念定义不够完整，需要获取前后文来理解完整含义",
  chunk_id: 1234,
  prev_count: 2,
  next_count: 2
});
```

### ragToc示例
```typescript
// ✅ 正确的调用方式
await ragToc({
  reasoning: "用户询问第3章的内容，需要获取完整章节信息",
  toc_id: "num_3"
});
```

### ragRange示例
```typescript
// ✅ 正确的调用方式
await ragRange({
  reasoning: "需要分析全局索引45-60之间的连续内容",
  start_index: 45,
  end_index: 60,
  max_chunks: 15
});
```

## 📊 工具选择决策树

```
用户询问
    ↓
是否是书籍元信息？
    ├─ 是 → 直接使用metadata回答，不调用工具
    └─ 否 ↓
        
是否明确询问章节？
    ├─ 是 → ragToc
    └─ 否 ↓
        
调用ragSearch搜索相关内容
    ↓
分析top1结果
    ├─ 信息充足 → 直接回答
    ├─ 需要上下文 → ragContext
    ├─ 需要完整章节 → ragToc
    └─ 需要连续范围 → ragRange
```

## 🎨 UI集成说明

### reasoning显示设计
```typescript
// 工具组件中的reasoning显示
{input && input.reasoning && (
  <div className="mb-2">
    <span className="text-xs text-muted-foreground italic">
      💭 {String(input.reasoning)}
    </span>
  </div>
)}
```

### 显示特点
- **位置**: 显示在工具卡片外面，不在边框内
- **样式**: 💭图标 + 斜体灰色文字
- **目的**: 让用户清楚看到AI的工具调用意图

## ⚠️ 常见错误和避免方法

### 错误1：忘记提供reasoning
```typescript
// ❌ 错误
ragSearch({ question: "问题" })

// ✅ 正确
ragSearch({ 
  reasoning: "用户询问相关问题，需要搜索内容",
  question: "问题" 
})
```

### 错误2：工具调用过多
```typescript
// ❌ 错误：连续调用多个工具
await ragSearch({...});
await ragContext({...});
await ragToc({...});

// ✅ 正确：有目的地选择工具
const searchResult = await ragSearch({...});
if (needMoreContext) {
  await ragContext({...});
}
```

### 错误3：不基于实际内容回答
```typescript
// ❌ 错误：调用工具但不使用结果
const result = await ragSearch({...});
// 然后基于记忆回答而不是result内容

// ✅ 正确：基于工具返回的实际内容
const result = await ragSearch({...});
// 分析result.results[0].content来生成回答
```

## 📈 提示词优化建议

### 1. 强调reasoning的重要性
在提示词开头就明确说明reasoning参数的必要性，使用醒目的标记（⚠️）。

### 2. 提供具体示例
为每个工具提供清晰的reasoning示例，而不是抽象的说明。

### 3. 强调标准流程
明确"ragSearch优先"的策略，避免盲目调用其他工具。

### 4. 例外情况说明
明确说明什么时候不应该调用工具（如书籍元信息查询）。

## 🔍 监控和调试

### 工具调用监控
```typescript
// 在开发环境中可以添加监控
console.log('Tool called:', {
  tool: 'ragSearch',
  reasoning: reasoning,
  timestamp: new Date().toISOString()
});
```

### 调试提示
- 检查reasoning是否清晰描述了调用目的
- 验证工具参数是否合理
- 确认是否基于工具结果生成回答

## 📚 相关文档

- [RAG工具系统总览](./README.md)
- [ragSearch详细文档](./rag-search.md)
- [ragContext详细文档](./rag-context.md)
- [ragToc详细文档](./rag-toc.md)
- [ragRange详细文档](./rag-range.md)

---

> 💡 **关键提醒**: reasoning参数不仅是技术要求，更是提升用户体验的重要设计。它让AI的工具调用变得透明和可理解，这是RAG系统用户体验的关键组成部分。 