# ragContext - 上下文扩展检索工具

## 📋 基本信息

| 属性 | 值 |
|------|-----|
| 工具名称 | ragContext |
| 文件位置 | `/src/ai/tools/ragContext.ts` |
| 主要用途 | 基于分块ID获取该分块的前后文内容，用于扩展上下文信息 |
| 使用频率 | ⭐⭐⭐⭐ (高频，理解上下文的关键工具) |

## 🎯 功能描述

ragContext是上下文扩展的专用工具，当ragSearch返回的单个分块信息不够完整时，使用此工具获取该分块的前后文内容，帮助更好地理解语境和完整含义。

## 📝 参数定义

### 输入参数 (InputSchema)

```typescript
{
  reasoning: string,     // 必需 - 调用此工具的原因和目的
  chunk_id: number,      // 必需 - 目标分块的数据库ID
  prev_count: number,    // 可选 - 获取前面多少个分块，默认2个，范围0-10
  next_count: number     // 可选 - 获取后面多少个分块，默认2个，范围0-10
}
```

### 参数详解

#### reasoning (必需)
- **类型**: string
- **验证**: 最小长度1
- **说明**: 解释为什么需要获取上下文
- **示例**: 
  - `"需要获取更多上下文来理解用户问题"`
  - `"搜索结果不够完整，需要前后文来理解完整含义"`
  - `"需要了解概念的前因后果"`

#### chunk_id (必需)
- **类型**: number (整数)
- **验证**: 最小值1
- **说明**: 目标分块的数据库ID，通常从ragSearch的结果中获取
- **示例**: `1234` (来自ragSearch结果的position.chunk_id)

#### prev_count (可选)
- **类型**: number (整数)
- **默认值**: 2
- **范围**: 0-10
- **说明**: 获取目标分块前面多少个分块

#### next_count (可选)
- **类型**: number (整数)
- **默认值**: 2
- **范围**: 0-10
- **说明**: 获取目标分块后面多少个分块

## 📤 输出结构

### 返回数据结构

```typescript
{
  // 结构化的上下文数据
  context: Array<{
    chunk_id: number,          // 分块ID
    chapter_title: string,     // 章节标题
    content: string,           // 分块内容
    
    // 位置标识
    is_target: boolean,        // 是否为目标分块
    relative_position: number, // 相对位置 (-2, -1, 0, 1, 2)
    position_label: string,    // 位置标签 "目标分块", "前1个", "后1个"
    
    // 详细位置信息
    toc_info: {
      toc_id: string,              // TOC节点ID
      toc_depth: number,           // TOC层级深度
      position_in_toc: string,     // 在TOC中的位置 "3/10"
      global_index: number,        // 全局索引
      md_source: string           // Markdown源文件
    }
  }>,
  
  // 格式化文本
  formatted: string,
  
  // 元信息
  meta: {
    reasoning: string,          // 调用原因
    target_chunk_id: number,    // 目标分块ID
    total_chunks: number,       // 返回的总分块数
    prev_count: number,         // 实际获取的前文数量
    next_count: number,         // 实际获取的后文数量
    target_found: boolean       // 是否找到目标分块
  }
}
```

### 格式化输出示例

```text
[上下文检索] 分块ID 1234 的前后文内容：
💭 调用原因：需要获取更多上下文来理解用户问题

📄 前2个 | 第三章 思考的代价
   位置：2/8 (全局44)
   内容：在讨论思考的代价之前，我们需要先理解认知资源的概念...

📄 前1个 | 第三章 思考的代价  
   位置：3/8 (全局45)
   内容：认知资源包括注意力、工作记忆和执行控制等多个方面...

🎯 目标分块 | 第三章 思考的代价
   位置：4/8 (全局46)
   内容：思考需要消耗大量的认知资源，这就是我们所说的思考代价...

📄 后1个 | 第三章 思考的代价
   位置：5/8 (全局47)  
   内容：这种代价表现为处理速度的下降和错误率的增加...

📄 后2个 | 第三章 思考的代价
   位置：6/8 (全局48)
   内容：因此，在设计学习任务时，我们需要考虑认知负荷的管理...
```

## 🔧 技术实现

### 调用流程

1. **参数验证**: Zod schema验证输入参数
2. **获取当前书籍**: 从activeBookStore获取书籍ID
3. **上下文检索**: 调用Tauri后端的`plugin:epub|get_chunk_with_context`命令
4. **结果处理**: 标识目标分块，计算相对位置
5. **格式化输出**: 生成带位置标识的格式化文本

### 后端集成

```typescript
const results = await invoke("plugin:epub|get_chunk_with_context", {
  bookId: activeBookId,
  chunkId: chunk_id,
  prevCount: prev_count ?? 2,
  nextCount: next_count ?? 2,
});
```

## 🎯 使用场景

### 1. 理解概念定义 (最常见)
```typescript
// 从ragSearch获得概念相关分块
const searchResult = await ragSearch({...});
const targetChunkId = searchResult.results[0].position.chunk_id;

// 获取完整的概念解释上下文
await ragContext({
  reasoning: "概念定义不够完整，需要前后文来理解",
  chunk_id: targetChunkId,
  prev_count: 2,
  next_count: 2
});
```

### 2. 补充论证过程
```typescript
await ragContext({
  reasoning: "需要了解论点的论证过程和结论",
  chunk_id: 5678,
  prev_count: 3,  // 获取更多前文了解论证起始
  next_count: 1   // 获取结论部分
});
```

### 3. 理解对话或叙述连贯性
```typescript
await ragContext({
  reasoning: "对话内容需要前后文才能理解完整含义",
  chunk_id: 9876,
  prev_count: 1,
  next_count: 1
});
```

## 🔗 与其他工具的配合

### 典型工作流程

```typescript
// 标准的两步检索流程

// 第一步：搜索相关内容
const searchResults = await ragSearch({
  reasoning: "用户询问关于XX的问题，需要搜索相关内容",
  question: "用户问题",
  limit: 5
});

// 第二步：分析top1结果，如果需要更多上下文
if (needMoreContext(searchResults.results[0])) {
  const contextResults = await ragContext({
    reasoning: "搜索结果需要更多上下文才能完整回答用户问题",
    chunk_id: searchResults.results[0].position.chunk_id,
    prev_count: 2,
    next_count: 2
  });
  
  // 基于扩展的上下文生成回答
  generateAnswer(contextResults);
}
```

### 避免的使用方式

❌ **不要**连续调用多个ragContext
```typescript
// 错误做法
await ragContext({ chunk_id: 1234 });
await ragContext({ chunk_id: 1235 });
await ragContext({ chunk_id: 1236 });
```

✅ **应该**一次性获取足够的上下文
```typescript
// 正确做法
await ragContext({ 
  chunk_id: 1235,  // 中间的分块
  prev_count: 3,   // 获取前面的内容
  next_count: 3    // 获取后面的内容
});
```

## ⚡ 性能特点

- **响应速度**: 50-200ms（本地数据库查询）
- **内容连贯性**: 保证按文档顺序返回连续内容
- **智能边界**: 自动处理文档开头/结尾的边界情况

## ⚠️ 注意事项

### 使用限制
1. **chunk_id必须存在**: 使用无效的ID会导致错误
2. **跨章节边界**: 可能跨越多个章节，注意内容的逻辑连贯性
3. **内容长度**: 过多的prev_count和next_count可能产生过长的内容

### 最佳实践

#### 参数设置建议
- **简单概念**: prev_count=1, next_count=1
- **复杂解释**: prev_count=2, next_count=2  
- **论证过程**: prev_count=3, next_count=1
- **完整段落**: prev_count=2, next_count=3

#### 什么时候使用ragContext
✅ **应该使用的情况**:
- ragSearch的top1结果内容不完整
- 需要理解概念的定义和解释
- 对话、论证需要前因后果
- 内容有明显的承接关系

❌ **不应该使用的情况**:
- ragSearch已经提供足够信息
- 用户询问整个章节内容（使用ragToc）
- 需要搜索其他相关概念（再次使用ragSearch）

## 🐛 常见问题

### Q: 如何确定需要多少个前后文分块？
A: 从少开始（1-2个），根据内容的复杂度和连贯性需求调整

### Q: 返回的内容可能跨越多个章节吗？
A: 是的，分块是按全局顺序排列的，可能跨越章节边界

### Q: chunk_id从哪里获取？
A: 通常来自ragSearch结果中的position.chunk_id字段

### Q: 如果目标分块不存在怎么办？
A: 工具会抛出错误，应该在调用前确保chunk_id的有效性

---

> 💡 **提示**: ragContext是理解复杂内容的关键工具，但要避免过度使用。优先分析ragSearch的结果，只在确实需要更多上下文时才调用。 