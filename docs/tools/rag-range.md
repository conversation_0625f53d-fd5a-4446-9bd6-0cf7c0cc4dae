# ragRange - 范围连续检索工具

## 📋 基本信息

| 属性 | 值 |
|------|-----|
| 工具名称 | ragRange |
| 文件位置 | `/src/ai/tools/ragRange.ts` |
| 主要用途 | 基于全局分块索引范围获取连续的文档内容，可跨越不同章节 |
| 使用频率 | ⭐⭐ (低频，特殊范围需求时使用) |

## 🎯 功能描述

ragRange是范围检索的专用工具，用于获取指定全局索引范围内的连续文档内容。与其他工具不同，它可以跨越章节边界，获取文档中任意连续片段，适用于特殊的范围分析需求。

## 📝 参数定义

### 输入参数 (InputSchema)

```typescript
{
  reasoning: string,      // 必需 - 调用此工具的原因和目的
  start_index: number,    // 必需 - 起始全局索引（包含）
  end_index: number,      // 可选 - 结束全局索引（不包含），默认为start_index+10
  max_chunks: number      // 可选 - 最大返回分块数，默认20个，范围1-50
}
```

### 参数详解

#### reasoning (必需)
- **类型**: string
- **验证**: 最小长度1
- **说明**: 解释为什么需要获取特定范围的内容
- **示例**: 
  - `"需要获取特定索引范围的连续内容进行分析"`
  - `"用户指定了特定的文档范围"`
  - `"需要分析跨章节的连续内容"`

#### start_index (必需)
- **类型**: number (整数)
- **验证**: 最小值0
- **说明**: 起始的全局分块索引（包含在结果中）
- **获取方式**: 
  - 从ragSearch结果的position.global_index获取
  - 从ragContext结果中获取
  - 用户明确指定的索引值

#### end_index (可选)
- **类型**: number (整数)
- **验证**: 最小值0
- **默认值**: start_index + 10
- **说明**: 结束的全局分块索引（不包含在结果中）
- **注意**: 必须大于start_index

#### max_chunks (可选)
- **类型**: number (整数)
- **默认值**: 20
- **范围**: 1-50
- **说明**: 限制返回的最大分块数量，防止结果过大

## 📤 输出结构

### 返回数据结构

```typescript
{
  // 范围基本信息
  range: {
    start_index: number,        // 起始索引
    end_index: number,          // 结束索引
    requested_size: number,     // 请求的范围大小
    actual_size: number        // 实际返回的大小
  },
  
  // 涉及的章节
  chapters: Array<{
    toc_id: string,            // TOC节点ID
    chapter_title: string,     // 章节标题
    toc_depth: number,         // TOC层级深度
    chunk_count: number,       // 该章节在范围内的分块数
    first_global: number,      // 该章节在范围内的第一个全局索引
    last_global: number       // 该章节在范围内的最后一个全局索引
  }>,
  
  // 完整的范围内容
  content: Array<{
    chunk_id: number,          // 分块ID
    sequence: number,          // 在范围中的序号 (1, 2, 3...)
    content: string,           // 分块内容
    
    // 章节信息
    chapter_info: {
      toc_id: string,            // TOC节点ID
      chapter_title: string,     // 章节标题
      toc_depth: number         // TOC层级深度
    },
    
    // 位置信息
    position: {
      global_index: number,      // 全局索引
      expected_index: number,    // 期望索引
      in_toc: string,           // 在TOC中的位置 "3/10"
      md_source: string         // Markdown源文件
    }
  }>,
  
  // 格式化文本
  formatted: string,
  
  // 统计信息
  stats: {
    total_chunks: number,           // 总分块数
    total_characters: number,       // 总字符数
    average_chunk_length: number,   // 平均分块长度
    chapters_spanned: number,       // 跨越的章节数
    first_chunk_id: number,        // 第一个分块ID
    last_chunk_id: number         // 最后一个分块ID
  },
  
  // 元信息
  meta: {
    reasoning: string           // 调用原因
  }
}
```

### 格式化输出示例

```text
[范围检索] 全局索引 45-54 的连续内容
💭 调用原因：需要获取特定索引范围的连续内容进行分析
📚 跨越 2 个章节，共 10 个分块

涉及章节：
  1. 第三章 思考的代价 (num_3) - 6个分块
  2. 第四章 学习策略 (num_4) - 4个分块

📄 #1 | 全局45 | 第三章 思考的代价
   位置：3/8 | 来源：chapter_03.md
   内容：认知负荷理论为我们提供了理解思考代价的理论框架...

📄 #2 | 全局46 | 第三章 思考的代价
   位置：4/8 | 来源：chapter_03.md
   内容：当我们面临复杂的认知任务时，大脑需要分配有限的认知资源...

--- 📖 第四章 学习策略 ---

  📄 #7 | 全局51 | 第四章 学习策略
     位置：1/12 | 来源：chapter_04.md
     内容：有效的学习策略能够帮助学习者更好地管理认知资源...

📄 #8 | 全局52 | 第四章 学习策略
   位置：2/12 | 来源：chapter_04.md
   内容：元认知策略是学习策略中最重要的组成部分之一...
```

## 🔧 技术实现

### 调用流程

1. **参数验证和处理**: 验证索引范围，计算实际范围大小
2. **边界检查**: 确保end_index > start_index，处理max_chunks限制
3. **范围检索**: 调用Tauri后端的`plugin:epub|get_chunks_by_range`命令
4. **章节分析**: 识别涉及的章节，统计每章节的分块数
5. **内容组织**: 按全局索引顺序组织内容，标识章节变化
6. **统计计算**: 计算字符数、章节数等统计信息
7. **格式化输出**: 生成带章节标识的格式化文本

### 后端集成

```typescript
// 计算实际范围
const actualEndIndex = end_index ?? start_index + 10;
const requestedRange = Math.min(actualEndIndex - start_index, actualMaxChunks);
const finalEndIndex = start_index + requestedRange;

// 调用后端接口
const results = await invoke("plugin:epub|get_chunks_by_range", {
  bookId: activeBookId,
  startIndex: start_index,
  endIndex: finalEndIndex,
});
```

## 🎯 使用场景

### 1. 特定范围分析 (主要用途)
```typescript
await ragRange({
  reasoning: "需要分析全局索引100-120之间的内容连续性",
  start_index: 100,
  end_index: 120,
  max_chunks: 20
});
```

### 2. 跨章节内容检索
```typescript
// 基于ragSearch结果，获取跨章节的连续内容
await ragRange({
  reasoning: "搜索结果显示相关内容跨越多个章节，需要连续内容",
  start_index: 78,  // 从某个章节的末尾
  end_index: 95,    // 到下个章节的开始
  max_chunks: 15
});
```

### 3. 文档结构分析
```typescript
await ragRange({
  reasoning: "分析文档特定部分的结构和连贯性",
  start_index: 200,
  end_index: 250,
  max_chunks: 50
});
```

## 🔗 与其他工具的配合

### 典型工作流程

#### 场景1：基于搜索结果扩展范围
```typescript
// 第一步：搜索获取相关内容
const searchResults = await ragSearch({
  reasoning: "搜索用户问题相关内容",
  question: "学习迁移",
  limit: 5
});

// 第二步：分析结果分布，发现需要连续范围
const minIndex = Math.min(...searchResults.results.map(r => r.position.global_index));
const maxIndex = Math.max(...searchResults.results.map(r => r.position.global_index));

// 第三步：获取包含所有相关内容的连续范围
if (maxIndex - minIndex > 5 && maxIndex - minIndex < 30) {
  await ragRange({
    reasoning: "搜索结果分布在连续范围内，需要获取完整的连续内容",
    start_index: minIndex - 2,  // 增加一些前文
    end_index: maxIndex + 3,    // 增加一些后文
    max_chunks: 25
  });
}
```

#### 场景2：用户明确指定范围
```typescript
// 用户说："我想看看全局索引50到70的内容"
await ragRange({
  reasoning: "用户明确要求查看特定全局索引范围的内容",
  start_index: 50,
  end_index: 70,
  max_chunks: 20
});
```

### 与其他工具的区别

| 工具 | 范围依据 | 边界限制 | 主要用途 |
|------|---------|----------|----------|
| ragContext | 基于chunk_id的前后文 | 受prev_count/next_count限制 | 理解特定分块的上下文 |
| ragToc | 基于toc_id的章节边界 | 限于单个章节内 | 获取完整章节内容 |
| ragRange | 基于全局索引的任意范围 | 可跨越任意边界 | 分析特定范围或跨章节内容 |

## ⚡ 性能特点

- **响应速度**: 150-800ms（取决于范围大小）
- **跨越能力**: 可以跨越章节、文件边界
- **灵活性**: 支持任意范围指定，最适合特殊需求

## ⚠️ 注意事项

### 使用限制
1. **索引范围有效性**: 必须确保索引在文档范围内
2. **范围大小控制**: 过大的范围可能影响性能和可读性
3. **跨章节连贯性**: 跨章节内容可能存在主题跳跃

### 最佳实践

#### 什么时候使用ragRange
✅ **应该使用的情况**:
- 用户明确指定了索引范围
- ragSearch结果显示相关内容分散在连续索引中
- 需要分析跨章节的内容连续性
- 进行文档结构或内容流分析

❌ **不应该使用的情况**:
- 只需要单个分块的上下文（使用ragContext）
- 需要完整章节内容（使用ragToc）
- 简单的概念查询（使用ragSearch）
- 范围过大导致内容过载

#### 参数设置建议
- **小范围分析**: 5-10个分块
- **中等范围**: 10-20个分块
- **大范围分析**: 20-30个分块（需谨慎）
- **max_chunks设置**: 通常设为范围大小的1.2倍作为安全边界

#### 索引计算技巧
```typescript
// 基于搜索结果计算合理范围
const globalIndices = searchResults.results.map(r => r.position.global_index);
const minIndex = Math.min(...globalIndices);
const maxIndex = Math.max(...globalIndices);
const rangeSize = maxIndex - minIndex + 1;

// 如果范围合理，使用ragRange
if (rangeSize > 3 && rangeSize <= 25) {
  await ragRange({
    reasoning: "基于搜索结果计算的连续范围",
    start_index: minIndex - 1,    // 增加前文
    end_index: maxIndex + 2,      // 增加后文
    max_chunks: rangeSize + 5     // 预留空间
  });
}
```

## 🐛 常见问题

### Q: 如何确定合适的start_index和end_index？
A: 通常基于ragSearch的结果，或者基于用户明确的需求。避免盲目指定范围

### Q: ragRange与ragContext的区别是什么？
A: ragContext基于chunk_id获取前后文，ragRange基于全局索引获取任意范围

### Q: 跨章节的内容会影响理解吗？
A: 可能会，因此要注意范围的合理性，必要时配合章节信息进行说明

### Q: 如果指定的范围超出文档边界会怎样？
A: 系统会返回实际存在的分块，不会报错，但要检查actual_size与requested_size的差异

### Q: 什么情况下范围太大？
A: 一般超过30个分块就可能造成信息过载，建议控制在20个以内

---

> 💡 **提示**: ragRange是最灵活但也最需要谨慎使用的工具。它适合特殊的分析需求，但不应该成为常用工具。大多数情况下，ragSearch + ragContext/ragToc的组合更合适。 