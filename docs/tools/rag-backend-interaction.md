# RAG 工具与后端交互完整指南

## 📋 概述

本文档详细说明了 RAG（Retrieval-Augmented Generation）工具系统与后端的交互机制，包括所有工具的功能、参数、后端命令和数据流。

## 🏗️ 系统架构

### 数据流概览
```
前端 RAG 工具 → Tauri 命令 → 后端数据库 → 返回结果 → 前端处理 → AI 模型
```

### 核心组件
- **前端工具**: TypeScript 实现的 AI 工具
- **Tauri 命令**: 跨平台 API 桥接
- **后端数据库**: SQLite + sqlite-vec 向量数据库
- **数据结构**: 统一的 DocumentChunk 结构

## 🛠️ RAG 工具详解

### 1. ragSearch - 向量搜索工具

#### 🎯 功能描述
基于语义相似度进行向量搜索，返回最相关的文档分块。

#### 📝 工具参数
```typescript
{
  query: string;           // 搜索查询文本
  limit?: number;          // 返回结果数量，默认5个
  reasoning?: string;      // 调用原因说明
  format_output?: boolean; // 是否格式化输出，默认true
}
```

#### 🔗 后端交互
```typescript
// 前端调用
const results = await invoke("plugin:epub|search_similar_chunks", {
  bookId: activeBookId,
  query: query,
  limit: limit,
  vectorConfig: getVectorModelConfig()
});
```

#### 🗄️ 后端命令
```rust
#[tauri::command]
pub async fn search_similar_chunks<R: Runtime>(
    app: AppHandle<R>,
    _state: State<'_, EpubState>,
    book_id: String,
    query: String,
    limit: usize,
    vector_config: VectorConfig,
) -> Result<Vec<SearchItemDto>, String>
```

#### 📊 数据库操作
```rust
// 向量搜索
db.search_similar_chunks(&embedding, limit)
```

#### 🎨 输出格式
- **结构化数据**: 包含位置信息的增强搜索结果
- **格式化文本**: 可选的用户友好格式
- **引用信息**: 标准化的引用数据

---

### 2. ragToc - 章节检索工具

#### 🎯 功能描述
基于章节标题获取完整章节内容，支持精确匹配和模糊匹配。

#### 📝 工具参数
```typescript
{
  chapter_title: string;   // 章节标题或关键词
  reasoning: string;       // 调用原因说明
}
```

#### 🔗 后端交互
```typescript
// 前端调用
const results = await invoke("plugin:epub|get_toc_chunks", {
  bookId: activeBookId,
  chapterTitle: chapter_title
});
```

#### 🗄️ 后端命令
```rust
#[tauri::command]
pub async fn get_toc_chunks<R: Runtime>(
    app: AppHandle<R>,
    _state: State<'_, EpubState>,
    book_id: String,
    chapter_title: String,
) -> Result<Vec<DocumentChunkDto>, String>
```

#### 📊 数据库操作
```rust
// 智能章节匹配
db.get_chunks_by_chapter_title(&chapter_title)

// 内部实现：
// 1. 精确匹配：split('|').any(|title| title.trim() == chapter_title)
// 2. 模糊匹配：WHERE related_chapter_titles LIKE '%chapter_title%'
```

#### 🎨 输出格式
- **章节信息**: 章节标题、文件信息、分块统计
- **完整内容**: 按文件内顺序排列的所有分块
- **位置信息**: 文件路径、顺序、分块位置

---

### 3. ragContext - 上下文检索工具

#### 🎯 功能描述
基于分块ID获取前后文内容，用于扩展上下文信息。

#### 📝 工具参数
```typescript
{
  chunk_id: number;        // 目标分块的数据库ID
  prev_count?: number;     // 前面分块数量，默认2个
  next_count?: number;     // 后面分块数量，默认2个
  reasoning: string;       // 调用原因说明
}
```

#### 🔗 后端交互
```typescript
// 前端调用
const results = await invoke("plugin:epub|get_chunk_with_context", {
  bookId: activeBookId,
  chunkId: chunk_id,
  prevCount: prev_count ?? 2,
  nextCount: next_count ?? 2
});
```

#### 🗄️ 后端命令
```rust
#[tauri::command]
pub async fn get_chunk_with_context<R: Runtime>(
    app: AppHandle<R>,
    _state: State<'_, EpubState>,
    book_id: String,
    chunk_id: i64,
    prev_count: usize,
    next_count: usize,
) -> Result<Vec<DocumentChunkDto>, String>
```

#### 📊 数据库操作
```rust
// 上下文检索
db.get_chunk_with_context(chunk_id, prev_count, next_count)

// 内部实现：基于 global_chunk_index 范围查询
```

#### 🎨 输出格式
- **目标分块**: 高亮显示的目标分块
- **前文**: 按顺序排列的前面分块
- **后文**: 按顺序排列的后面分块
- **位置标记**: 清晰的位置和顺序信息

---

### 4. ragRange - 范围检索工具

#### 🎯 功能描述
基于全局索引范围获取连续的文档内容，可跨越不同章节。

#### 📝 工具参数
```typescript
{
  start_index: number;     // 起始全局索引（包含）
  end_index?: number;      // 结束全局索引（不包含）
  max_chunks?: number;     // 最大返回分块数，默认20个
  reasoning: string;       // 调用原因说明
}
```

#### 🔗 后端交互
```typescript
// 前端调用
const results = await invoke("plugin:epub|get_chunks_by_range", {
  bookId: activeBookId,
  startIndex: start_index,
  endIndex: actualEndIndex,
  maxChunks: actualMaxChunks
});
```

#### 🗄️ 后端命令
```rust
#[tauri::command]
pub async fn get_chunks_by_range<R: Runtime>(
    app: AppHandle<R>,
    _state: State<'_, EpubState>,
    book_id: String,
    start_index: usize,
    end_index: usize,
    max_chunks: usize,
) -> Result<Vec<DocumentChunkDto>, String>
```

#### 📊 数据库操作
```rust
// 范围检索
db.get_chunks_by_range(start_index, end_index, max_chunks)

// 内部实现：WHERE global_chunk_index BETWEEN start AND end
```

#### 🎨 输出格式
- **连续内容**: 按全局索引顺序的连续分块
- **跨章节**: 可能包含多个章节的内容
- **范围信息**: 实际检索的索引范围
- **统计信息**: 分块数量和覆盖范围

## 📊 统一数据结构

### DocumentChunk 类型定义
```typescript
type DocumentChunk = {
  id?: number;                    // 数据库ID
  book_title: string;             // 书籍标题
  book_author: string;            // 书籍作者
  md_file_path: string;           // MD文件路径
  file_order_in_book: number;     // 文件在书中的顺序
  related_chapter_titles: string; // 相关章节标题（|分隔）
  chunk_text: string;             // 分块文本内容
  chunk_order_in_file: number;    // 在文件中的分块顺序
  total_chunks_in_file: number;   // 该文件的总分块数
  global_chunk_index: number;     // 全局分块索引
};
```

### SearchItemDto 类型定义
```typescript
type SearchItemDto = {
  book_title: string;             // 书籍标题
  book_author: string;            // 书籍作者
  related_chapter_titles: string; // 相关章节标题
  content: string;                // 分块内容
  similarity: number;             // 相似度分数
};
```

## 🔄 工具选择策略

### 使用场景指南

#### 1. **内容发现** → ragSearch
- 用户询问特定主题或概念
- 需要找到相关的文档片段
- 语义搜索和相似度匹配

#### 2. **章节阅读** → ragToc
- 用户想了解特定章节的完整内容
- 基于章节标题或关键词查找
- 获取结构化的章节信息

#### 3. **上下文扩展** → ragContext
- 需要理解特定分块的前后文
- 扩展搜索结果的上下文
- 深入理解特定内容

#### 4. **连续阅读** → ragRange
- 需要获取特定范围的连续内容
- 跨章节的内容分析
- 基于索引的精确定位

### 工具组合策略
```typescript
// 常用组合模式
const toolCombinations = {
  // 深度分析：搜索 → 上下文 → 章节
  deepAnalysis: ["ragSearch", "ragContext", "ragToc"],
  
  // 内容发现：搜索 → 章节
  contentDiscovery: ["ragSearch", "ragToc"],
  
  // 精确定位：范围 → 上下文
  preciseLocation: ["ragRange", "ragContext"],
};
```

## 🚀 性能优化

### 数据库索引
- `related_chapter_titles`: 章节搜索优化
- `md_file_path`: 文件级别查询优化
- `global_chunk_index`: 范围和上下文查询优化
- `(file_order_in_book, chunk_order_in_file)`: 复合索引

### 缓存策略
- 向量模型配置缓存
- 常用查询结果缓存
- 章节信息缓存

### 错误处理
- 统一的错误处理机制
- 友好的错误信息
- 自动重试和回退策略

## 🎯 最佳实践

### 工具调用
1. **明确目的**: 在 reasoning 参数中说明调用原因
2. **合理限制**: 设置适当的 limit 和 max_chunks
3. **错误处理**: 处理可能的异常情况
4. **结果验证**: 检查返回结果的有效性

### 性能考虑
1. **批量操作**: 避免频繁的小量查询
2. **索引利用**: 充分利用数据库索引
3. **内存管理**: 控制大量数据的内存使用
4. **并发控制**: 避免同时进行大量查询

## 🔧 配置管理

### 向量模型配置
```typescript
// 向量模型配置获取
function getVectorModelConfig() {
  const llamaState = useLlamaStore.getState();

  // 外部向量模型（优先）
  if (llamaState.vectorModelEnabled) {
    const selectedModel = llamaState.getSelectedVectorModel();
    if (selectedModel) {
      return {
        baseUrl: selectedModel.url,
        model: selectedModel.modelId,
        apiKey: selectedModel.apiKey || null,
        dimension: 1024,
        source: "external",
      };
    }
  }

  // 本地 llama.cpp 服务（回退）
  const port = llamaState.currentSession?.port;
  const baseUrl = port ? `http://127.0.0.1:${port}` : "http://127.0.0.1:3544";

  return {
    baseUrl,
    model: "local-embed",
    apiKey: null,
    dimension: 1024,
    source: "local",
  };
}
```

### 活跃书籍管理
```typescript
// 获取当前活跃书籍
const { activeBookId } = useActiveBookStore.getState();
if (!activeBookId) {
  throw new Error("未找到当前阅读图书，请先在阅读器中打开图书");
}
```

## ⚠️ 错误处理机制

### 常见错误类型

#### 1. **书籍未选择错误**
```typescript
// 错误信息
"未找到当前阅读图书，请先在阅读器中打开图书"

// 解决方案
- 确保在阅读器中打开了图书
- 检查 activeBookStore 状态
```

#### 2. **章节未找到错误**
```typescript
// 错误信息
`未找到章节 "${chapter_title}" 的内容`

// 解决方案
- 检查章节标题拼写
- 尝试使用关键词搜索
- 使用模糊匹配功能
```

#### 3. **向量配置错误**
```typescript
// 错误信息
"向量模型配置无效或服务不可用"

// 解决方案
- 检查向量模型服务状态
- 验证 API 配置
- 回退到本地服务
```

#### 4. **数据库连接错误**
```typescript
// 错误信息
"数据库连接失败或文件不存在"

// 解决方案
- 检查书籍是否已完成向量化
- 验证数据库文件路径
- 重新进行向量化处理
```

### 错误处理最佳实践
```typescript
try {
  const results = await invoke("plugin:epub|search_similar_chunks", params);
  return processResults(results);
} catch (error) {
  // 记录错误
  console.error("RAG工具调用失败:", error);

  // 用户友好的错误信息
  const userMessage = typeof error === "string"
    ? error
    : error?.message || "检索失败，请稍后重试";

  throw new Error(userMessage);
}
```

## 📈 监控和调试

### 性能监控
```typescript
// 调用时间监控
const startTime = performance.now();
const results = await invoke(command, params);
const duration = performance.now() - startTime;
console.log(`${command} 执行时间: ${duration.toFixed(2)}ms`);
```

### 调试信息
```typescript
// 详细的调试日志
console.log("RAG工具调用:", {
  tool: "ragSearch",
  params: { query, limit },
  bookId: activeBookId,
  vectorConfig: getVectorModelConfig(),
  timestamp: new Date().toISOString()
});
```

## 🔮 未来扩展

### 计划中的功能

#### 1. **ragFile 工具**
- 基于文件路径获取完整文件内容
- 支持文件级别的内容分析
- 提供文件元数据信息

#### 2. **BM25 搜索集成**
- 混合向量搜索和 BM25 搜索
- 提升章节搜索的准确性
- 支持多种搜索策略

#### 3. **智能工具选择**
- 基于查询内容自动推荐工具
- 工具组合策略优化
- 上下文感知的工具调用

#### 4. **缓存优化**
- 查询结果缓存
- 向量计算缓存
- 智能缓存失效策略

## 📚 相关文档

- [文件中心架构重构文档](../new-chunk/README.md)
- [向量化系统文档](../md-vectorization-system.md)
- [Tauri 集成文档](../tauri-integration.md)
- [前端架构文档](../frontend-architecture.md)

---

这套 RAG 工具系统提供了完整的文档检索和分析能力，通过统一的数据结构和清晰的交互接口，为 AI 应用提供了强大的知识检索支持。系统采用了文件中心架构，具有高性能、高可靠性和良好的扩展性。
