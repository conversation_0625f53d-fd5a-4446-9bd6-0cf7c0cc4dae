# ragToc - 章节完整检索工具

## 📋 基本信息

| 属性 | 值 |
|------|-----|
| 工具名称 | ragToc |
| 文件位置 | `/src/ai/tools/ragToc.ts` |
| 主要用途 | 基于TOC节点ID获取该章节的完整内容，按章节内顺序返回所有分块 |
| 使用频率 | ⭐⭐⭐ (中频，章节讨论时使用) |

## 🎯 功能描述

ragToc是章节级别内容检索的专用工具，当用户询问整个章节内容，或者ragSearch结果表明需要完整章节理解时使用。它能够获取指定TOC节点下的所有内容分块，保持章节内的逻辑顺序。

## 📝 参数定义

### 输入参数 (InputSchema)

```typescript
{
  reasoning: string,    // 必需 - 调用此工具的原因和目的
  toc_id: string       // 必需 - TOC节点ID，如 'num_1', 'num_2' 等
}
```

### 参数详解

#### reasoning (必需)
- **类型**: string
- **验证**: 最小长度1
- **说明**: 解释为什么需要获取整个章节的内容
- **示例**: 
  - `"用户想了解整个章节的内容"`
  - `"需要完整的章节内容来回答用户的问题"`
  - `"用户询问第X章讲了什么"`

#### toc_id (必需)
- **类型**: string
- **验证**: 最小长度1
- **说明**: TOC（Table of Contents）节点的唯一标识符
- **格式**: 通常为 `"num_1"`, `"num_2"`, `"num_3"` 等
- **获取方式**: 
  - 从ragSearch结果的position.toc_id字段获取
  - 从书籍元信息和目录中直接引用
  - 从用户明确指定的章节编号转换

## 📤 输出结构

### 返回数据结构

```typescript
{
  // 章节基本信息
  chapter: {
    toc_id: string,            // TOC节点ID
    chapter_title: string,     // 章节标题
    toc_depth: number,         // TOC层级深度
    total_chunks: number,      // 总分块数
    md_source: string         // Markdown源文件名
  },
  
  // 完整的章节内容
  content: Array<{
    chunk_id: number,          // 分块ID
    sequence: number,          // 在章节中的序号 (1, 2, 3...)
    content: string,           // 分块内容
    
    // 位置信息
    position: {
      in_chapter: string,        // 在章节中的位置 "3/10"
      global_index: number,      // 全局索引
      is_first: boolean,         // 是否为章节开始
      is_last: boolean          // 是否为章节结束
    }
  }>,
  
  // 格式化文本
  formatted: string,
  
  // 统计信息
  stats: {
    total_chunks: number,           // 总分块数
    total_characters: number,       // 总字符数
    average_chunk_length: number,   // 平均分块长度
    first_chunk_id: number,        // 第一个分块ID
    last_chunk_id: number         // 最后一个分块ID
  },
  
  // 元信息
  meta: {
    reasoning: string           // 调用原因
  }
}
```

### 格式化输出示例

```text
[章节内容] 第三章 思考的代价 (num_3)
💭 调用原因：用户想了解整个章节的内容
📖 层级深度：1 | 分块数：8 | 来源：chapter_03.md

📌 第1块 [章节开始]
   位置：1/8 (全局43)
   内容：在本章中，我们将深入探讨思考过程中涉及的各种代价。思考并非没有成本的活动，它需要消耗认知资源，包括注意力、工作记忆和执行控制...

📄 第2块
   位置：2/8 (全局44)
   内容：认知负荷理论为我们提供了理解思考代价的理论框架。根据该理论，人类的信息处理能力是有限的...

📄 第3块
   位置：3/8 (全局45)
   内容：当我们面临复杂的认知任务时，大脑需要分配有限的认知资源来处理信息...

📄 第4块
   位置：4/8 (全局46)
   内容：实验研究表明，高认知负荷的任务会导致反应时间增加和错误率上升...

📄 第5块
   位置：5/8 (全局47)
   内容：除了认知层面的代价，思考还涉及生理层面的成本...

📄 第6块
   位置：6/8 (全局48)
   内容：长时间的深度思考会导致精神疲劳，这种疲劳不仅影响当前的认知表现...

📄 第7块
   位置：7/8 (全局49)
   内容：了解思考的代价对于学习和教学具有重要意义...

📌 第8块 [章节结束]
   位置：8/8 (全局50)
   内容：总之，思考的代价是一个多维度的概念，涉及认知、生理和情感等多个方面。在设计学习任务时，我们需要平衡学习效果和认知负荷...
```

## 🔧 技术实现

### 调用流程

1. **参数验证**: Zod schema验证TOC ID格式
2. **获取当前书籍**: 从activeBookStore获取书籍ID
3. **章节检索**: 调用Tauri后端的`plugin:epub|get_toc_chunks`命令
4. **内容组织**: 按章节内顺序组织分块内容
5. **统计计算**: 计算字符数、平均长度等统计信息
6. **格式化输出**: 生成带标识的格式化文本

### 后端集成

```typescript
const results = await invoke("plugin:epub|get_toc_chunks", {
  bookId: activeBookId,
  tocId: toc_id,
});
```

## 🎯 使用场景

### 1. 章节概览 (最常见)
```typescript
await ragToc({
  reasoning: "用户询问第3章讲了什么，需要完整的章节内容",
  toc_id: "num_3"
});
```

### 2. 深度章节讨论
```typescript
await ragToc({
  reasoning: "用户想深入讨论某章节的内容，需要完整信息",
  toc_id: "num_5"
});
```

### 3. 基于搜索结果的章节扩展
```typescript
// 第一步：搜索发现用户问题涉及整个章节
const searchResults = await ragSearch({
  reasoning: "搜索用户问题相关内容",
  question: "认知负荷管理策略",
  limit: 5
});

// 第二步：如果结果集中在某个章节，获取完整章节
if (needFullChapter(searchResults)) {
  await ragToc({
    reasoning: "搜索结果表明需要完整章节理解",
    toc_id: searchResults.results[0].position.toc_id
  });
}
```

## 🔗 与其他工具的配合

### 典型工作流程

#### 场景1：用户明确询问章节
```typescript
// 用户问："第2章讲了什么？"
await ragToc({
  reasoning: "用户明确询问第2章的内容",
  toc_id: "num_2"
});
```

#### 场景2：搜索后发现需要完整章节
```typescript
// 第一步：搜索
const searchResults = await ragSearch({
  reasoning: "用户询问关于学习策略的问题",
  question: "学习策略 元认知",
  limit: 5
});

// 第二步：分析结果，发现集中在某个章节
const mainTocId = findMostFrequentTocId(searchResults.results);

// 第三步：获取完整章节
await ragToc({
  reasoning: "搜索结果主要来自同一章节，需要完整内容进行讨论",
  toc_id: mainTocId
});
```

### 不适合的使用场景

❌ **不要**用于简单概念查询
```typescript
// 错误：用户只是想了解一个概念
await ragToc({ toc_id: "num_1" });  // 返回整个章节，信息过载
```

✅ **应该**先用ragSearch，再决定是否需要完整章节
```typescript
// 正确：先搜索相关概念
const results = await ragSearch({
  reasoning: "搜索概念相关内容",
  question: "工作记忆",
  limit: 3
});
```

## ⚡ 性能特点

- **响应速度**: 100-500ms（取决于章节大小）
- **内容完整性**: 保证章节内所有分块按顺序返回
- **内存效率**: 一次性加载整章内容，适合章节级讨论

## ⚠️ 注意事项

### 使用限制
1. **TOC ID必须存在**: 使用无效的TOC ID会导致错误
2. **内容长度**: 某些章节可能很长，需要注意输出长度管理
3. **层级结构**: 只返回指定TOC节点下的直接内容，不包含子章节

### 最佳实践

#### 什么时候使用ragToc
✅ **应该使用的情况**:
- 用户明确询问某章节内容："第X章讲了什么？"
- ragSearch结果表明问题涉及整个章节的理解
- 用户想要深入讨论某个主题，且该主题对应完整章节
- 需要章节级别的概览和总结

❌ **不应该使用的情况**:
- 用户只是询问简单概念或事实
- ragSearch已经提供足够的信息
- 需要跨多个章节的信息（使用多次ragSearch）
- 用户只需要部分内容（使用ragContext）

#### TOC ID的获取方式
1. **从ragSearch结果**: `results[0].position.toc_id`
2. **从书籍元信息**: 直接引用目录中的TOC ID
3. **用户指定**: "第3章" → "num_3"

#### 输出长度管理
- 大章节可能包含大量内容，注意在UI中合理展示
- 可以提供章节摘要而非完整内容
- 支持分页或折叠显示

## 🐛 常见问题

### Q: 如何知道书籍有哪些TOC ID？
A: 查看书籍的元信息和目录，或从ragSearch结果中观察toc_id模式

### Q: TOC ID的命名规则是什么？
A: 通常遵循 "num_1", "num_2" 等模式，具体取决于书籍的处理方式

### Q: 章节太长怎么办？
A: 可以配合ragSearch先找到相关部分，再使用ragContext获取局部上下文

### Q: 如何处理多级标题结构？
A: ragToc只返回指定TOC节点的直接内容，不包含子章节。如需子章节，需要单独调用

### Q: 章节内容的顺序是否可靠？
A: 是的，内容按照在原文档中的顺序返回，保证逻辑连贯性

---

> 💡 **提示**: ragToc适合章节级别的讨论和概览，但要避免在简单查询中使用，以免产生信息过载。结合用户意图和ragSearch结果来决定是否需要完整章节内容。 