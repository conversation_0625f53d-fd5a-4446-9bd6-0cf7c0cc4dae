# ragSearch - 向量相似度检索工具

## 📋 基本信息

| 属性 | 值 |
|------|-----|
| 工具名称 | ragSearch |
| 文件位置 | `/src/ai/tools/ragSearch.ts` |
| 主要用途 | 在当前图书中进行向量检索，返回相关片段及其精确位置信息 |
| 使用频率 | ⭐⭐⭐⭐⭐ (最高，所有RAG查询的起点) |

## 🎯 功能描述

ragSearch是RAG系统的核心工具，使用向量相似度算法在当前电子书中查找与用户问题最相关的内容片段。它不仅返回内容本身，还提供精确的位置信息，支持后续的智能上下文检索。

## 📝 参数定义

### 输入参数 (InputSchema)

```typescript
{
  reasoning: string,    // 必需 - 调用此工具的原因和目的
  question: string,     // 必需 - 用户的问题，将进行向量化相似度检索
  limit: number,        // 可选 - 返回的片段数量，默认5个，范围1-20
  format: boolean       // 可选 - 是否返回格式化的上下文文本，默认true
}
```

### 参数详解

#### reasoning (必需)
- **类型**: string
- **验证**: 最小长度1
- **说明**: 描述为什么调用这个工具
- **示例**: 
  - `"用户询问关于思考代价的问题，需要搜索相关内容"`
  - `"需要找到与个体差异相关的内容片段"`

#### question (必需)
- **类型**: string  
- **验证**: 最小长度1
- **说明**: 用户的实际问题，系统会对此进行向量化并匹配相似内容
- **示例**: 
  - `"什么是思考的代价"`
  - `"个体差异如何影响学习效果"`

#### limit (可选)
- **类型**: number (整数)
- **默认值**: 5
- **范围**: 1-20
- **说明**: 控制返回结果的数量，通常5个结果足以覆盖相关内容

#### format (可选)
- **类型**: boolean
- **默认值**: true
- **说明**: 是否返回格式化的文本输出，建议保持默认值

## 📤 输出结构

### 返回数据结构

```typescript
{
  // 增强的结构化数据，包含位置信息
  results: Array<{
    rank: number,           // 相似度排名 (1, 2, 3...)
    chapter_title: string,  // 章节标题
    similarity: number,     // 相似度百分比 (0-100)
    content: string,        // 内容片段
    
    // 关键位置信息，用于后续上下文检索
    position: {
      chunk_id: number,           // 分块ID，用于ragContext
      toc_id: string,             // TOC节点ID，用于ragToc  
      toc_depth: number,          // TOC层级深度
      global_index: number,       // 全局索引，用于ragRange
      toc_position: string,       // 在TOC中的位置 "3/10"
      md_source: string           // Markdown源文件名
    }
  }>,
  
  // 格式化文本（如果format=true）
  formatted: string | null,
  
  // 元信息
  meta: {
    reasoning: string,      // 调用原因
    total_found: number,    // 找到的结果总数
    book_id: string,        // 书籍ID
    query: string          // 原始查询
  }
}
```

### 格式化输出示例

```text
[RAG检索结果] 找到 5 个相关片段，包含位置信息：
💭 调用原因：用户询问关于思考代价的问题，需要搜索相关内容

【1 | 相似度92.3%】
章节：第三章 思考的代价
位置：TOC-num_3 第2/8块 (深度1, 全局45)
思考需要消耗大量的认知资源，包括注意力、工作记忆和执行控制...
---

【2 | 相似度87.1%】
章节：第一章 认知负荷理论
位置：TOC-num_1 第5/12块 (深度1, 全局12)
认知负荷理论表明，人类的信息处理能力是有限的...
---
```

## 🔧 技术实现

### 调用流程

1. **参数验证**: Zod schema验证输入参数
2. **获取当前书籍**: 从activeBookStore获取当前阅读的书籍ID
3. **向量检索**: 调用Tauri后端的`plugin:epub|search_db`命令
4. **结果处理**: 格式化位置信息和相似度分数
5. **输出生成**: 根据format参数决定是否生成格式化文本

### 后端集成

```typescript
const results = await invoke("plugin:epub|search_db", {
  bookId: activeBookId,
  query: question,
  limit: limit ?? 5,
  dimension: 1024,
  baseUrl: "http://127.0.0.1:3544", // 本地embedding服务
  model: "local-embed",
  apiKey: null,
});
```

## 🎯 使用场景

### 1. 问题回答 (最常见)
```typescript
ragSearch({
  reasoning: "用户询问关于XX的问题，需要搜索相关内容",
  question: "用户的具体问题",
  limit: 5
})
```

### 2. 概念解释
```typescript
ragSearch({
  reasoning: "用户想了解某个概念的定义",
  question: "概念名称 定义 解释",
  limit: 3
})
```

### 3. 主题讨论
```typescript
ragSearch({
  reasoning: "用户想讨论某个主题，需要找到相关内容",
  question: "主题关键词",
  limit: 8
})
```

## 🔗 与其他工具的配合

### 与ragContext配合
```typescript
// 第一步：搜索相关内容
const searchResults = await ragSearch({...});

// 第二步：如果需要更多上下文
const contextResults = await ragContext({
  reasoning: "需要获取更多上下文来理解搜索结果",
  chunk_id: searchResults.results[0].position.chunk_id,
  prev_count: 2,
  next_count: 2
});
```

### 与ragToc配合
```typescript
// 第一步：搜索找到相关章节
const searchResults = await ragSearch({...});

// 第二步：获取整个章节内容
const tocResults = await ragToc({
  reasoning: "用户想了解整个章节的内容",
  toc_id: searchResults.results[0].position.toc_id
});
```

## ⚡ 性能特点

- **响应速度**: 通常200-800ms（取决于文档大小）
- **精确度**: 基于语义相似度，通常top1结果相关性>80%
- **扩展性**: 支持1-20个结果，建议使用5-8个获得最佳效果

## ⚠️ 注意事项

### 使用限制
1. **必须有打开的电子书**: 工具依赖activeBookStore中的当前书籍
2. **需要本地embedding服务**: 依赖本地的向量化服务运行
3. **reasoning参数必填**: 不提供会导致验证错误

### 最佳实践
1. **优先分析top1结果**: 通常最相关，重点关注
2. **合理设置limit**: 太少可能遗漏信息，太多影响效率
3. **提供清晰的reasoning**: 帮助用户理解工具调用目的
4. **基于结果决定后续操作**: 判断是否需要调用扩展工具

## 🐛 常见问题

### Q: 搜索结果不相关怎么办？
A: 尝试调整question的关键词，使用更具体或更泛化的表达

### Q: 为什么有时候找不到内容？
A: 可能是书籍还没有完成向量化索引，或者查询词与书籍内容差异太大

### Q: limit设置多少比较合适？
A: 常规问答建议5个，深度讨论可以8-10个，概念查询可以3个

---

> 💡 **提示**: ragSearch是所有RAG操作的起点，掌握它的使用对整个RAG系统至关重要。 