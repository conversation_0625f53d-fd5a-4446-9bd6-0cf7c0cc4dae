# RAG 工具交互流程图

## 📋 概述

本文档通过流程图和时序图详细展示 RAG 工具与后端的交互流程。

## 🔄 整体交互流程

```mermaid
graph TD
    A[AI 模型] --> B[RAG 工具调用]
    B --> C{工具类型}
    
    C -->|ragSearch| D[向量搜索]
    C -->|ragToc| E[章节检索]
    C -->|ragContext| F[上下文检索]
    C -->|ragRange| G[范围检索]
    
    D --> H[获取向量配置]
    H --> I[Tauri 命令调用]
    
    E --> J[验证章节标题]
    J --> I
    
    F --> K[验证分块ID]
    K --> I
    
    G --> L[验证索引范围]
    L --> I
    
    I --> M[后端数据库操作]
    M --> N[返回结果]
    N --> O[前端结果处理]
    O --> P[格式化输出]
    P --> Q[返回给 AI 模型]
```

## 🔍 ragSearch 详细流程

```mermaid
sequenceDiagram
    participant AI as AI 模型
    participant Tool as ragSearch 工具
    participant Store as 状态管理
    participant Tau<PERSON> as Tauri 命令
    participant DB as 向量数据库
    
    AI->>Tool: 调用 ragSearch(query, limit)
    Tool->>Store: 获取 activeBookId
    Tool->>Store: 获取向量模型配置
    
    alt 书籍未选择
        Store-->>Tool: null
        Tool-->>AI: 抛出错误
    else 正常流程
        Store-->>Tool: bookId + vectorConfig
        Tool->>Tauri: search_similar_chunks
        Tauri->>DB: 向量搜索
        DB-->>Tauri: 搜索结果
        Tauri-->>Tool: DocumentChunk[]
        Tool->>Tool: 结果处理和格式化
        Tool-->>AI: 格式化的搜索结果
    end
```

## 📖 ragToc 详细流程

```mermaid
sequenceDiagram
    participant AI as AI 模型
    participant Tool as ragToc 工具
    participant Store as 状态管理
    participant Tauri as Tauri 命令
    participant DB as 数据库
    
    AI->>Tool: 调用 ragToc(chapter_title)
    Tool->>Store: 获取 activeBookId
    
    alt 书籍未选择
        Store-->>Tool: null
        Tool-->>AI: 抛出错误
    else 正常流程
        Store-->>Tool: bookId
        Tool->>Tauri: get_toc_chunks
        Tauri->>DB: 章节搜索
        
        alt 精确匹配
            DB->>DB: split('|') 精确匹配
            DB-->>Tauri: 匹配结果
        else 模糊匹配
            DB->>DB: LIKE '%title%' 模糊搜索
            DB-->>Tauri: 搜索结果
        end
        
        Tauri-->>Tool: DocumentChunk[]
        Tool->>Tool: 构建章节信息
        Tool-->>AI: 完整章节内容
    end
```

## 🔗 ragContext 详细流程

```mermaid
sequenceDiagram
    participant AI as AI 模型
    participant Tool as ragContext 工具
    participant Store as 状态管理
    participant Tauri as Tauri 命令
    participant DB as 数据库
    
    AI->>Tool: 调用 ragContext(chunk_id, prev, next)
    Tool->>Store: 获取 activeBookId
    
    alt 书籍未选择
        Store-->>Tool: null
        Tool-->>AI: 抛出错误
    else 正常流程
        Store-->>Tool: bookId
        Tool->>Tauri: get_chunk_with_context
        Tauri->>DB: 上下文查询
        DB->>DB: 基于 global_chunk_index 范围查询
        DB-->>Tauri: 上下文分块
        Tauri-->>Tool: DocumentChunk[]
        Tool->>Tool: 标记目标分块
        Tool-->>AI: 带上下文的内容
    end
```

## 📏 ragRange 详细流程

```mermaid
sequenceDiagram
    participant AI as AI 模型
    participant Tool as ragRange 工具
    participant Store as 状态管理
    participant Tauri as Tauri 命令
    participant DB as 数据库
    
    AI->>Tool: 调用 ragRange(start, end, max)
    Tool->>Store: 获取 activeBookId
    Tool->>Tool: 计算实际范围
    
    alt 参数无效
        Tool-->>AI: 抛出错误
    else 正常流程
        Store-->>Tool: bookId
        Tool->>Tauri: get_chunks_by_range
        Tauri->>DB: 范围查询
        DB->>DB: WHERE global_chunk_index BETWEEN
        DB-->>Tauri: 范围分块
        Tauri-->>Tool: DocumentChunk[]
        Tool->>Tool: 构建范围信息
        Tool-->>AI: 连续范围内容
    end
```

## 🗄️ 数据库操作流程

```mermaid
graph TD
    A[Tauri 命令] --> B{操作类型}
    
    B -->|向量搜索| C[计算查询向量]
    C --> D[sqlite-vec 搜索]
    D --> E[JOIN document_chunks]
    
    B -->|章节搜索| F[解析章节标题]
    F --> G{匹配策略}
    G -->|精确| H[split + filter]
    G -->|模糊| I[LIKE 查询]
    
    B -->|上下文查询| J[获取目标分块]
    J --> K[计算索引范围]
    K --> L[范围查询]
    
    B -->|范围查询| M[验证索引范围]
    M --> N[BETWEEN 查询]
    
    E --> O[返回结果]
    H --> O
    I --> O
    L --> O
    N --> O
    
    O --> P[转换为 DTO]
    P --> Q[返回前端]
```

## ⚡ 性能优化流程

```mermaid
graph TD
    A[查询请求] --> B{缓存检查}
    B -->|命中| C[返回缓存结果]
    B -->|未命中| D[数据库查询]
    
    D --> E{索引优化}
    E -->|向量搜索| F[向量索引]
    E -->|章节搜索| G[章节标题索引]
    E -->|范围查询| H[全局索引]
    
    F --> I[执行查询]
    G --> I
    H --> I
    
    I --> J[结果处理]
    J --> K[更新缓存]
    K --> L[返回结果]
    
    C --> M[性能监控]
    L --> M
    M --> N[日志记录]
```

## 🚨 错误处理流程

```mermaid
graph TD
    A[工具调用] --> B{前置检查}
    B -->|失败| C[参数错误]
    B -->|成功| D[后端调用]
    
    D --> E{后端响应}
    E -->|成功| F[结果处理]
    E -->|失败| G[错误分类]
    
    G --> H{错误类型}
    H -->|网络错误| I[重试机制]
    H -->|数据错误| J[数据验证]
    H -->|系统错误| K[系统检查]
    
    I --> L{重试次数}
    L -->|未超限| D
    L -->|超限| M[返回错误]
    
    C --> N[用户友好错误]
    J --> N
    K --> N
    M --> N
    
    F --> O[成功返回]
    N --> P[错误返回]
```

## 📊 数据流转换

```mermaid
graph LR
    A[用户查询] --> B[工具参数]
    B --> C[Tauri 命令参数]
    C --> D[数据库查询]
    D --> E[原始数据]
    E --> F[DocumentChunk]
    F --> G[DTO 转换]
    G --> H[前端处理]
    H --> I[格式化输出]
    I --> J[AI 模型输入]
```

## 🔄 状态管理流程

```mermaid
stateDiagram-v2
    [*] --> Idle: 初始状态
    Idle --> Loading: 工具调用
    Loading --> Success: 调用成功
    Loading --> Error: 调用失败
    Success --> Idle: 结果返回
    Error --> Idle: 错误处理
    Error --> Loading: 重试调用
    
    Loading: 显示加载状态
    Success: 处理返回结果
    Error: 显示错误信息
```

## 🎯 工具选择决策树

```mermaid
graph TD
    A[用户查询] --> B{查询类型}
    
    B -->|语义搜索| C[ragSearch]
    B -->|章节内容| D[ragToc]
    B -->|上下文扩展| E[ragContext]
    B -->|连续内容| F[ragRange]
    
    C --> G{结果满意?}
    G -->|否| H[ragContext 扩展]
    G -->|是| I[返回结果]
    
    D --> J{需要上下文?}
    J -->|是| K[ragContext 补充]
    J -->|否| I
    
    E --> I
    F --> I
    H --> I
    K --> I
```

这些流程图清晰地展示了 RAG 工具系统的各个组件如何协同工作，为开发者提供了完整的系统理解和调试指南。
