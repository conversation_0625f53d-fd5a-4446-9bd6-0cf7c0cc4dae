# Book接口字段详细解释

## 📚 核心标识和格式字段

### hash: string (现在用作 id)
- **作用**：书籍的唯一哈希标识符，是整个系统中识别书籍的核心键
- **用途**：用作数据库主键、文件夹命名、缓存键等
- **生成方式**：使用`partialMD5(file)`基于书籍内容计算的哈希值，确保同一本书始终有相同的hash
- **重复检测**：在上传时自动检测重复书籍，避免重复存储
- **代码示例**：在`getDir(book: Book)`中用作`${book.hash}`创建存储目录

### format: BookFormat
- **支持格式**：`"EPUB" | "PDF" | "MOBI" | "CBZ" | "FB2" | "FBZ"`
- **用途**：决定使用哪个解析引擎，影响阅读界面和功能
- **代码关联**：在`DocumentLoader`中根据格式选择不同的解析器

## 🔗 内容来源字段（互斥设计）

### url?: string
- **场景**：远程书籍（云端存储、在线图书馆）
- **功能**：懒加载机制，需要时才下载内容
- **注释**：`// if Book is a remote book we just lazy load the book content via url`

### filePath?: string
- **场景**：本地临时书籍（用户刚导入但未永久保存）
- **功能**：直接从本地文件系统读取
- **注释**：`// if Book is a transient local book we can load the book content via filePath`

## 📖 基本信息字段

### title: string
- **特点**：用户可编辑的显示标题
- **来源**：从书籍元数据提取，用户可后续修改
- **注释**：`// editable title from metadata`
- **保护机制**：重新导入时，如果用户已修改过标题，会保留用户的修改

### sourceTitle?: string
- **作用**：保存导入时的原始文件名/标题，作为"源真相"
- **用途**：用于文件定位和重新导入时的匹配
- **注释**：`// parsed when the book is imported and used to locate the file`
- **更新逻辑**：每次重新导入时都会更新为当前文件的原始标题

### title vs sourceTitle 详细区别

#### 初次导入时
```typescript
// 导入新书时，两个字段都设置为相同值
const book: Book = {
  title: formatTitle(loadedBook.metadata.title),      // 用户可见的标题
  sourceTitle: formatTitle(loadedBook.metadata.title), // 原始标题备份
  // ...
};
```

#### 重新导入时的保护逻辑
```typescript
// 重新导入同一本书时
if (existingBook) {
  // 保留用户可能修改过的title，只有为空时才用新的
  existingBook.title = existingBook.title ?? book.title;
  
  // sourceTitle总是更新为最新的原始标题
  existingBook.sourceTitle = existingBook.sourceTitle ?? book.sourceTitle;
}
```

#### 实际使用场景
1. **用户编辑标题**：用户在元数据编辑界面修改了书名
   - `title`: "我的自定义书名" (用户修改后)
   - `sourceTitle`: "Original Book Title" (保持原始)

2. **重新导入书籍**：用户重新导入了同一本书的新版本
   - `title`: "我的自定义书名" (保留用户修改)
   - `sourceTitle`: "Updated Original Title" (更新为新版本的原始标题)

3. **文件定位**：系统需要找到对应的物理文件
   - 使用`sourceTitle`来匹配文件系统中的文件名

### author: string
- **特点**：必填字段，作者信息
- **用途**：在界面中广泛显示，用于搜索和分类

### primaryLanguage?: string
- **含义**：书籍主要语言代码（如 "en", "zh-CN"）
- **影响**：文字方向、排版等

## 📁 组织和分类字段

### 分组字段演进
- **group?: string** - 已废弃的旧分组方式
- **groupId?: string** - 新的分组ID，支持更复杂的分组结构
- **groupName?: string** - 分组显示名称

### tags?: string[]
- **用途**：用户自定义标签数组
- **功能**：支持多标签分类和过滤

## 🖼️ 封面管理

### coverImageUrl?: string | null
- **含义**：封面图片的URL地址
- **类型**：可能是本地路径或远程URL
- **null值**：表示无封面或封面加载失败

## ⏰ 完整的时间戳体系

### 核心时间戳
- **createdAt: number** - 书籍首次添加到库中的时间
- **updatedAt: number** - 书籍信息最后修改时间

### 软删除支持
- **deletedAt?: number | null** - 软删除时间戳，支持回收站功能

### 同步相关时间戳
- **uploadedAt?: number | null** - 上传到云端的时间
- **downloadedAt?: number | null** - 从云端下载的时间
- **coverDownloadedAt?: number | null** - 封面下载完成时间

### 废弃字段
- **lastUpdated?: number** - 被updatedAt取代，保留为向后兼容

## 📊 阅读进度系统

### progress?: [number, number]
- **格式**：`[当前页码, 总页数]`
- **特点**：基于1的页码（用户友好的显示方式）
- **用途**：
  - 在书籍列表中显示阅读进度条
  - 恢复上次阅读位置
  - 统计阅读完成情况
- **代码实现**：在`setProgress`方法中计算并更新
- **显示逻辑**：在`ProgressInfoView`组件中格式化显示

## 📋 丰富的元数据系统

### metadata?: BookMetadata
包含详细的图书元数据：

#### 基础信息
- **title**: 原始标题（可能是多语言对象）
- **author**: 作者信息（可能包含详细的贡献者信息）
- **language**: 语言信息（字符串或数组）
- **editor**: 编辑者
- **publisher**: 出版社
- **published**: 出版日期
- **description**: 书籍描述
- **subject**: 主题分类数组
- **identifier**: ISBN等标识符

#### 系列信息
- **subtitle**: 副标题
- **series**: 系列名称
- **seriesIndex**: 在系列中的序号
- **seriesTotal**: 系列总数

#### 封面相关
- **coverImageFile**: 封面文件路径
- **coverImageUrl**: 封面URL
- **coverImageBlobUrl**: 封面的Blob URL

## 🔄 数据流转和转换

这个Book接口在系统中的数据流：
1. **导入阶段**：从文件解析生成Book对象
2. **存储阶段**：通过`transformBookToDB`转换为DBBook存储
3. **读取阶段**：通过`transformBookFromDB`转换回Book对象
4. **显示阶段**：在各种UI组件中使用

## 💡 设计亮点

1. **灵活的内容来源**：url和filePath的设计支持多种书籍来源
2. **完整的时间轴**：从创建到删除的完整生命周期追踪
3. **渐进式分组**：从简单的group到复杂的groupId/groupName
4. **软删除支持**：deletedAt字段支持回收站功能
5. **元数据分离**：将详细元数据分离到BookMetadata，保持主结构简洁
6. **用户编辑保护**：title/sourceTitle的双重设计保护用户的自定义修改

这个接口设计体现了一个成熟的电子书管理系统的完整需求，支持本地/远程书籍、完整的元数据管理、阅读进度追踪、分类组织等功能。

## 📝 接口定义

```typescript
export interface Book {
  // 内容来源字段（互斥）
  url?: string;                    // 远程书籍URL
  filePath?: string;              // 本地文件路径
  
  // 核心标识
  hash: string;                   // 唯一哈希标识符
  format: BookFormat;             // 书籍格式
  
  // 基本信息
  title: string;                  // 可编辑标题
  sourceTitle?: string;           // 原始标题
  author: string;                 // 作者
  primaryLanguage?: string;       // 主要语言
  
  // 组织分类
  group?: string;                 // 已废弃
  groupId?: string;              // 分组ID
  groupName?: string;            // 分组名称
  tags?: string[];               // 标签数组
  
  // 封面
  coverImageUrl?: string | null; // 封面URL
  
  // 时间戳
  createdAt: number;             // 创建时间
  updatedAt: number;             // 更新时间
  deletedAt?: number | null;     // 删除时间
  uploadedAt?: number | null;    // 上传时间
  downloadedAt?: number | null;  // 下载时间
  coverDownloadedAt?: number | null; // 封面下载时间
  lastUpdated?: number;          // 已废弃
  
  // 阅读进度
  progress?: [number, number];   // [当前页, 总页数]
  
  // 详细元数据
  metadata?: BookMetadata;       // 详细元数据对象
}
```
