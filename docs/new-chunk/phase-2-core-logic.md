# 阶段二：核心处理逻辑重构

## 📋 概述

阶段二专注于重构核心的 EPUB 处理逻辑，移除复杂的位置估算和区间分配算法，简化为文件级别的处理。

## 🎯 主要任务

### ✅ TODO 2.1：重构文件分组和处理逻辑
**文件**: `pipeline.rs`

#### 移除的复杂逻辑

##### 位置估算逻辑（第 208-243 行）
```rust
// ❌ 移除：复杂的标题匹配和位置估算
for (toc_index, n) in &nodes {
    let title = n.title.trim();
    if let Some(pos) = md_content[search_from..].find(title) {
        let global_pos = search_from + pos;
        starts.push((*toc_index, global_pos));
        search_from = global_pos + title.len();
    }
}

// ❌ 移除：等分估计位置填充
if starts.len() < nodes.len() {
    let content_len = md_content.len();
    let mut est_starts: Vec<(usize, usize)> = Vec::new();
    for (i, (toc_index, _n)) in nodes.iter().enumerate() {
        let est = (i as f32 / nodes.len() as f32 * content_len as f32) as usize;
        est_starts.push((*toc_index, est));
    }
    starts = est_starts;
}
```

##### 区间分配逻辑（第 246-268 行）
```rust
// ❌ 移除：复杂的区间计算和分块分配
let mut intervals: Vec<(usize, usize, usize)> = Vec::new();
for i in 0..starts.len() {
    let (toc_idx, s) = starts[i];
    let e = if i + 1 < starts.len() { starts[i + 1].1 } else { content_len };
    intervals.push((toc_idx, s, e));
}

// ❌ 移除：基于位置的分块分配
for (k, chunk_content) in chunks.into_iter().enumerate() {
    let est_pos = ((k as f32 / total_chunks as f32) * content_len as f32) as usize;
    let mut picked: Option<usize> = None;
    for (toc_idx, s, e) in &intervals {
        if est_pos >= *s && est_pos < *e { picked = Some(*toc_idx); break; }
    }
    let toc_index = picked.unwrap_or_else(|| intervals.last().map(|x| x.0).unwrap_or(0));
}
```

#### 新的简化逻辑

##### 文件级别处理
```rust
// ✅ 新增：收集该文件相关的所有 TOC 节点信息
let mut related_toc_nodes = Vec::new();
let mut min_play_order = u32::MAX;

for (_toc_index, node) in &nodes {
    related_toc_nodes.push(node.clone());
    min_play_order = min_play_order.min(node.play_order);
}

// ✅ 新增：生成章节标题字符串（用 | 分隔）
let related_chapter_titles = related_toc_nodes
    .iter()
    .map(|node| node.title.clone())
    .collect::<Vec<_>>()
    .join("|");
```

##### 直接分片关联
```rust
// ✅ 新增：为该文件的每个分片创建记录
for (chunk_index, chunk_content) in chunks.into_iter().enumerate() {
    if chunk_content.trim().is_empty() { continue; }

    all_chunks.push((
        md_src.clone(),
        min_play_order,
        related_chapter_titles.clone(),
        chunk_index,
        total_chunks,
        chunk_content,
    ));
}
```

### ✅ TODO 2.2：重写分片生成和存储逻辑
**文件**: `pipeline.rs`

#### 数据结构变化
```rust
// 旧的 all_chunks 结构
all_chunks: Vec<(
    FlatTocNode,    // TOC 节点
    usize,          // TOC 索引
    usize,          // 在 TOC 中的分片索引
    usize,          // TOC 总分片数
    String,         // 分片内容
)>

// 新的 all_chunks 结构
all_chunks: Vec<(
    String,         // MD 文件路径
    u32,            // 文件顺序
    String,         // 相关章节标题（| 分隔）
    usize,          // 在文件中的分片索引
    usize,          // 文件总分片数
    String,         // 分片内容
)>
```

### ✅ TODO 2.3：更新 DocumentChunk 创建逻辑
**文件**: `pipeline.rs`

#### 旧的创建逻辑
```rust
// ❌ 旧逻辑：基于 TOC 节点创建
DocumentChunk {
    chapter_title: toc_node.title.clone(),
    chapter_order: toc_node.play_order as usize,
    md_src: toc_node.md_src.clone(),
    toc_depth: toc_node.depth,
    toc_id: toc_node.id.clone(),
    toc_index: *toc_index,
    chunk_index_in_toc: *chunk_idx_in_toc,
    total_chunks_in_toc: *total_chunks_in_toc,
    // ...
}
```

#### 新的创建逻辑
```rust
// ✅ 新逻辑：基于文件信息创建
DocumentChunk {
    md_file_path: md_src.clone(),
    file_order_in_book: *file_order,
    related_chapter_titles: related_chapter_titles.clone(),
    chunk_order_in_file: *chunk_index,
    total_chunks_in_file: *total_chunks,
    // ...
}
```

### ✅ TODO 2.4：更新进度报告逻辑
**文件**: `pipeline.rs`

#### ProgressUpdate 结构更新
```rust
// 旧结构
#[derive(Debug, Clone, Serialize)]
pub struct ProgressUpdate {
    pub current: usize,
    pub total: usize,
    pub percent: f32,
    pub chapter_title: String,  // ❌ 旧字段
    pub chunk_index: usize,
}

// 新结构
#[derive(Debug, Clone, Serialize)]
pub struct ProgressUpdate {
    pub current: usize,
    pub total: usize,
    pub percent: f32,
    pub md_file_path: String,           // ✅ 新字段
    pub chunk_index: usize,
    pub related_chapter_titles: String, // ✅ 新字段
}
```

#### 进度回调更新
```rust
// 旧回调
cb(ProgressUpdate {
    current: i + 1,
    total,
    percent: progress,
    chapter_title: toc_node.title.clone(),  // ❌ 旧字段
    chunk_index: *chunk_idx_in_toc,
});

// 新回调
cb(ProgressUpdate {
    current: i + 1,
    total,
    percent: progress,
    md_file_path: md_src.clone(),           // ✅ 新字段
    chunk_index: *chunk_index,
    related_chapter_titles: related_chapter_titles.clone(), // ✅ 新字段
});
```

## 🔄 处理流程对比

### 旧流程（章节中心）
```
1. 按 md_src 分组 TOC 节点
2. 对每个文件：
   a. 估算每个 TOC 节点在文件中的位置
   b. 计算 TOC 节点的文本区间
   c. 对文件进行分片
   d. 根据分片位置分配到对应的 TOC 节点
   e. 创建 DocumentChunk（一对一关系）
```

### 新流程（文件中心）
```
1. 按 md_src 分组 TOC 节点
2. 对每个文件：
   a. 收集所有相关的 TOC 节点信息
   b. 生成章节标题字符串（| 分隔）
   c. 对文件进行分片
   d. 每个分片关联所有相关章节
   e. 创建 DocumentChunk（一对多关系）
```

## 📊 性能改进

### 算法复杂度
- **旧算法**: O(n²) - 需要对每个分片计算与所有区间的关系
- **新算法**: O(n) - 直接为每个分片分配章节信息

### 代码行数减少
- **移除代码**: ~80 行复杂的位置估算和区间分配逻辑
- **新增代码**: ~20 行简单的文件级别处理逻辑
- **净减少**: ~60 行代码

### 可维护性提升
- **消除不确定性**: 移除了基于字符位置的估算
- **逻辑简化**: 处理流程更加直观
- **调试友好**: 更容易理解和调试

## 🎉 阶段二成果

### 完成的任务
- ✅ 完全移除了复杂的位置估算逻辑
- ✅ 完全移除了区间分配逻辑
- ✅ 简化为文件级别的直接处理
- ✅ 更新了所有相关的数据结构和进度报告

### 核心改进
- **可靠性提升**: 消除了位置估算的不确定性
- **性能优化**: 算法复杂度从 O(n²) 降低到 O(n)
- **代码简化**: 减少了约 60 行复杂逻辑
- **语义化**: 每个分片明确关联所有相关章节

### 架构优势
- **一对多关系**: 一个分片可以关联多个章节
- **信息完整**: 不会丢失章节关联信息
- **扩展性强**: 支持复杂的多章节场景
- **维护性好**: 逻辑清晰，易于理解和修改
