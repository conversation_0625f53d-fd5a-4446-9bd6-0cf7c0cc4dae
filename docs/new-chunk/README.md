# 文件中心架构重构文档

## 📋 重构概述

本次重构将 EPUB 处理系统从"章节中心"架构转换为"文件中心"架构，解决了复杂的章节边界检测问题，提供了更可靠和语义化的分块处理。

## 🎯 重构目标

### 原始问题
- **复杂的位置估算**：多个 TOC 章节指向同一个 markdown 文件，需要复杂的算法来估算章节边界
- **不准确的分块分配**：基于字符位置的估算经常产生错误的章节-分块关联
- **维护困难**：复杂的边界检测逻辑难以调试和维护

### 解决方案
- **文件级别处理**：以 markdown 文件为基本处理单元
- **多章节关联**：每个分块关联所有相关的章节标题（用 `|` 分隔）
- **简化架构**：移除复杂的位置估算和区间分配逻辑

## 🏗️ 架构对比

### 旧架构（章节中心）
```
TOC章节 → 估算位置 → 分配区间 → 分块归属
```

### 新架构（文件中心）
```
MD文件 → 直接分块 → 关联所有相关章节
```

## 📊 数据结构变化

### DocumentChunk 结构对比

#### 旧结构
```rust
pub struct DocumentChunk {
    // 章节信息
    chapter_title: String,
    chapter_order: u32,
    
    // TOC 导航信息
    md_src: String,
    toc_depth: u32,
    toc_id: String,
    toc_index: usize,
    
    // 分块位置信息
    chunk_index_in_toc: usize,
    total_chunks_in_toc: usize,
    
    // 其他字段...
}
```

#### 新结构
```rust
pub struct DocumentChunk {
    // 文件信息
    md_file_path: String,
    file_order_in_book: u32,
    
    // 章节关联信息（使用 | 分隔符）
    related_chapter_titles: String, // "第一章 引言|1.1 背景介绍|1.2 研究目标"
    
    // 分片信息
    chunk_order_in_file: usize,
    total_chunks_in_file: usize,
    
    // 其他字段...
}
```

## 🔄 重构阶段

本次重构分为五个阶段：

1. **[阶段一：数据结构重构](./phase-1-data-structures.md)**
2. **[阶段二：核心处理逻辑重构](./phase-2-core-logic.md)**
3. **[阶段三：数据库操作重构](./phase-3-database.md)**
4. **[阶段四：API和接口适配](./phase-4-api-interface.md)**
5. **[阶段五：BM25和工具集成](./phase-5-bm25-tools.md)**

## 📁 文档结构

- `README.md` - 重构概述（本文件）
- `phase-1-data-structures.md` - 数据结构重构详情
- `phase-2-core-logic.md` - 核心处理逻辑重构详情
- `phase-3-database.md` - 数据库操作重构详情
- `phase-4-api-interface.md` - API和接口适配详情
- `phase-5-bm25-tools.md` - BM25和工具集成详情
- `migration-guide.md` - 迁移指南
- `performance-comparison.md` - 性能对比分析
- `compilation-fixes.md` - 编译错误修复记录

## 🎉 重构成果

### 核心改进
- ✅ 彻底移除了复杂的章节边界猜测算法
- ✅ 简化为文件级别的处理，每个分片关联所有相关章节
- ✅ 使用 `|` 分隔符存储章节信息，便于后续 BM25 搜索
- ✅ 提供了更可靠和语义化的分块处理

### 技术优势
- **可靠性提升**：消除了位置估算的不确定性
- **维护性改善**：代码逻辑更简单清晰
- **扩展性增强**：支持复杂的多章节关联场景
- **性能优化**：减少了复杂的计算开销

## 🔗 相关文档

- [MD向量化系统文档](../md-vectorization-system.md)
- [Tauri集成文档](../tauri-integration.md)
- [工具系统文档](../tools/README.md)
