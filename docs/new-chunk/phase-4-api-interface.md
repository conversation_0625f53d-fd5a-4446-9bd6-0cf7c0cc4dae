# 阶段四：API和接口适配

## 📋 概述

阶段四专注于更新所有前端接口和工具，使其适配新的文件中心数据结构。

## 🎯 主要任务

### ✅ TODO 4.1：更新 Tauri 命令接口
**文件**: `commands.rs`

#### 更新的 DTO 结构
```rust
// 旧的 SearchItemDto
#[derive(Serialize)]
pub struct SearchItemDto {
    pub book_title: String,
    pub book_author: String,
    pub chapter_title: String,  // ❌ 移除
    pub content: String,
    pub similarity: f32,
}

// 新的 SearchItemDto
#[derive(Serialize)]
pub struct SearchItemDto {
    pub book_title: String,
    pub book_author: String,
    pub related_chapter_titles: String,  // ✅ 新增
    pub content: String,
    pub similarity: f32,
}
```

#### 更新的 DocumentChunkDto
```rust
// 旧的 DocumentChunkDto
#[derive(Serialize)]
pub struct DocumentChunkDto {
    pub id: Option<i64>,
    pub book_title: String,
    pub book_author: String,
    pub chapter_title: String,      // ❌ 移除
    pub chapter_order: u32,         // ❌ 移除
    pub chunk_text: String,
    pub chunk_order: usize,         // ❌ 移除
    pub md_src: String,             // ❌ 移除
    pub toc_depth: u32,             // ❌ 移除
    pub toc_id: String,             // ❌ 移除
    pub toc_index: usize,           // ❌ 移除
    pub chunk_index_in_toc: usize,  // ❌ 移除
    pub total_chunks_in_toc: usize, // ❌ 移除
    pub global_chunk_index: usize,
}

// 新的 DocumentChunkDto
#[derive(Serialize)]
pub struct DocumentChunkDto {
    pub id: Option<i64>,
    pub book_title: String,
    pub book_author: String,
    pub md_file_path: String,           // ✅ 新增
    pub file_order_in_book: u32,        // ✅ 新增
    pub related_chapter_titles: String, // ✅ 新增
    pub chunk_text: String,
    pub chunk_order_in_file: usize,     // ✅ 新增
    pub total_chunks_in_file: usize,    // ✅ 新增
    pub global_chunk_index: usize,
}
```

#### 更新的命令函数
```rust
// 旧的命令函数
#[tauri::command]
pub async fn get_toc_chunks<R: Runtime>(
    app: AppHandle<R>,
    _state: State<'_, EpubState>,
    book_id: String,
    toc_id: String,  // ❌ 旧参数
) -> Result<Vec<DocumentChunkDto>, String>

// 新的命令函数
#[tauri::command]
pub async fn get_toc_chunks<R: Runtime>(
    app: AppHandle<R>,
    _state: State<'_, EpubState>,
    book_id: String,
    chapter_title: String,  // ✅ 新参数
) -> Result<Vec<DocumentChunkDto>, String>
```

### ✅ TODO 4.2：更新前端 TypeScript 类型
**文件**: `ragToc.ts`, `ragContext.ts`, `ragRange.ts`

#### 更新的 DocumentChunk 类型
```typescript
// 旧的类型定义
type DocumentChunk = {
  id?: number;
  book_title: string;
  book_author: string;
  chapter_title: string;      // ❌ 移除
  chapter_order: number;      // ❌ 移除
  chunk_text: string;
  chunk_order: number;        // ❌ 移除
  md_src: string;             // ❌ 移除
  toc_depth: number;          // ❌ 移除
  toc_id: string;             // ❌ 移除
  toc_index: number;          // ❌ 移除
  chunk_index_in_toc: number; // ❌ 移除
  total_chunks_in_toc: number;// ❌ 移除
  global_chunk_index: number;
};

// 新的类型定义
type DocumentChunk = {
  id?: number;
  book_title: string;
  book_author: string;
  md_file_path: string;           // ✅ 新增
  file_order_in_book: number;     // ✅ 新增
  related_chapter_titles: string; // ✅ 新增
  chunk_text: string;
  chunk_order_in_file: number;    // ✅ 新增
  total_chunks_in_file: number;   // ✅ 新增
  global_chunk_index: number;
};
```

### ✅ TODO 4.3：更新 ragToc 工具
**文件**: `ragToc.ts`

#### 工具描述更新
```typescript
// 旧的工具描述
export const ragTocTool = tool({
  description: "根据TOC ID获取指定章节的所有分块内容，用于获取完整的章节信息",
  parameters: z.object({
    tocId: z.string().describe("TOC节点的唯一标识符"),  // ❌ 旧参数
    reasoning: z.string().optional().describe("调用此工具的原因"),
  }),
  // ...
});

// 新的工具描述
export const ragTocTool = tool({
  description: "根据章节标题获取指定章节的所有分块内容，用于获取完整的章节信息",
  parameters: z.object({
    chapterTitle: z.string().describe("章节标题，支持精确匹配和模糊匹配"),  // ✅ 新参数
    reasoning: z.string().optional().describe("调用此工具的原因"),
  }),
  // ...
});
```

#### 调用逻辑更新
```typescript
// 旧的调用逻辑
const chunks = await invoke<DocumentChunk[]>("get_toc_chunks", {
  bookId: currentBookId,
  tocId: args.tocId,  // ❌ 旧参数
});

// 新的调用逻辑
const chunks = await invoke<DocumentChunk[]>("get_toc_chunks", {
  bookId: currentBookId,
  chapterTitle: args.chapterTitle,  // ✅ 新参数
});
```

#### 结果处理更新
```typescript
// 旧的结果处理
const formattedChunks = chunks.map((chunk, index) => {
  const position = `第${chunk.chunk_index_in_toc + 1}/${chunk.total_chunks_in_toc}块`;  // ❌ 旧字段
  const source = `${chunk.chapter_title} - ${position}`;  // ❌ 旧字段
  
  return {
    index: index + 1,
    source,
    toc_info: `TOC-${chunk.toc_id} (深度${chunk.toc_depth})`,  // ❌ 旧字段
    content: chunk.chunk_text,
  };
});

// 新的结果处理
const formattedChunks = chunks.map((chunk, index) => {
  const position = `第${chunk.chunk_order_in_file + 1}/${chunk.total_chunks_in_file}块`;  // ✅ 新字段
  const source = `${chunk.related_chapter_titles} - ${position}`;  // ✅ 新字段
  
  return {
    index: index + 1,
    source,
    file_info: `文件-${chunk.md_file_path} (顺序${chunk.file_order_in_book})`,  // ✅ 新字段
    content: chunk.chunk_text,
  };
});
```

### ✅ TODO 4.4：更新 metadata.md 生成
**文件**: `pipeline.rs`

#### 旧的 metadata.md 生成
```rust
// ❌ 旧逻辑：基于 TOC ID
writeln!(metadata_content, "## 章节信息")?;
for (toc_index, node) in &flat_toc {
    writeln!(
        metadata_content,
        "- **TOC-{}**: {} (深度: {}, 顺序: {})",
        node.id, node.title, node.depth, node.play_order
    )?;
}
```

#### 新的 metadata.md 生成
```rust
// ✅ 新逻辑：基于章节标题
writeln!(metadata_content, "## 章节信息")?;
for (toc_index, node) in &flat_toc {
    writeln!(
        metadata_content,
        "- **{}**: 文件路径: {}, 顺序: {}",
        node.title, node.md_src, node.play_order
    )?;
}

writeln!(metadata_content, "\n## 文件信息")?;
let mut file_groups: std::collections::HashMap<String, Vec<&FlatTocNode>> = std::collections::HashMap::new();
for (_toc_index, node) in &flat_toc {
    file_groups.entry(node.md_src.clone()).or_insert_with(Vec::new).push(node);
}

for (md_file, nodes) in file_groups {
    let chapter_titles: Vec<String> = nodes.iter().map(|n| n.title.clone()).collect();
    writeln!(
        metadata_content,
        "- **{}**: 包含章节: {}",
        md_file,
        chapter_titles.join(" | ")
    )?;
}
```

### ✅ TODO 4.5：更新 EmbeddingDialog 组件
**文件**: `EmbeddingDialog.tsx`

#### 类型定义更新
```typescript
// 旧的类型定义
interface SearchItem {
  book_title: string;
  book_author: string;
  chapter_title: string;  // ❌ 移除
  content: string;
  similarity: number;
}

// 新的类型定义
interface SearchItem {
  book_title: string;
  book_author: string;
  related_chapter_titles: string;  // ✅ 新增
  content: string;
  similarity: number;
}
```

#### 显示逻辑更新
```tsx
// 旧的显示逻辑
<div className="text-sm text-gray-600 mb-2">
  章节: {chunk.chapter_title}  {/* ❌ 旧字段 */}
  <br />
  位置: TOC-{chunk.toc_id} 第{chunk.chunk_index_in_toc + 1}/{chunk.total_chunks_in_toc}块  {/* ❌ 旧字段 */}
  <br />
  深度: {chunk.toc_depth}, 顺序: {chunk.chapter_order}  {/* ❌ 旧字段 */}
</div>

// 新的显示逻辑
<div className="text-sm text-gray-600 mb-2">
  章节: {chunk.related_chapter_titles}  {/* ✅ 新字段 */}
  <br />
  位置: 文件-{chunk.md_file_path} 第{chunk.chunk_order_in_file + 1}/{chunk.total_chunks_in_file}块  {/* ✅ 新字段 */}
  <br />
  文件顺序: {chunk.file_order_in_book}, 全局索引: {chunk.global_chunk_index}  {/* ✅ 新字段 */}
</div>
```

### ✅ TODO 4.6：更新 ragSearch 工具
**文件**: `ragSearch.ts`

#### EnhancedSearchItem 类型更新
```typescript
// 旧的类型定义
type EnhancedSearchItem = {
  book_title: string;
  book_author: string;
  chapter_title: string;  // ❌ 移除
  content: string;
  similarity: number;
  
  chunk_id: number | null;
  toc_id: string;         // ❌ 移除
  toc_depth: number;      // ❌ 移除
  global_chunk_index: number;
  chunk_index_in_toc: number;     // ❌ 移除
  total_chunks_in_toc: number;    // ❌ 移除
  md_src: string;         // ❌ 移除
};

// 新的类型定义
type EnhancedSearchItem = {
  book_title: string;
  book_author: string;
  related_chapter_titles: string;  // ✅ 新增
  content: string;
  similarity: number;
  
  chunk_id: number | null;
  md_file_path: string;           // ✅ 新增
  file_order_in_book: number;     // ✅ 新增
  global_chunk_index: number;
  chunk_order_in_file: number;    // ✅ 新增
  total_chunks_in_file: number;   // ✅ 新增
};
```

## 🔄 接口迁移策略

### 参数映射
```
旧参数              → 新参数
tocId              → chapterTitle
toc_id             → chapter_title
```

### 字段映射
```
旧字段                    → 新字段
chapter_title            → related_chapter_titles
md_src                   → md_file_path
chapter_order            → file_order_in_book
chunk_index_in_toc       → chunk_order_in_file
total_chunks_in_toc      → total_chunks_in_file
toc_id, toc_depth        → (移除)
```

### 兼容性处理
- **渐进式迁移**: 保持旧接口一段时间，同时提供新接口
- **错误处理**: 对无效的章节标题提供友好的错误信息
- **回退机制**: 精确匹配失败时自动使用模糊匹配

## 🎉 阶段四成果

### 完成的任务
- ✅ 更新了所有 Tauri 命令接口和 DTO
- ✅ 更新了所有前端 TypeScript 类型定义
- ✅ 重构了 ragToc 工具的参数和逻辑
- ✅ 更新了 metadata.md 生成逻辑
- ✅ 适配了 EmbeddingDialog 组件
- ✅ 更新了 ragSearch 工具的类型定义

### 核心改进
- **接口统一**: 所有接口都使用新的数据结构
- **类型安全**: TypeScript 类型定义完全匹配后端结构
- **用户体验**: 更直观的章节标题参数替代抽象的 TOC ID
- **信息丰富**: 显示更多有用的文件和位置信息

### 技术优势
- **向前兼容**: 新接口设计考虑了未来扩展需求
- **错误处理**: 完善的错误处理和回退机制
- **性能优化**: 减少了不必要的数据传输
- **维护性**: 清晰的接口设计便于维护和调试
