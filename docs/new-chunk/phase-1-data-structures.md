# 阶段一：数据结构重构

## 📋 概述

阶段一专注于重新设计核心数据结构，从章节中心转向文件中心的架构。

## 🎯 主要任务

### ✅ TODO 1.1：重新设计 DocumentChunk 结构
**文件**: `database.rs`

#### 移除的字段
```rust
// 旧的章节中心字段
chapter_title: String,
chapter_order: u32,

// 旧的 TOC 导航字段
md_src: String,
toc_depth: u32,
toc_id: String,
toc_index: usize,

// 旧的分块位置字段
chunk_index_in_toc: usize,
total_chunks_in_toc: usize,
```

#### 新增的字段
```rust
// 文件信息
md_file_path: String,        // MD文件路径 "text/part001.md"
file_order_in_book: u32,     // 文件在书中的顺序

// 章节关联信息（使用 | 分隔符）
related_chapter_titles: String, // "第一章 引言|1.1 背景介绍|1.2 研究目标"

// 分片信息
chunk_order_in_file: usize,  // 在文件中的分片顺序
total_chunks_in_file: usize, // 该文件的总分片数
```

### ✅ TODO 1.2：更新数据库表结构
**文件**: `database.rs`

#### 新的表结构
```sql
CREATE TABLE IF NOT EXISTS document_chunks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_title TEXT NOT NULL,
    book_author TEXT NOT NULL,
    
    -- 文件信息
    md_file_path TEXT NOT NULL,
    file_order_in_book INTEGER NOT NULL,
    
    -- 章节关联信息（使用 | 分隔符）
    related_chapter_titles TEXT NOT NULL DEFAULT '',
    
    -- 分片信息
    chunk_text TEXT NOT NULL,
    chunk_order_in_file INTEGER NOT NULL,
    total_chunks_in_file INTEGER NOT NULL,
    
    -- 全局位置信息
    global_chunk_index INTEGER NOT NULL
);
```

#### 新增的索引
```sql
-- 为章节标题添加全文搜索索引
CREATE INDEX IF NOT EXISTS idx_related_chapter_titles ON document_chunks (related_chapter_titles);

-- 为文件路径添加索引
CREATE INDEX IF NOT EXISTS idx_md_file_path ON document_chunks (md_file_path);

-- 为文件顺序添加索引
CREATE INDEX IF NOT EXISTS idx_file_order ON document_chunks (file_order_in_book);

-- 为全局索引添加索引
CREATE INDEX IF NOT EXISTS idx_global_chunk_index ON document_chunks (global_chunk_index);

-- 复合索引：文件顺序 + 分片顺序
CREATE INDEX IF NOT EXISTS idx_file_chunk_order ON document_chunks (file_order_in_book, chunk_order_in_file);
```

### ✅ TODO 1.3：重新设计 SearchResult 结构
**文件**: `database.rs`

#### 扁平化设计
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    pub chunk_id: i64,
    pub book_title: String,
    pub book_author: String,
    pub md_file_path: String,
    pub file_order_in_book: u32,
    pub related_chapter_titles: String,
    pub chunk_text: String,
    pub chunk_order_in_file: usize,
    pub total_chunks_in_file: usize,
    pub global_chunk_index: usize,
    pub similarity_score: f32,
}
```

### ✅ TODO 1.4：新增章节搜索功能
**文件**: `database.rs`

#### 新增方法
```rust
/// 基于章节标题搜索分块（模糊匹配）
pub fn search_chunks_by_chapter(&self, chapter_query: &str, limit: usize) -> Result<Vec<DocumentChunk>>

/// 通过章节标题精确获取所有相关分块（用于 ragToc 工具）
pub fn get_chunks_by_chapter_title(&self, chapter_title: &str) -> Result<Vec<DocumentChunk>>
```

#### 搜索策略
1. **精确匹配**：分割 `|` 后精确匹配章节标题
2. **模糊匹配**：如果精确匹配无结果，使用 LIKE 查询

### ✅ TODO 1.5：更新 API 接口
**文件**: `commands.rs`

#### 更新的结构
```rust
#[derive(Serialize)]
pub struct SearchItemDto {
    pub book_title: String,
    pub book_author: String,
    pub related_chapter_titles: String,  // 新字段
    pub content: String,
    pub similarity: f32,
}

#[derive(Serialize)]
pub struct DocumentChunkDto {
    pub id: Option<i64>,
    pub book_title: String,
    pub book_author: String,
    pub md_file_path: String,           // 新字段
    pub file_order_in_book: u32,        // 新字段
    pub related_chapter_titles: String, // 新字段
    pub chunk_text: String,
    pub chunk_order_in_file: usize,     // 新字段
    pub total_chunks_in_file: usize,    // 新字段
    pub global_chunk_index: usize,
}
```

#### 更新的命令
```rust
/// 通过章节标题获取所有相关分块
#[tauri::command]
pub async fn get_toc_chunks<R: Runtime>(
    app: AppHandle<R>,
    _state: State<'_, EpubState>,
    book_id: String,
    chapter_title: String,  // 参数从 toc_id 改为 chapter_title
) -> Result<Vec<DocumentChunkDto>, String>
```

## 🔄 数据迁移

### 字段映射关系
```
旧字段                    → 新字段
chapter_title            → related_chapter_titles (单个章节)
md_src                   → md_file_path
chapter_order            → file_order_in_book (使用最小 play_order)
chunk_index_in_toc       → chunk_order_in_file
total_chunks_in_toc      → total_chunks_in_file
```

### 多章节关联处理
```rust
// 生成章节标题字符串（用 | 分隔）
let related_chapter_titles = related_toc_nodes
    .iter()
    .map(|node| node.title.clone())
    .collect::<Vec<_>>()
    .join("|");
```

## 🎉 阶段一成果

### 完成的任务
- ✅ 完全重新设计了 `DocumentChunk` 数据结构
- ✅ 更新了数据库表结构和索引
- ✅ 重新设计了 `SearchResult` 为扁平结构
- ✅ 新增了章节搜索功能
- ✅ 更新了所有相关的 API 接口和 DTO

### 核心改进
- **数据结构简化**：移除了复杂的 TOC 相关字段
- **多章节支持**：使用 `|` 分隔符支持一个文件关联多个章节
- **查询优化**：新增了多个索引提升查询性能
- **接口统一**：所有 API 接口都使用新的数据结构

### 为后续阶段奠定基础
阶段一的数据结构重构为后续的处理逻辑重构、数据库操作重构和 API 适配提供了坚实的基础。
