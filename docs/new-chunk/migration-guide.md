# 迁移指南

## 📋 概述

本指南详细说明了从章节中心架构迁移到文件中心架构的完整过程，包括数据迁移、代码更新和测试验证。

## 🗄️ 数据迁移

### 数据库结构变化

#### 旧表结构
```sql
CREATE TABLE document_chunks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_title TEXT NOT NULL,
    book_author TEXT NOT NULL,
    chapter_title TEXT NOT NULL,
    chapter_order INTEGER NOT NULL,
    chunk_text TEXT NOT NULL,
    chunk_order INTEGER NOT NULL,
    md_src TEXT NOT NULL,
    toc_depth INTEGER NOT NULL,
    toc_id TEXT NOT NULL,
    toc_index INTEGER NOT NULL,
    chunk_index_in_toc INTEGER NOT NULL,
    total_chunks_in_toc INTEGER NOT NULL,
    global_chunk_index INTEGER NOT NULL
);
```

#### 新表结构
```sql
CREATE TABLE document_chunks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_title TEXT NOT NULL,
    book_author TEXT NOT NULL,
    md_file_path TEXT NOT NULL,
    file_order_in_book INTEGER NOT NULL,
    related_chapter_titles TEXT NOT NULL DEFAULT '',
    chunk_text TEXT NOT NULL,
    chunk_order_in_file INTEGER NOT NULL,
    total_chunks_in_file INTEGER NOT NULL,
    global_chunk_index INTEGER NOT NULL
);
```

### 数据迁移脚本

#### 方案一：重新处理（推荐）
```bash
# 1. 备份现有数据
cp ~/.local/share/epub-reader/books/[book_id]/database.db backup_database.db

# 2. 删除现有数据库
rm ~/.local/share/epub-reader/books/[book_id]/database.db

# 3. 重新处理 EPUB 文件
# 使用新的处理逻辑重新生成数据库
```

#### 方案二：数据转换（复杂）
```sql
-- 创建临时表存储转换后的数据
CREATE TABLE document_chunks_new (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_title TEXT NOT NULL,
    book_author TEXT NOT NULL,
    md_file_path TEXT NOT NULL,
    file_order_in_book INTEGER NOT NULL,
    related_chapter_titles TEXT NOT NULL DEFAULT '',
    chunk_text TEXT NOT NULL,
    chunk_order_in_file INTEGER NOT NULL,
    total_chunks_in_file INTEGER NOT NULL,
    global_chunk_index INTEGER NOT NULL
);

-- 数据转换逻辑（需要复杂的聚合查询）
INSERT INTO document_chunks_new (
    book_title, book_author, md_file_path, file_order_in_book,
    related_chapter_titles, chunk_text, chunk_order_in_file,
    total_chunks_in_file, global_chunk_index
)
SELECT 
    book_title,
    book_author,
    md_src as md_file_path,
    MIN(chapter_order) as file_order_in_book,
    GROUP_CONCAT(DISTINCT chapter_title, '|') as related_chapter_titles,
    chunk_text,
    ROW_NUMBER() OVER (PARTITION BY md_src ORDER BY global_chunk_index) - 1 as chunk_order_in_file,
    COUNT(*) OVER (PARTITION BY md_src) as total_chunks_in_file,
    global_chunk_index
FROM document_chunks
GROUP BY md_src, global_chunk_index
ORDER BY global_chunk_index;

-- 替换原表
DROP TABLE document_chunks;
ALTER TABLE document_chunks_new RENAME TO document_chunks;
```

## 💻 代码迁移

### 后端代码更新

#### 1. 数据结构更新
```rust
// 更新所有使用 DocumentChunk 的代码
// 旧字段 → 新字段映射：
// chapter_title → related_chapter_titles
// md_src → md_file_path
// chapter_order → file_order_in_book
// chunk_index_in_toc → chunk_order_in_file
// total_chunks_in_toc → total_chunks_in_file
```

#### 2. 数据库查询更新
```rust
// 更新所有 SQL 查询语句
// 更新字段名称
// 更新索引定义
// 更新查询逻辑
```

#### 3. API 接口更新
```rust
// 更新 Tauri 命令参数
// toc_id → chapter_title
// 更新返回的 DTO 结构
```

### 前端代码更新

#### 1. TypeScript 类型更新
```typescript
// 更新所有 DocumentChunk 类型定义
// 更新 SearchItem 类型定义
// 更新相关接口调用
```

#### 2. 组件更新
```typescript
// 更新 EmbeddingDialog 组件
// 更新显示逻辑
// 更新 API 调用参数
```

#### 3. 工具更新
```typescript
// 更新 ragToc 工具参数和逻辑
// 更新 ragSearch 工具类型定义
// 更新 ragContext 和 ragRange 工具
```

## 🧪 测试验证

### 功能测试清单

#### 1. 数据处理测试
- [ ] EPUB 文件解析正常
- [ ] TOC 结构解析正确
- [ ] Markdown 文件分块正常
- [ ] 章节关联信息正确（使用 | 分隔）
- [ ] 文件顺序信息正确
- [ ] 全局索引连续性

#### 2. 数据库操作测试
- [ ] 数据插入正常
- [ ] 向量搜索功能正常
- [ ] 章节搜索功能正常（精确匹配）
- [ ] 章节搜索功能正常（模糊匹配）
- [ ] 统计查询功能正常
- [ ] 索引性能正常

#### 3. API 接口测试
- [ ] `get_toc_chunks` 命令正常（使用 chapter_title 参数）
- [ ] `search_similar_chunks` 命令正常
- [ ] 返回的 DTO 结构正确
- [ ] 错误处理正常

#### 4. 前端功能测试
- [ ] EmbeddingDialog 显示正常
- [ ] ragToc 工具功能正常
- [ ] ragSearch 工具功能正常
- [ ] ragContext 工具功能正常
- [ ] ragRange 工具功能正常

#### 5. 集成测试
- [ ] 完整的 EPUB 处理流程
- [ ] AI 工具调用正常
- [ ] 搜索结果准确性
- [ ] 性能表现正常

### 测试数据准备

#### 测试用例设计
```
1. 简单结构 EPUB
   - 单个文件对应单个章节
   - 验证基本功能

2. 复杂结构 EPUB
   - 多个章节指向同一文件
   - 验证多章节关联功能

3. 大型 EPUB
   - 大量章节和文件
   - 验证性能表现

4. 边界情况
   - 空章节标题
   - 特殊字符
   - 超长内容
```

### 性能基准测试

#### 关键指标
```
1. 处理速度
   - EPUB 解析时间
   - 分块处理时间
   - 向量化时间

2. 查询性能
   - 向量搜索响应时间
   - 章节搜索响应时间
   - 复杂查询响应时间

3. 内存使用
   - 处理过程内存峰值
   - 查询过程内存使用
   - 长期运行内存稳定性

4. 存储效率
   - 数据库文件大小
   - 索引空间占用
   - 压缩效果
```

## 🚨 常见问题和解决方案

### 问题 1：数据迁移失败
**症状**: 数据转换过程中出现错误
**解决方案**:
1. 检查原始数据完整性
2. 使用重新处理方案而非数据转换
3. 逐步迁移，分批处理

### 问题 2：章节搜索无结果
**症状**: 使用章节标题搜索时返回空结果
**解决方案**:
1. 检查章节标题的精确性
2. 使用模糊匹配作为回退
3. 检查 `related_chapter_titles` 字段的分隔符

### 问题 3：性能下降
**症状**: 查询响应时间明显增加
**解决方案**:
1. 检查索引是否正确创建
2. 分析查询执行计划
3. 优化查询语句

### 问题 4：前端显示异常
**症状**: 界面显示错误或缺失信息
**解决方案**:
1. 检查 TypeScript 类型定义
2. 验证 API 返回数据结构
3. 更新组件显示逻辑

## 📋 迁移检查清单

### 迁移前准备
- [ ] 备份现有数据库
- [ ] 备份配置文件
- [ ] 记录当前版本信息
- [ ] 准备测试数据

### 代码更新
- [ ] 后端数据结构更新
- [ ] 后端数据库操作更新
- [ ] 后端 API 接口更新
- [ ] 前端类型定义更新
- [ ] 前端组件更新
- [ ] 前端工具更新

### 测试验证
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 性能测试通过
- [ ] 用户验收测试通过

### 部署上线
- [ ] 生产环境备份
- [ ] 代码部署
- [ ] 数据迁移
- [ ] 功能验证
- [ ] 性能监控

## 🎯 迁移成功标准

### 功能完整性
- 所有原有功能正常工作
- 新功能按预期工作
- 无功能回归

### 性能表现
- 查询响应时间不超过原来的 120%
- 内存使用稳定
- 存储空间使用合理

### 数据一致性
- 所有数据正确迁移
- 章节关联信息准确
- 搜索结果准确

### 用户体验
- 界面显示正常
- 操作流程顺畅
- 错误处理友好
