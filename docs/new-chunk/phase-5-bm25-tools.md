# 阶段五：BM25和工具集成

## 📋 概述

阶段五专注于集成 BM25 搜索算法和优化工具系统，充分利用新的文件中心架构的优势。

## 🎯 主要任务

### 🔄 TODO 5.1：集成 BM25 章节搜索
**文件**: `database.rs`

#### BM25 搜索实现
```rust
/// 使用 BM25 算法进行章节内容搜索
pub fn bm25_search_chapters(&self, query: &str, limit: usize) -> Result<Vec<DocumentChunk>> {
    // 利用 related_chapter_titles 字段进行 BM25 搜索
    let mut stmt = self.conn.prepare(
        r#"
        SELECT
            id, book_title, book_author, md_file_path,
            file_order_in_book, related_chapter_titles, chunk_text,
            chunk_order_in_file, total_chunks_in_file, global_chunk_index,
            bm25(related_chapter_titles, ?1) as bm25_score
        FROM document_chunks
        WHERE related_chapter_titles MATCH ?1
        ORDER BY bm25_score DESC
        LIMIT ?2
        "#,
    )?;
    
    // 执行查询并返回结果
    // ...
}
```

#### 混合搜索策略
```rust
/// 混合搜索：向量搜索 + BM25 章节搜索
pub fn hybrid_search(&self, query: &str, embedding: &[f32], limit: usize) -> Result<Vec<SearchResult>> {
    // 1. 向量搜索获取语义相关的分块
    let vector_results = self.search_similar_chunks(embedding, limit / 2)?;
    
    // 2. BM25 搜索获取章节标题匹配的分块
    let bm25_results = self.bm25_search_chapters(query, limit / 2)?;
    
    // 3. 合并和去重结果
    let mut combined_results = Vec::new();
    // ... 合并逻辑
    
    Ok(combined_results)
}
```

### 🔄 TODO 5.2：优化 ragToc 工具
**文件**: `ragToc.ts`

#### 智能章节匹配
```typescript
export const ragTocTool = tool({
  description: `
根据章节标题获取指定章节的所有分块内容。
支持多种匹配策略：
1. 精确匹配：完全匹配章节标题
2. 模糊匹配：部分匹配章节标题
3. BM25搜索：基于相关性的章节搜索
`,
  parameters: z.object({
    chapterTitle: z.string().describe("章节标题，支持精确匹配和模糊匹配"),
    searchStrategy: z.enum(["exact", "fuzzy", "bm25"]).optional().describe("搜索策略，默认为智能选择"),
    reasoning: z.string().optional().describe("调用此工具的原因"),
  }),
});
```

#### 多策略搜索实现
```typescript
async function searchChapterContent(chapterTitle: string, strategy?: string) {
  // 1. 首先尝试精确匹配
  let chunks = await invoke<DocumentChunk[]>("get_toc_chunks", {
    bookId: currentBookId,
    chapterTitle: chapterTitle,
    searchType: "exact"
  });
  
  if (chunks.length > 0) {
    return { chunks, strategy: "exact" };
  }
  
  // 2. 如果精确匹配失败，尝试模糊匹配
  chunks = await invoke<DocumentChunk[]>("get_toc_chunks", {
    bookId: currentBookId,
    chapterTitle: chapterTitle,
    searchType: "fuzzy"
  });
  
  if (chunks.length > 0) {
    return { chunks, strategy: "fuzzy" };
  }
  
  // 3. 最后尝试 BM25 搜索
  chunks = await invoke<DocumentChunk[]>("search_chapters_bm25", {
    bookId: currentBookId,
    query: chapterTitle,
    limit: 50
  });
  
  return { chunks, strategy: "bm25" };
}
```

### 🔄 TODO 5.3：增强 ragSearch 工具
**文件**: `ragSearch.ts`

#### 文件级别聚合
```typescript
// 增强搜索结果，支持文件级别的聚合显示
const enhancedContext = results.map((r, idx) => {
  // 解析章节标题
  const chapterTitles = r.related_chapter_titles.split('|').map(t => t.trim());
  const primaryChapter = chapterTitles[0] || "未知章节";
  const additionalChapters = chapterTitles.slice(1);
  
  return {
    rank: idx + 1,
    primary_chapter: primaryChapter,
    additional_chapters: additionalChapters,
    similarity: Number.parseFloat((r.similarity * 100).toFixed(1)),
    content: r.content,
    
    position: {
      chunk_id: r.chunk_id,
      md_file_path: r.md_file_path,
      file_order_in_book: r.file_order_in_book,
      global_index: r.global_chunk_index,
      file_position: `${r.chunk_order_in_file + 1}/${r.total_chunks_in_file}`,
    },
    
    // 文件级别的上下文信息
    file_context: {
      total_chunks: r.total_chunks_in_file,
      current_chunk: r.chunk_order_in_file + 1,
      completion_rate: ((r.chunk_order_in_file + 1) / r.total_chunks_in_file * 100).toFixed(1)
    }
  };
});
```

#### 智能引用生成
```typescript
// 生成更智能的引用信息
const citations = enhancedContext.map((item, idx) => {
  const chapterInfo = item.additional_chapters.length > 0 
    ? `${item.primary_chapter} (+${item.additional_chapters.length}个相关章节)`
    : item.primary_chapter;
    
  return {
    id: idx + 1,
    source: `${chapterInfo} - 相似度${item.similarity}%`,
    file_path: item.position.md_file_path,
    position: `文件第${item.position.file_position}块`,
    completion: `${item.file_context.completion_rate}%`,
    preview: item.content.slice(0, 100) + (item.content.length > 100 ? "..." : ""),
  };
});
```

### 🔄 TODO 5.4：新增文件级别工具
**文件**: `ragFile.ts`

#### 新工具：ragFile
```typescript
export const ragFileTool = tool({
  description: `
根据文件路径获取整个 Markdown 文件的所有分块内容。
适用于需要获取完整文件内容的场景。
`,
  parameters: z.object({
    filePath: z.string().describe("Markdown 文件路径，如 'text/part001.md'"),
    includeMetadata: z.boolean().optional().describe("是否包含文件元数据信息"),
    reasoning: z.string().optional().describe("调用此工具的原因"),
  }),
});

// 实现逻辑
async function execute(args: { filePath: string; includeMetadata?: boolean; reasoning?: string }) {
  const chunks = await invoke<DocumentChunk[]>("get_file_chunks", {
    bookId: currentBookId,
    filePath: args.filePath,
  });
  
  if (chunks.length === 0) {
    return `未找到文件 "${args.filePath}" 的内容。`;
  }
  
  // 按 chunk_order_in_file 排序
  chunks.sort((a, b) => a.chunk_order_in_file - b.chunk_order_in_file);
  
  const fileInfo = {
    path: args.filePath,
    order_in_book: chunks[0].file_order_in_book,
    total_chunks: chunks[0].total_chunks_in_file,
    related_chapters: chunks[0].related_chapter_titles.split('|').map(t => t.trim()),
  };
  
  // 格式化输出
  let result = [];
  
  if (args.includeMetadata) {
    result.push(`📁 文件信息：${fileInfo.path}`);
    result.push(`📖 书中顺序：第 ${fileInfo.order_in_book} 个文件`);
    result.push(`📄 总分块数：${fileInfo.total_chunks} 块`);
    result.push(`📚 相关章节：${fileInfo.related_chapters.join(' | ')}`);
    result.push('');
  }
  
  chunks.forEach((chunk, index) => {
    result.push(`【第 ${index + 1}/${fileInfo.total_chunks} 块】`);
    result.push(chunk.chunk_text);
    result.push('---');
  });
  
  return result.join('\n');
}
```

### 🔄 TODO 5.5：优化工具选择策略
**文件**: `tools/index.ts`

#### 智能工具推荐
```typescript
// 根据查询内容智能推荐工具
export function recommendTool(query: string): string[] {
  const recommendations = [];
  
  // 章节相关查询
  if (/第?\d+章|章节|目录/.test(query)) {
    recommendations.push("ragToc");
  }
  
  // 文件相关查询
  if (/文件|\.md|part\d+/.test(query)) {
    recommendations.push("ragFile");
  }
  
  // 内容搜索查询
  if (/搜索|查找|相关/.test(query)) {
    recommendations.push("ragSearch");
  }
  
  // 上下文查询
  if (/前后|上下文|周围/.test(query)) {
    recommendations.push("ragContext", "ragRange");
  }
  
  return recommendations;
}
```

#### 工具组合策略
```typescript
// 定义常用的工具组合
export const toolCombinations = {
  // 深度章节分析
  chapterAnalysis: ["ragToc", "ragSearch", "ragContext"],
  
  // 文件级别分析
  fileAnalysis: ["ragFile", "ragSearch"],
  
  // 内容发现
  contentDiscovery: ["ragSearch", "ragToc"],
  
  // 精确定位
  preciseLocation: ["ragRange", "ragContext"],
};
```

## 📊 性能优化

### BM25 索引优化
```sql
-- 为 BM25 搜索创建专门的虚拟表
CREATE VIRTUAL TABLE IF NOT EXISTS chapter_fts USING fts5(
    related_chapter_titles,
    content=document_chunks,
    content_rowid=id
);

-- 触发器保持 FTS 表同步
CREATE TRIGGER IF NOT EXISTS chapter_fts_insert AFTER INSERT ON document_chunks BEGIN
    INSERT INTO chapter_fts(rowid, related_chapter_titles) VALUES (new.id, new.related_chapter_titles);
END;
```

### 缓存策略
```typescript
// 实现工具结果缓存
class ToolResultCache {
  private cache = new Map<string, { result: any; timestamp: number }>();
  private readonly TTL = 5 * 60 * 1000; // 5分钟
  
  get(key: string) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.TTL) {
      return cached.result;
    }
    this.cache.delete(key);
    return null;
  }
  
  set(key: string, result: any) {
    this.cache.set(key, { result, timestamp: Date.now() });
  }
}
```

## 🎉 阶段五成果

### 计划完成的任务
- 🔄 集成 BM25 章节搜索算法
- 🔄 优化 ragToc 工具的智能匹配
- 🔄 增强 ragSearch 工具的文件级别聚合
- 🔄 新增 ragFile 工具支持文件级别查询
- 🔄 实现智能工具选择和组合策略

### 预期改进
- **搜索精度**: BM25 算法提升章节搜索的相关性
- **工具智能**: 多策略搜索提供更好的用户体验
- **信息聚合**: 文件级别的信息聚合提供更完整的上下文
- **性能优化**: 缓存和索引优化提升响应速度

### 技术优势
- **算法先进**: 结合向量搜索和 BM25 的混合搜索
- **策略灵活**: 多种搜索策略自动选择最佳结果
- **信息丰富**: 提供文件级别和章节级别的完整信息
- **用户友好**: 智能工具推荐简化用户操作

**注意**: 阶段五为计划阶段，具体实现需要根据前四个阶段的完成情况和用户需求进行调整。
