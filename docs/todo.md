# TODO

面向近期改进与收敛的任务清单（按优先级大致排序）。后续可勾选并细化为 Issue/PR。

## 向量与嵌入服务
- [ ] 后端化自动启动 Llama 服务器：在 Tauri setup 中拉起，失败重试与错误事件上报
- [ ] 前端通过 Tauri 事件接收端口与状态，统一写入 `llamaStore`
- [ ] 向量化请求支持批量输入（`/v1/embeddings` 一次传多条）以优化吞吐
- [ ] 动态维度检测：以首条嵌入结果的长度为准，防止维度不一致
- [ ] 将所有 1024 硬编码维度点位配置化（或由模型元数据/服务器返回）

## EPUB → mdBook → 向量化流水线
- [ ] 在 `pipeline.rs` 中将 `chunk_text` 切换为 `chunk_md_file`，提升分片质量
- [ ] TOC 解析新增 fallback：优先 `toc.ncx`，其次 `toc.xhtml`，再次 `SUMMARY.md`
- [ ] 跳过重复 mdBook 转换：按时间戳或哈希判断是否需要重新转换
- [ ] 记录/保留锚点信息（必要时扩展 `FlatTocNode` 字段），便于前端定位

### 分片与定位精度（已做/待做）
- [x] 去重策略：按 `md_src` 分组，同一 Markdown 文件只分片一次，避免为每个 TOC 节点重复分片
- [x] 现行定位：
  - 标题匹配 + 等分估计：
    - 先在全文中顺序查找 TOC 标题，命中则记为该节点起点；
    - 未命中则在全文长度上等分估计起点；
    - 将全文切成 [start, next_start) 区间，按分片序号等长估计的“片起点”归属到对应区间（仅一次入库）。
- [ ] 精度升级（基于真实偏移）：
  - 在 `chunk_md_file` 中返回每个分片的源文档偏移 `[start,end]`；
  - `pipeline` 以 offset 与 TOC 区间做交集归属，彻底消除“等长估计”的误差。
- [ ] 锚点优先：
  - `FlatTocNode` 已保留 `anchor` 字段；
  - 在 MD 中解析/生成 heading id，优先使用锚点定位 TOC 起点；
  - 标题匹配仅作兜底；完全缺失时再做等分估计。
- [ ] 标题匹配鲁棒化：
  - 统一全/半角、空白、标点、大小写再匹配；
  - 支持模糊匹配（trigram/编辑距离）+ heading 就近优先；
  - 对重复标题，采用“就近上一命中位置”的单调递增选择策略。
- [ ] 重叠与大小控制：
  - 维持约 10–20% overlap；对超长块引入二次细分或自适应 overlap；
  - 将向量器字符上限与分片上限对齐，避免向量侧截断（已将嵌入上限提升为 2000，可按需要调整）。
- [ ] 入库去重保护：
  - 入库前可对 `chunk_text` 计算 hash（例如 SHA1）做进程内去重，兜底任何异常重复。
- [ ] 指标与日志：
  - 记录每本书的标题命中率、锚点命中率、估计覆盖率、每个 `toc_id` 的分片数量分布；
  - 采样打印“区间边界 + 相邻片内容片段”，便于人工校验。
- [ ] 测试用例：
  - 同一文件被多个 TOC 指向（含/不含 anchor）；
  - 标题重复/极短标题/标题变体（空格/标点/全半角）；
  - 锚点可解析/不可解析两类；
  - 断言：每段文本只入库一次；ragToc 返回的片集合与期望区间一致。

## 数据库与一致性
- [ ] 将删除书籍改为事务：删除文件失败/数据库失败时回滚或给出一致化策略
- [ ] 在连接层启用 SQLite 外键（`PRAGMA foreign_keys = ON`），并保留手动删除兜底
- [ ] 检查/补充必要索引的覆盖率与查询计划（上下文/范围/TOC 查询）

## API 与前端集成
- [ ] 统一向量相关入口的 `baseUrl` 来源：优先 `llamaStore.currentSession.port`，可覆盖
- [ ] 暴露嵌入服务器健康检查 API（front 调用，显示状态/版本/模型）
- [ ] Llama 模型路径的跨平台配置：首启引导、持久化与验证

## 健壮性与测试
- [ ] URL 百分号编码解码：已替换为 UTF-8 解码，补充包含中文/特殊字符的单测
- [ ] 增加包含 `,`、空格、符号的 TOC/路径的回归用例
- [ ] 大文件/长章节的分片边界回归测试（标题、代码块、列表）

## 文档与开发体验
- [ ] 更新开发文档：端口从硬编码改为动态，会话获取方式与调试方法
- [ ] 示例脚本：一键索引一本书并做检索（含本地服务器启动）

---

补充说明：本文件为任务占位，后续根据优先级细化为具体实现计划与验收标准。
