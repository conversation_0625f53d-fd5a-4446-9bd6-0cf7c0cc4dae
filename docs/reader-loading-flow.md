# Reader 加载书籍和配置逻辑分析

## 概述

Reader 模块负责书籍的阅读界面，包括书籍加载、配置管理、视图状态等功能。本文档总结了 Reader 加载书籍和配置的完整流程。

## 主要组件架构

```
pages/reader/page.tsx
├── components/Reader.tsx (主容器)
    ├── components/ReaderContent.tsx (内容管理)
    │   ├── components/BooksGrid.tsx (书籍网格显示)
    │   ├── components/FoliateViewer.tsx (单个书籍视图)
    │   └── components/notebook/Notebook.tsx (笔记本)
    └── components/chat/chat-sidebar.tsx (AI对话)
```

## 加载流程

### 1. 页面初始化 (Reader.tsx)

**位置**: `packages/app/src/pages/reader/components/Reader.tsx`

#### 1.1 基础初始化
```typescript
// 主题设置和屏幕唤醒锁
useTheme({ systemUIVisible: settings.alwaysShowStatusBar, appThemeColor: "base-100" });
useScreenWakeLock(settings.screenWakeLock);

// 字体和国际化初始化
useEffect(() => {
    mountAdditionalFonts(document);
    initDayjs(getLocale());
}, []);
```

#### 1.2 库和配置加载
```typescript
const initLibrary = async () => {
    try {
        // 1. 获取 AppService 实例
        const appService = await envConfig.getAppService();
        
        // 2. 加载系统设置
        const settings = await appService.loadSettings();
        setSettings(settings);

        // 3. 获取书籍列表 (新API)
        const booksWithStatus = await getBooksWithStatus();
        
        // 4. 转换为旧格式以保持兼容性
        const convertedBooks = await Promise.all(
            booksWithStatus.map(convertBookWithStatusToBook)
        );
        
        // 5. 更新库存状态
        setLibrary(convertedBooks);
        setLibraryLoaded(true);
    } catch (error) {
        console.error("Error loading library for reader:", error);
        setLibraryLoaded(true);
    }
};
```

**关键类型转换**: `BookWithStatus` → `Book`
- 添加文件URL转换 (`convertFileSrc`)
- 保持向后兼容性
- 处理封面路径转换

### 2. 书籍内容初始化 (ReaderContent.tsx)

**位置**: `packages/app/src/pages/reader/components/ReaderContent.tsx`

#### 2.1 URL参数解析
```typescript
const bookIds = ids || searchParams?.get("ids") || "";
const initialIds = bookIds.split(BOOK_IDS_SEPARATOR).filter(Boolean);
const initialBookKeys = initialIds.map((id) => `${id}`);
```

#### 2.2 书籍视图状态初始化
```typescript
initialBookKeys.forEach((key, index) => {
    const id = key.split("-")[0]!;
    const isPrimary = !uniqueIds.has(id);
    uniqueIds.add(id);
    
    if (!getViewState(key)) {
        // 异步初始化每个书籍的视图状态
        initViewState(envConfig, id, key, isPrimary).catch((error) => {
            console.log("Error initializing book", key, error);
        });
        
        // 设置第一个书籍为侧边栏显示的书籍
        if (index === 0) setSideBarBookKey(key);
    }
});
```

### 3. 视图状态管理 (readerStore)

**位置**: `packages/app/src/store/readerStore.ts`

#### 3.1 状态结构
```typescript
interface ViewState {
    key: string;           // 唯一标识符
    view: FoliateView | null;   // Foliate视图实例
    isPrimary: boolean;    // 是否为主要视图
    loading: boolean;      // 加载状态
    error: string | null;  // 错误信息
    progress: BookProgress | null;  // 阅读进度
    ribbonVisible: boolean;     // 书签可见性
    ttsEnabled: boolean;        // TTS启用状态
    gridInsets: Insets | null;  // 网格间距
    viewSettings: ViewSettings | null;  // 视图设置
}
```

#### 3.2 视图状态初始化流程
```typescript
initViewState: async (envConfig, id, key, isPrimary = true) => {
    // 1. 获取书籍数据存储
    const booksData = useBookDataStore.getState().booksData;
    
    // 2. 检查是否已加载
    if (!booksData[id]) {
        // 3. 加载书籍内容和配置
        const { book, file, config } = await appService.loadBookContent(
            library.find(b => b.id === id)!, 
            settings
        );
        
        // 4. 解析书籍文档
        const bookDoc = new DocumentLoader().load(file, book);
        await bookDoc.load();
        
        // 5. 更新元数据
        book.sourceTitle = formatTitle(bookDoc.metadata.title);
        book.primaryLanguage = book.primaryLanguage ?? getPrimaryLanguage(bookDoc.metadata.language);
        book.metadata = book.metadata ?? bookDoc.metadata;
        
        // 6. 保存到书籍数据存储
        useBookDataStore.setState({
            booksData: {
                ...state.booksData,
                [id]: { id, book, file, config, bookDoc }
            }
        });
    }
    
    // 7. 合并视图设置 (全局设置 < 书籍配置 < 视图设置)
    const config = booksData[id]?.config as BookConfig;
    const configViewSettings = config.viewSettings!;
    const globalViewSettings = settings.globalViewSettings;
    
    // 8. 创建视图状态
    set(state => ({
        viewStates: {
            ...state.viewStates,
            [key]: {
                key, view: null, isPrimary, loading: false, error: null,
                progress: null, ribbonVisible: false, ttsEnabled: false,
                gridInsets: null,
                viewSettings: { ...globalViewSettings, ...configViewSettings }
            }
        }
    }));
}
```

### 4. 书籍配置加载 (appService)

**位置**: `packages/app/src/services/appService.ts`

#### 4.1 配置文件结构
- **位置**: `Books/{bookHash}.config`
- **格式**: JSON序列化
- **内容**: 视图设置、搜索配置、阅读进度等

#### 4.2 配置加载流程
```typescript
async loadBookConfig(book: Book, settings: SystemSettings): Promise<BookConfig> {
    const { globalViewSettings } = settings;
    try {
        let str = "{}";
        // 1. 检查配置文件是否存在
        if (await this.fs.exists(getConfigFilename(book), "Books")) {
            // 2. 读取配置文件
            str = await this.fs.readFile(getConfigFilename(book), "Books", "text");
        }
        // 3. 反序列化配置，合并默认设置
        return deserializeConfig(str, globalViewSettings, DEFAULT_BOOK_SEARCH_CONFIG);
    } catch {
        // 4. 失败时返回默认配置
        return deserializeConfig("{}", globalViewSettings, DEFAULT_BOOK_SEARCH_CONFIG);
    }
}
```

#### 4.3 书籍内容加载
```typescript
async loadBookContent(book: Book, settings: SystemSettings): Promise<BookContent> {
    // 1. 获取书籍文件
    const file = await this.fs.getFile(getLocalBookFilename(book), "Books");
    
    // 2. 加载配置
    const config = await this.loadBookConfig(book, settings);
    
    return { book, file, config };
}
```

## 状态管理架构

### Store 职责分工

#### 1. `libraryStore` - 书籍库管理
- 书籍列表存储 (`library: Book[]`)
- 新版书籍状态 (`booksWithStatus: BookWithStatusAndUrls[]`)
- 搜索功能
- 书籍刷新逻辑

#### 2. `readerStore` - 阅读状态管理
- 视图状态集合 (`viewStates: { [key: string]: ViewState }`)
- 当前打开的书籍键列表 (`bookKeys: string[]`)
- 视图初始化和清理
- 阅读进度管理

#### 3. `bookDataStore` - 书籍数据缓存
- 书籍内容缓存 (`booksData: { [id: string]: BookData }`)
- 书籍文档对象
- 配置数据

#### 4. `bookSettingsStore` - 书籍设置
- 系统全局设置
- 用户偏好设置

## 错误处理机制

### 1. 书籍加载失败
```typescript
try {
    // 加载逻辑
} catch (error) {
    set(state => ({
        viewStates: {
            ...state.viewStates,
            [key]: {
                ...defaultViewState,
                error: "Failed to load book."
            }
        }
    }));
}
```

### 2. 配置文件损坏
- 自动使用默认配置
- 不中断加载流程
- 记录错误日志

## 性能优化

### 1. 懒加载
- 书籍内容按需加载
- 视图状态延迟初始化

### 2. 缓存机制
- `bookDataStore` 缓存已加载的书籍数据
- 避免重复解析和加载

### 3. 异步处理
- 并行加载多个书籍
- 非阻塞的初始化流程

## 书籍状态保存机制

### 双重存储架构

书籍状态采用了**双重存储策略**，结合了文件系统和数据库存储：

#### 1. Tauri 后端数据库存储 (主要状态)
```rust
// 位置：src-tauri/src/core/books/
// 数据库：SQLite
```

**存储内容**：
- ✅ 书籍基本信息 (标题、作者、格式等)
- ✅ 阅读状态 (unread/reading/completed) 
- ✅ 阅读进度 (当前页/总页数)
- ✅ 阅读时间统计
- ✅ 最后阅读位置
- ✅ 开始/完成时间戳

**API调用**：
```typescript
// 更新书籍状态
await invoke<BookStatus>("update_book_status", { bookId, updateData });

// 获取书籍状态  
await invoke<BookWithStatus[]>("get_books_with_status", { options });
```

#### 2. 本地文件系统存储 (详细配置)
```
Books/
├── {bookHash}.epub          # 书籍文件
├── {bookHash}.config        # 阅读配置文件
├── {bookHash}_cover.jpg     # 封面图片  
└── library.json            # 书籍库索引
```

**配置文件内容** (`{bookHash}.config`):
```json
{
  "viewSettings": {
    "fontSize": 16,
    "fontFamily": "serif", 
    "lineHeight": 1.6,
    "theme": "light"
  },
  "progress": [45, 200],     // [当前页, 总页数]
  "location": "epubcfi(/6/8[chapter1]!/4)",  // 精确位置
  "searchConfig": {...},
  "updatedAt": 1704067200000
}
```

#### 3. 内存状态管理 (运行时)
```typescript
// Zustand Stores
├── readerStore      # 视图状态、阅读进度
├── bookDataStore    # 书籍内容缓存、配置
├── libraryStore     # 书籍列表、搜索状态
└── bookSettingsStore # 全局阅读设置
```

### 保存触发机制

#### 自动保存 (10秒节流)
```typescript
// useProgressAutoSave.ts
const saveBookConfig = throttle(async () => {
  await saveConfig(envConfig, bookKey, config, settings);
}, 10000);
```

#### 手动保存触发点
- 📖 翻页时更新进度
- 🔖 添加书签时
- ⚙️ 修改阅读设置时  
- 🚪 关闭书籍时
- 📱 应用退出时

### 数据流向

```
用户操作 → Store状态更新 → 节流保存 → 双重持久化
                    ↓
    ┌─────────────────────────────┐
    │  readerStore (内存状态)      │ 
    └─────────────────────────────┘
              ↓ (10秒后)
    ┌─────────────────────────────┐
    │  文件系统 + Tauri数据库      │
    │  .config文件 + SQLite       │
    └─────────────────────────────┘
```

### 与 localStorage 的区别

❌ **不使用 localStorage**，原因：
- 📁 跨平台文件系统更可靠
- 🗄️ SQLite 数据库性能更好  
- 🔄 支持复杂查询和关联
- 📊 便于数据迁移和备份
- 🔐 更好的数据隔离

### 存储优势

- ✅ **持久性强**：文件系统 + 数据库双保险
- ✅ **性能优化**：内存缓存 + 节流写入
- ✅ **数据完整**：元数据和详细配置分离存储
- ✅ **跨平台**：Tauri统一文件系统API

## 总结

Reader 的加载流程体现了分层架构的设计思想：
1. **表示层** - React组件负责UI渲染和用户交互
2. **状态层** - Zustand stores 管理应用状态
3. **服务层** - AppService 处理文件系统和配置
4. **数据层** - 书籍解析和元数据提取
5. **存储层** - 双重存储确保数据可靠性

这种架构确保了代码的可维护性、扩展性和性能，同时保持了数据的持久化和可靠性。
